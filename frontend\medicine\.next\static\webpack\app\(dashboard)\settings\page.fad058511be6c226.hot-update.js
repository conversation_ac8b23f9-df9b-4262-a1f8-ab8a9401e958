"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHome.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSettings.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCalendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconEdit.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconX.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst AccueilEtAgenda = ()=>{\n    _s();\n    // États pour les onglets\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    // États pour les modales\n    const [motifModalOpened, setMotifModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [agendaModalOpened, setAgendaModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [salleModalOpened, setSalleModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [evenementModalOpened, setEvenementModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMotif, setEditingMotif] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingAgenda, setEditingAgenda] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSalle, setEditingSalle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingEvenement, setEditingEvenement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour les formulaires\n    const [motifForm, setMotifForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: '',\n        description: '',\n        duration: 15,\n        color: '#9b4d93',\n        colorRayee: '#792485',\n        agendaDefaut: '',\n        servicesDesignes: '',\n        couleurSombre: false\n    });\n    const [agendaForm, setAgendaForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        servicesDesignes: '',\n        isResource: false\n    });\n    const [salleForm, setSalleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        type: 'attente',\n        capacity: 1,\n        servicesDesignes: ''\n    });\n    const [evenementForm, setEvenementForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        dateDebut: '',\n        dateFin: '',\n        color: '#9b4d93',\n        indisponible: false,\n        permanent: false,\n        touteJournee: false,\n        tousLesJours: false,\n        cabinet: false\n    });\n    // États pour les paramètres généraux\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        heureOuverture: '08:00',\n        heureFermeture: '20:00',\n        premierJourSemaine: 'Lundi',\n        vueDefautCalendrier: 'Semaine',\n        minimumIntervalle: '15 minutes',\n        fuseauHoraire: 'Africa/Casablanca',\n        cacherJoursFermeture: false,\n        curseurAgendaHeureCurrent: true,\n        filtre24hCabinet: true,\n        utiliserAbbreviation: false,\n        utiliserListePatients: false,\n        afficherSeulementVisitesTerminees: false\n    });\n    // Données de test pour les motifs\n    const [motifs, setMotifs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            code: 'CNS',\n            description: 'Consultation',\n            duration: 15,\n            color: '#9b4d93',\n            colorRayee: '#792485'\n        },\n        {\n            id: 2,\n            code: 'CTR',\n            description: 'Contrôle',\n            duration: 15,\n            color: '#c05889',\n            colorRayee: '#7d1868'\n        },\n        {\n            id: 3,\n            code: 'CHI',\n            description: 'Chirurgie/Paro',\n            duration: 30,\n            color: '#c14317',\n            colorRayee: '#922a12'\n        },\n        {\n            id: 4,\n            code: 'COM',\n            description: 'Composite',\n            duration: 15,\n            color: '#2836cd',\n            colorRayee: '#091486'\n        },\n        {\n            id: 5,\n            code: 'DS',\n            description: 'Dépose Sutures',\n            duration: 15,\n            color: '#2e7d32',\n            colorRayee: '#1b5e20'\n        },\n        {\n            id: 6,\n            code: 'CS',\n            description: '1er Consultation',\n            duration: 15,\n            color: '#e91e63',\n            colorRayee: '#c2185b'\n        },\n        {\n            id: 7,\n            code: 'DEV',\n            description: 'Devis',\n            duration: 15,\n            color: 'rgb(23, 160, 131)',\n            colorRayee: '#024f3f'\n        },\n        {\n            id: 8,\n            code: 'END',\n            description: 'Endodontie',\n            duration: 60,\n            color: '#a08523',\n            colorRayee: '#564507'\n        },\n        {\n            id: 9,\n            code: 'FOR',\n            description: 'Formation',\n            duration: 60,\n            color: '#982552',\n            colorRayee: '#72183c'\n        },\n        {\n            id: 10,\n            code: 'IMP',\n            description: 'Implantologie',\n            duration: 60,\n            color: '#388983',\n            colorRayee: '#26645e'\n        },\n        {\n            id: 11,\n            code: 'ORT',\n            description: 'Orthodontie',\n            duration: 30,\n            color: '#464098',\n            colorRayee: '#1a7c81'\n        },\n        {\n            id: 12,\n            code: 'CM',\n            description: 'FS Chassis/Monta',\n            duration: 15,\n            color: '#6c407a',\n            colorRayee: '#9c4168'\n        },\n        {\n            id: 13,\n            code: 'POS',\n            description: 'PA Pose',\n            duration: 15,\n            color: '#b25953',\n            colorRayee: '#9c3e3e'\n        },\n        {\n            id: 14,\n            code: 'EMP',\n            description: 'PC TP EMP',\n            duration: 30,\n            color: '#4c8d57',\n            colorRayee: '#366e42'\n        },\n        {\n            id: 15,\n            code: 'ARM',\n            description: 'PC ESS Armature',\n            duration: 30,\n            color: '#3291ef',\n            colorRayee: '#0d667b'\n        },\n        {\n            id: 16,\n            code: 'SCE',\n            description: 'PC Scellement',\n            duration: 15,\n            color: '#c2185b',\n            colorRayee: '#880e4f'\n        },\n        {\n            id: 17,\n            code: 'PRO',\n            description: 'Prophylaxie',\n            duration: 15,\n            color: '#009688',\n            colorRayee: '#00695c'\n        }\n    ]);\n    // Données de test pour les agendas\n    const [agendas, setAgendas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'Domicile',\n            description: '',\n            color: '#f4511e',\n            isResource: false\n        },\n        {\n            id: 2,\n            name: 'Clinique',\n            description: '',\n            color: '#43a047',\n            isResource: false\n        },\n        {\n            id: 3,\n            name: 'Cabinet',\n            description: '',\n            color: '#0097a7',\n            isResource: false\n        }\n    ]);\n    // Données de test pour les docteurs (Code couleurs médecine)\n    const [docteurs, setDocteurs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'Docteur',\n            color: '#d50000'\n        }\n    ]);\n    // Données de test pour les salles\n    const [salles, setSalles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'SLT',\n            description: 'Salle d\\'attente',\n            color: '#039be5',\n            type: 'attente',\n            capacity: 30\n        },\n        {\n            id: 2,\n            name: 'FTL',\n            description: 'Fauteuil 1',\n            color: '#ff5722',\n            type: 'consultation',\n            capacity: 1\n        },\n        {\n            id: 3,\n            name: 'FT 1',\n            description: '',\n            color: '#000000',\n            type: 'consultation',\n            capacity: 1\n        },\n        {\n            id: 4,\n            name: 'SALLE DE CONSULTATION 2',\n            description: '',\n            color: '#000000',\n            type: 'consultation',\n            capacity: 1\n        }\n    ]);\n    // Données de test pour les événements\n    const [evenements, setEvenements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: 'CONGRES',\n            medecins: 'DEMO DEMO',\n            dateDebut: '21/04/2025',\n            dateFin: '23/04/2025',\n            color: '#f52dbe',\n            indisponible: true,\n            permanent: false,\n            touteJournee: true,\n            tousLesJours: false,\n            cabinet: false\n        }\n    ]);\n    // États pour la recherche\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Filtrer les motifs selon la recherche\n    const filteredMotifs = motifs.filter((motif)=>motif.code.toLowerCase().includes(searchTerm.toLowerCase()) || motif.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    // Jours de la semaine\n    const joursSemaine = [\n        'Lundi',\n        'Mardi',\n        'Mercredi',\n        'Jeudi',\n        'Vendredi',\n        'Samedi',\n        'Dimanche'\n    ];\n    // Options pour les selects\n    const vueCalendrierOptions = [\n        {\n            value: 'Semaine',\n            label: 'Semaine'\n        },\n        {\n            value: 'Mois',\n            label: 'Mois'\n        },\n        {\n            value: 'Jour',\n            label: 'Jour'\n        }\n    ];\n    const intervalleOptions = [\n        {\n            value: '5 minutes',\n            label: '5 minutes'\n        },\n        {\n            value: '10 minutes',\n            label: '10 minutes'\n        },\n        {\n            value: '15 minutes',\n            label: '15 minutes'\n        },\n        {\n            value: '30 minutes',\n            label: '30 minutes'\n        }\n    ];\n    const fuseauHoraireOptions = [\n        {\n            value: 'Africa/Casablanca',\n            label: 'Africa/Casablanca'\n        },\n        {\n            value: 'Europe/Paris',\n            label: 'Europe/Paris'\n        },\n        {\n            value: 'UTC',\n            label: 'UTC'\n        }\n    ];\n    const agendaDefautOptions = [\n        {\n            value: 'agenda1',\n            label: 'Agenda par défaut'\n        },\n        {\n            value: 'agenda2',\n            label: 'Agenda 2'\n        }\n    ];\n    const servicesDesignesOptions = [\n        {\n            value: 'service1',\n            label: 'Services désignés'\n        },\n        {\n            value: 'service2',\n            label: 'Service 2'\n        }\n    ];\n    // Fonctions pour gérer les modales\n    const openMotifModal = (motif)=>{\n        if (motif) {\n            setEditingMotif(motif);\n            setMotifForm({\n                code: motif.code,\n                description: motif.description,\n                duration: motif.duration,\n                color: motif.color,\n                colorRayee: motif.colorRayee,\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        } else {\n            setEditingMotif(null);\n            setMotifForm({\n                code: '',\n                description: '',\n                duration: 15,\n                color: '#9b4d93',\n                colorRayee: '#792485',\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        }\n        setMotifModalOpened(true);\n    };\n    const openAgendaModal = (agenda)=>{\n        if (agenda) {\n            setEditingAgenda(agenda);\n            setAgendaForm({\n                name: agenda.name,\n                description: agenda.description,\n                color: agenda.color,\n                servicesDesignes: '',\n                isResource: agenda.isResource\n            });\n        } else {\n            setEditingAgenda(null);\n            setAgendaForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                servicesDesignes: '',\n                isResource: false\n            });\n        }\n        setAgendaModalOpened(true);\n    };\n    const openSalleModal = (salle)=>{\n        if (salle) {\n            setEditingSalle(salle);\n            setSalleForm({\n                name: salle.name,\n                description: salle.description,\n                color: salle.color,\n                type: salle.type,\n                capacity: salle.capacity,\n                servicesDesignes: ''\n            });\n        } else {\n            setEditingSalle(null);\n            setSalleForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                type: 'attente',\n                capacity: 1,\n                servicesDesignes: ''\n            });\n        }\n        setSalleModalOpened(true);\n    };\n    const openEvenementModal = (evenement)=>{\n        if (evenement) {\n            setEditingEvenement(evenement);\n            setEvenementForm({\n                title: evenement.title,\n                dateDebut: evenement.dateDebut,\n                dateFin: evenement.dateFin,\n                color: evenement.color,\n                indisponible: evenement.indisponible,\n                permanent: evenement.permanent,\n                touteJournee: evenement.touteJournee,\n                tousLesJours: evenement.tousLesJours,\n                cabinet: evenement.cabinet\n            });\n        } else {\n            setEditingEvenement(null);\n            setEvenementForm({\n                title: '',\n                dateDebut: '',\n                dateFin: '',\n                color: '#9b4d93',\n                indisponible: false,\n                permanent: false,\n                touteJournee: false,\n                tousLesJours: false,\n                cabinet: false\n            });\n        }\n        setEvenementModalOpened(true);\n    };\n    // Fonctions pour sauvegarder\n    const handleSaveMotif = ()=>{\n        if (editingMotif) {\n            // Modifier un motif existant\n            setMotifs((prev)=>prev.map((m)=>m.id === editingMotif.id ? {\n                        ...m,\n                        ...motifForm\n                    } : m));\n        } else {\n            // Ajouter un nouveau motif\n            const newMotif = {\n                id: Date.now(),\n                code: motifForm.code,\n                description: motifForm.description,\n                duration: motifForm.duration,\n                color: motifForm.color,\n                colorRayee: motifForm.colorRayee\n            };\n            setMotifs((prev)=>[\n                    ...prev,\n                    newMotif\n                ]);\n        }\n        setMotifModalOpened(false);\n    };\n    const handleSaveAgenda = ()=>{\n        if (editingAgenda) {\n            // Modifier un agenda existant\n            setAgendas((prev)=>prev.map((a)=>a.id === editingAgenda.id ? {\n                        ...a,\n                        ...agendaForm\n                    } : a));\n        } else {\n            // Ajouter un nouvel agenda\n            const newAgenda = {\n                id: Date.now(),\n                name: agendaForm.name,\n                description: agendaForm.description,\n                color: agendaForm.color,\n                isResource: agendaForm.isResource\n            };\n            setAgendas((prev)=>[\n                    ...prev,\n                    newAgenda\n                ]);\n        }\n        setAgendaModalOpened(false);\n    };\n    // Fonction pour supprimer un motif\n    const handleDeleteMotif = (id)=>{\n        setMotifs((prev)=>prev.filter((m)=>m.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        gap: \"lg\",\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                order: 2,\n                className: \"text-gray-800 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24,\n                        className: \"text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Accueil et Agenda\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                value: activeTab,\n                onChange: (value)=>setActiveTab(value || 'general'),\n                variant: \"outline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.List, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"general\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"G\\xe9n\\xe9ral\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"motifs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Motifs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"code-couleurs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 56\n                                }, void 0),\n                                children: \"Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"agendas\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"Agendas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"salles\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"evenements\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 53\n                                }, void 0),\n                                children: \"\\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"general\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                gap: \"lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Horaires du centre\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure d'ouverture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureOuverture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureOuverture: e.target.value\n                                                                        })),\n                                                                placeholder: \"08:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure de fermeture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureFermeture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureFermeture: e.target.value\n                                                                        })),\n                                                                placeholder: \"20:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                        size: \"sm\",\n                                                        fw: 500,\n                                                        mb: \"xs\",\n                                                        children: \"Jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-wrap\",\n                                                        children: joursSemaine.map((jour)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                variant: \"light\",\n                                                                color: \"blue\",\n                                                                className: \"cursor-pointer\",\n                                                                children: jour\n                                                            }, jour, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Param\\xe8tres d'affichage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Premier jour de la semaine\",\n                                                            value: generalSettings.premierJourSemaine,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        premierJourSemaine: value || 'Lundi'\n                                                                    })),\n                                                            data: joursSemaine.map((jour)=>({\n                                                                    value: jour,\n                                                                    label: jour\n                                                                }))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Vue par d\\xe9faut du calendrier\",\n                                                            value: generalSettings.vueDefautCalendrier,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        vueDefautCalendrier: value || 'Semaine'\n                                                                    })),\n                                                            data: vueCalendrierOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Minimum intervalle de temps\",\n                                                            value: generalSettings.minimumIntervalle,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        minimumIntervalle: value || '15 minutes'\n                                                                    })),\n                                                            data: intervalleOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Fuseau horaire\",\n                                                            value: generalSettings.fuseauHoraire,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        fuseauHoraire: value || 'Africa/Casablanca'\n                                                                    })),\n                                                            data: fuseauHoraireOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                                gap: \"xs\",\n                                                mt: \"md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.cacherJoursFermeture,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    cacherJoursFermeture: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Cacher les jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.curseurAgendaHeureCurrent,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    curseurAgendaHeureCurrent: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Curseur d'agenda \\xe0 l'heure current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.filtre24hCabinet,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    filtre24hCabinet: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Filtre de 24h du cabinet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserAbbreviation,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserAbbreviation: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser abr\\xe9viation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserListePatients,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserListePatients: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser la liste des patients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.afficherSeulementVisitesTerminees,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    afficherSeulementVisitesTerminees: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Afficher seulement les visites termin\\xe9es dans 'Historique journalier'\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Rappel de rendez-vous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Dur\\xe9e avant RDV\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"5\",\n                                                            data: [\n                                                                {\n                                                                    value: '5',\n                                                                    label: 'Modèle SMS'\n                                                                },\n                                                                {\n                                                                    value: '10',\n                                                                    label: '10 minutes'\n                                                                },\n                                                                {\n                                                                    value: '15',\n                                                                    label: '15 minutes'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"Mod\\xe8le Email\",\n                                                            data: [\n                                                                {\n                                                                    value: 'email1',\n                                                                    label: 'Modèle Email'\n                                                                },\n                                                                {\n                                                                    value: 'email2',\n                                                                    label: 'Email 2'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"motifs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                placeholder: \"Rechercher\",\n                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openMotifModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Ajouter nouveau motif\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Motif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Dur\\xe9e (min)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur ray\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: filteredMotifs.map((motif)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: motif.code\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.colorRayee,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.colorRayee\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openMotifModal(motif),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 696,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                onClick: ()=>handleDeleteMotif(motif.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 711,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 706,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 705,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, motif.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"code-couleurs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                    placeholder: \"Docteur\",\n                                                    data: docteurs.map((d)=>({\n                                                            value: d.id.toString(),\n                                                            label: d.name\n                                                        })),\n                                                    style: {\n                                                        width: 200\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            size: \"sm\",\n                                                            fw: 500,\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                            color: \"#d50000\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Docteur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 752,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: docteurs.map((docteur)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: docteur.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: docteur.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 764,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: docteur.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 765,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                        label: \"Supprimer\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"red\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 775,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, docteur.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"agendas\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"Agendas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openAgendaModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Nouvel agenda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Ressource\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: agendas.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                        colSpan: 5,\n                                                        className: \"text-center text-gray-500 py-8\",\n                                                        children: \"Aucun agenda trouv\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 21\n                                                }, undefined) : agendas.map((agenda)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: agenda.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: agenda.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: agenda.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 829,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: agenda.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 830,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                    color: agenda.isResource ? 'green' : 'gray',\n                                                                    children: agenda.isResource ? 'Oui' : 'Non'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 833,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                        label: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"blue\",\n                                                                            onClick: ()=>openAgendaModal(agenda),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 841,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 840,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, agenda.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 802,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 789,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"salles\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 862,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 861,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"evenements\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour \\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 870,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 869,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 868,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Modal, {\n                opened: motifModalOpened,\n                onClose: ()=>setMotifModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 882,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 880,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Code \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 35\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.code,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 891,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Description \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 900,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.description,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 889,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Dur\\xe9e (min) \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.duration.toString(),\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    duration: parseInt(e.target.value) || 15\n                                                })),\n                                        type: \"number\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 910,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 918,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur ray\\xe9e\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.colorRayee,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 908,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Agenda par d\\xe9faut\",\n                                        value: motifForm.agendaDefaut,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    agendaDefaut: value || ''\n                                                })),\n                                        data: agendaDefautOptions,\n                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 16,\n                                            className: \"cursor-pointer text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 31\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: motifForm.servicesDesignes,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 955,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 944,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_29__.Switch, {\n                                    checked: motifForm.couleurSombre,\n                                    onChange: (e)=>setMotifForm((prev)=>({\n                                                ...prev,\n                                                couleurSombre: e.currentTarget.checked\n                                            })),\n                                    label: \"Couleur sombre\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setMotifModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveMotif,\n                                            disabled: !motifForm.code || !motifForm.description,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 964,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 888,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 876,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Modal, {\n                opened: agendaModalOpened,\n                onClose: ()=>setAgendaModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 999,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Agenda\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1000,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 998,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Nom \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1010,\n                                                    columnNumber: 34\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: agendaForm.name,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1009,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: agendaForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1020,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1019,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1017,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1016,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: \"Description\",\n                                        value: agendaForm.description,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: agendaForm.servicesDesignes,\n                                        onChange: (value)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1039,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1038,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1030,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_29__.Switch, {\n                                    checked: agendaForm.isResource,\n                                    onChange: (e)=>setAgendaForm((prev)=>({\n                                                ...prev,\n                                                isResource: e.currentTarget.checked\n                                            })),\n                                    label: \"Afficher comme ressource\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1049,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setAgendaModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveAgenda,\n                                            disabled: !agendaForm.name,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1064,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1056,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1048,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1006,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 994,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AccueilEtAgenda, \"9TmtZyRHMHLof8M+DHEVpq/AogA=\");\n_c = AccueilEtAgenda;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AccueilEtAgenda);\nvar _c;\n$RefreshReg$(_c, \"AccueilEtAgenda\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx\n"));

/***/ })

});