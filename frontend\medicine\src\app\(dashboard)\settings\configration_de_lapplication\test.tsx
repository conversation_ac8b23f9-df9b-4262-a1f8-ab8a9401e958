import React from 'react'

const test = () => {
  return (
 <>
<div class="param-body mn-module flex layout-column md-whiteframe-z1 ng-scope" ui-view="" style=""><md-toolbar class="mn-module-header md-accent ng-scope _md _md-toolbar-transitions">
    <div class="md-toolbar-tools">
        <div class="mn-module-icon">
            <md-icon md-font-icon="mdi-view-list" md-font-set="mdi" class="md-font mdi mdi-view-list" role="img" aria-label="mdi-view-list"></md-icon>
        </div>
        <h2 translate-once="list_setup">Gestion des listes</h2>
        <span flex="" class="flex"></span>
        <button class="md-button md-ink-ripple" type="button" ng-transclude="" aria-label="new list item" ng-click="vm.handleItem({}, $event)" ng-disabled="!vm.currentList" disabled="disabled">
            <md-icon md-font-icon="mdi-plus" md-font-set="mdi" class="ng-scope md-font mdi mdi-plus" role="img" aria-hidden="true"></md-icon>
            <span translate-once="list_setup_add" class="ng-scope">Ajouter</span>
        </button>
    </div>
</md-toolbar>

<md-content class="mn-module-body md-padding layout-column flex ng-scope _md">
    <div flex="" layout="row" class="layout-row flex">
        <md-sidenav class="mn-module-side-nav md-locked-open md-sidenav-left layout-column flex-nogrow md-closed ng-isolate-scope _md" md-is-locked-open="true" tabindex="-1">
            <md-toolbar class="mn-module-header md-primary _md _md-toolbar-transitions">
                <div class="md-toolbar-tools">
                    <div class="mn-module-icon">
                        <md-icon md-font-icon="mdi-format-list-bulleted" md-font-set="mdi" class="md-font mdi mdi-format-list-bulleted" role="img" aria-label="mdi-format-list-bulleted"></md-icon>
                    </div>
                    <h2>
                        <span translate-once="list_setup_models">Types</span>
                    </h2>
                    <span flex="" class="flex"></span>
                </div>
            </md-toolbar>
            <md-content flex="" layout="column" class="_md layout-column flex">
                <!-- ngIf: vm.models --><md-list flex="" ng-if="vm.models" role="list" class="ng-scope flex">
                    <!-- ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Titre</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Profession</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">État civil</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Origine ethnique</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Nationalité</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">SpokenLanguage</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Unité</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Unité d'inventaire</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Groupe sanguin</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Banque</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Mode de paiement</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">PrescriptionIndication</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Indication d'examen</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Forme posologique</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">TVA</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">ObservationIndication</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Catégories des dépenses</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Catégories des contacts</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Raison de résiliation de contrat</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index --><md-list-item class="flex layout-row layout-align-start-center _md-button-wrap ng-scope _md md-clickable" ng-repeat="item in vm.models track by $index" role="listitem" tabindex="-1"><div class="md-button md-no-style"><button class="md-no-style md-button md-ink-ripple" type="button" ng-transclude="" ng-click="vm.viewList(item)" aria-label="consult list item"></button>   <div class="md-list-item-inner">
                            <span>
                                <md-icon md-font-icon="mdi-file-document" md-font-set="mdi" class="list-icon md-font mdi mdi-file-document" aria-label="status" role="img"></md-icon>
                                <span ng-bind="item | translate" class="flex ng-binding">Types d'appareils de réhabilitation</span>
                            </span>

                        <!-- ngIf: vm.currentList == item -->
                    </div><div class="md-secondary-container"></div></div></md-list-item><!-- end ngRepeat: item in vm.models track by $index -->
                </md-list><!-- end ngIf: vm.models -->
            </md-content>
        </md-sidenav>
        <md-content class="mn-module-side-content layout-column flex _md">
            <div class="table-container flex">
                <md-table-container>
                    <table md-table="" class="mn-striped md-table ng-isolate-scope" md-progress="vm.promise">
                        <thead md-head="" class="md-head ng-isolate-scope">
                        <tr md-row="" class="md-row">
                            <th md-column="" class="md-column ng-isolate-scope">
                                <span translate-once="list_setup_value">Nom</span>
                            </th>
                            <th md-column="" class="md-column ng-isolate-scope">
                                <span translate-once="list_setup_description">Description</span>
                            </th>
                            <th md-column="" class="md-column ng-isolate-scope">
                                <span translate-once="list_setup_is_hidden">Caché</span>
                            </th>
                            <th md-column="" class="actions-column-3 right-aligned md-column ng-isolate-scope">
                                <!-- ngIf: vm.items && vm.items.length > 0 -->
                                <!-- ngIf: vm.items && vm.items.length > 0 -->
                            </th>
                        </tr>
                        </thead>
                        <thead class="md-table-progress ng-isolate-scope" md-table-progress=""><tr>
  <th colspan="4">
    <md-progress-linear ng-show="deferred()" md-mode="indeterminate" aria-valuemin="0" aria-valuemax="100" role="progressbar" aria-hidden="true" class="ng-hide"><div class="md-container md-mode-indeterminate"><div class="md-dashed"></div><div class="md-bar md-bar1"></div><div class="md-bar md-bar2"></div></div></md-progress-linear>
  </th>
</tr></thead><tbody md-body="" dragula="&quot;item-bag&quot;" dragula-model="vm.items" class="md-body ng-isolate-scope">

                        <!-- ngIf: !vm.items || vm.items.length == 0 --><tr md-row="" ng-if="!vm.items || vm.items.length == 0" class="md-row ng-scope">
                            <td md-cell="" colspan="4" translate-once="no_element_to_show" class="md-cell">Aucun élément trouvé.</td>
                            <td md-cell="" ng-hide="true" class="md-cell ng-hide" aria-hidden="true"></td>
                        </tr><!-- end ngIf: !vm.items || vm.items.length == 0 -->

                        <!-- ngRepeat: item in vm.items -->
                        </tbody>
                    </table>
                </md-table-container>
            </div>
        </md-content>
    </div>

</md-content></div>
 
 </>
  )
}

export default test
