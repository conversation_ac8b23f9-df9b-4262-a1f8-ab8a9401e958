"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHeart.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconHeart.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconHeart)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconHeart = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"heart\", \"IconHeart\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572\",\n            \"key\": \"svg-0\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconHeart.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25IZWFydC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDQSxDQUFlLG9GQUFxQixZQUFXLE9BQVMsY0FBYTtJQUFDO1FBQUM7UUFBTyxDQUFDO1lBQUEsSUFBSSwrRUFBK0U7WUFBQSxPQUFNLENBQU87UUFBQSxDQUFDO0tBQUM7Q0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcc3JjXFxpY29uc1xcSWNvbkhlYXJ0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVSZWFjdENvbXBvbmVudCBmcm9tICcuLi9jcmVhdGVSZWFjdENvbXBvbmVudCc7XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVSZWFjdENvbXBvbmVudCgnb3V0bGluZScsICdoZWFydCcsICdJY29uSGVhcnQnLCBbW1wicGF0aFwiLHtcImRcIjpcIk0xOS41IDEyLjU3MmwtNy41IDcuNDI4bC03LjUgLTcuNDI4YTUgNSAwIDEgMSA3LjUgLTYuNTY2YTUgNSAwIDEgMSA3LjUgNi41NzJcIixcImtleVwiOlwic3ZnLTBcIn1dXSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHeart.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/PlanDeSoins.tsx":
/*!********************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/PlanDeSoins.tsx ***!
  \********************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Divider/Divider.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHeart.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconUsers.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconStethoscope.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/form */ \"(app-pages-browser)/./node_modules/@mantine/form/esm/use-form.mjs\");\n/* harmony import */ var mantine_datatable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mantine-datatable */ \"(app-pages-browser)/./node_modules/mantine-datatable/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PlanDeSoins = ()=>{\n    _s();\n    // États pour les onglets et modales\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [protocoleModalOpened, { open: openProtocoleModal, close: closeProtocoleModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [acteSoinModalOpened, { open: openActeSoinModal, close: closeActeSoinModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [groupeActeModalOpened, { open: openGroupeActeModal, close: closeGroupeActeModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [procedureModalOpened, { open: openProcedureModal, close: closeProcedureModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [acteModalOpened, { open: openActeModal, close: closeActeModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [modaliteModalOpened, { open: openModaliteModal, close: closeModaliteModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    // États pour les données\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [protocolesSoin, setProtocolesSoin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [actesSoin, setActesSoin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [groupesActe, setGroupesActe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [actes, setActes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modalites, setModalites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Formulaires\n    const protocoleForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            titre: '',\n            groupes: []\n        },\n        validate: {\n            titre: {\n                \"PlanDeSoins.useForm[protocoleForm]\": (value)=>!value ? 'Le titre est requis' : null\n            }[\"PlanDeSoins.useForm[protocoleForm]\"]\n        }\n    });\n    const acteSoinForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            titre: '',\n            groupeActe: '',\n            acte: '',\n            motif: ''\n        },\n        validate: {\n            titre: {\n                \"PlanDeSoins.useForm[acteSoinForm]\": (value)=>!value ? 'Le titre est requis' : null\n            }[\"PlanDeSoins.useForm[acteSoinForm]\"],\n            groupeActe: {\n                \"PlanDeSoins.useForm[acteSoinForm]\": (value)=>!value ? 'Le groupe d\\'acte est requis' : null\n            }[\"PlanDeSoins.useForm[acteSoinForm]\"],\n            acte: {\n                \"PlanDeSoins.useForm[acteSoinForm]\": (value)=>!value ? 'L\\'acte est requis' : null\n            }[\"PlanDeSoins.useForm[acteSoinForm]\"],\n            motif: {\n                \"PlanDeSoins.useForm[acteSoinForm]\": (value)=>!value ? 'Le motif est requis' : null\n            }[\"PlanDeSoins.useForm[acteSoinForm]\"]\n        }\n    });\n    const groupeActeForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            titre: '',\n            couleur: '#3b82f6'\n        },\n        validate: {\n            titre: {\n                \"PlanDeSoins.useForm[groupeActeForm]\": (value)=>!value ? 'Le titre est requis' : null\n            }[\"PlanDeSoins.useForm[groupeActeForm]\"]\n        }\n    });\n    const procedureForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            code: '',\n            nom: '',\n            honoraire: 0,\n            servicesDesignes: '',\n            codeNGAP: '',\n            codeCCAM: '',\n            tnr: '',\n            modalite: '',\n            remboursable: false\n        },\n        validate: {\n            code: {\n                \"PlanDeSoins.useForm[procedureForm]\": (value)=>!value ? 'Le code est requis' : null\n            }[\"PlanDeSoins.useForm[procedureForm]\"],\n            nom: {\n                \"PlanDeSoins.useForm[procedureForm]\": (value)=>!value ? 'Le nom est requis' : null\n            }[\"PlanDeSoins.useForm[procedureForm]\"]\n        }\n    });\n    const acteForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            code: '',\n            description: '',\n            duree: 15,\n            couleur: '#3b82f6',\n            couleurRayee: '#ef4444',\n            agendaParDefaut: '',\n            servicesDesignes: '',\n            couleurSombre: false\n        },\n        validate: {\n            code: {\n                \"PlanDeSoins.useForm[acteForm]\": (value)=>!value ? 'Le code est requis' : null\n            }[\"PlanDeSoins.useForm[acteForm]\"],\n            description: {\n                \"PlanDeSoins.useForm[acteForm]\": (value)=>!value ? 'La description est requise' : null\n            }[\"PlanDeSoins.useForm[acteForm]\"]\n        }\n    });\n    const modaliteForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            valeur: '',\n            description: ''\n        },\n        validate: {\n            valeur: {\n                \"PlanDeSoins.useForm[modaliteForm]\": (value)=>!value ? 'La valeur est requise' : null\n            }[\"PlanDeSoins.useForm[modaliteForm]\"]\n        }\n    });\n    // Gestionnaires pour les onglets\n    const handleTabChange = (value)=>{\n        if (value) setActiveTab(value);\n    };\n    // Gestionnaires de soumission\n    const handleProtocoleSubmit = (values)=>{\n        const newProtocole = {\n            id: Date.now().toString(),\n            titre: values.titre,\n            description: ''\n        };\n        setProtocolesSoin((prev)=>[\n                ...prev,\n                newProtocole\n            ]);\n        closeProtocoleModal();\n        protocoleForm.reset();\n    };\n    const handleActeSoinSubmit = (values)=>{\n        const newActeSoin = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setActesSoin((prev)=>[\n                ...prev,\n                newActeSoin\n            ]);\n        closeActeSoinModal();\n        acteSoinForm.reset();\n    };\n    const handleGroupeActeSubmit = (values)=>{\n        const newGroupeActe = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setGroupesActe((prev)=>[\n                ...prev,\n                newGroupeActe\n            ]);\n        closeGroupeActeModal();\n        groupeActeForm.reset();\n    };\n    const handleProcedureSubmit = (values)=>{\n        const newProcedure = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setProcedures((prev)=>[\n                ...prev,\n                newProcedure\n            ]);\n        closeProcedureModal();\n        procedureForm.reset();\n    };\n    const handleActeSubmit = (values)=>{\n        const newActe = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setActes((prev)=>[\n                ...prev,\n                newActe\n            ]);\n        closeActeModal();\n        acteForm.reset();\n    };\n    const handleModaliteSubmit = (values)=>{\n        const newModalite = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setModalites((prev)=>[\n                ...prev,\n                newModalite\n            ]);\n        closeModaliteModal();\n        modaliteForm.reset();\n    };\n    // Fonction pour obtenir le bouton d'action selon l'onglet actif\n    const getActionButton = ()=>{\n        switch(activeTab){\n            case 'protocoles-soin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 26\n                    }, void 0),\n                    onClick: openProtocoleModal,\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    children: \"Protocole de soins\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 11\n                }, undefined);\n            case 'actes-soin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 26\n                    }, void 0),\n                    onClick: openActeSoinModal,\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    children: \"Acte de soins\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, undefined);\n            case 'groupes-actes':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 26\n                    }, void 0),\n                    onClick: openGroupeActeModal,\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    children: \"Groupe d'acte\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    // Colonnes pour les tables\n    const protocoleColumns = [\n        {\n            accessor: 'titre',\n            title: 'Titre'\n        },\n        {\n            accessor: 'description',\n            title: 'Description'\n        }\n    ];\n    const acteSoinColumns = [\n        {\n            accessor: 'titre',\n            title: 'Titre'\n        },\n        {\n            accessor: 'groupeActe',\n            title: 'Groupe d\\'acte'\n        },\n        {\n            accessor: 'couleur',\n            title: 'Couleur',\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 rounded\",\n                        style: {\n                            backgroundColor: record.couleur\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 7\n                }, undefined)\n        },\n        {\n            accessor: 'acte',\n            title: 'Acte'\n        },\n        {\n            accessor: 'motif',\n            title: 'Motif'\n        }\n    ];\n    const groupeActeColumns = [\n        {\n            accessor: 'titre',\n            title: 'Titre'\n        },\n        {\n            accessor: 'couleur',\n            title: 'Couleur',\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 rounded\",\n                        style: {\n                            backgroundColor: record.couleur\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 7\n                }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-gray-50 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 32,\n                                className: \"text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-800\",\n                                children: \"Param\\xe9trage des plan de soins\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, undefined),\n                    getActionButton()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 376,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                shadow: \"sm\",\n                padding: \"lg\",\n                radius: \"md\",\n                withBorder: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                    value: activeTab,\n                    onChange: handleTabChange,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.List, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Tab, {\n                                    value: \"general\",\n                                    children: \"G\\xe9n\\xe9ral\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Tab, {\n                                    value: \"protocoles-soin\",\n                                    children: \"Protocoles de soin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Tab, {\n                                    value: \"actes-soin\",\n                                    children: \"Actes de soin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Tab, {\n                                    value: \"groupes-actes\",\n                                    children: \"Groupes d'actes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Panel, {\n                            value: \"general\",\n                            pt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                        placeholder: \"Rechercher\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        value: searchQuery,\n                                        onChange: (event)=>setSearchQuery(event.currentTarget.value),\n                                        className: \"w-96\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                    columns: [\n                                        {\n                                            accessor: 'titre',\n                                            title: 'Titre'\n                                        },\n                                        {\n                                            accessor: 'description',\n                                            title: 'Description'\n                                        }\n                                    ],\n                                    records: [],\n                                    noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\",\n                                    minHeight: 200\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Panel, {\n                            value: \"protocoles-soin\",\n                            pt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                        placeholder: \"Rechercher\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        value: searchQuery,\n                                        onChange: (event)=>setSearchQuery(event.currentTarget.value),\n                                        className: \"w-96\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                    columns: protocoleColumns,\n                                    records: protocolesSoin,\n                                    noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\",\n                                    minHeight: 200\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Panel, {\n                            value: \"actes-soin\",\n                            pt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                        placeholder: \"Rechercher\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        value: searchQuery,\n                                        onChange: (event)=>setSearchQuery(event.currentTarget.value),\n                                        className: \"w-96\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                    columns: acteSoinColumns,\n                                    records: actesSoin,\n                                    noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\",\n                                    minHeight: 200\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Panel, {\n                            value: \"groupes-actes\",\n                            pt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                        placeholder: \"Rechercher\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        value: searchQuery,\n                                        onChange: (event)=>setSearchQuery(event.currentTarget.value),\n                                        className: \"w-96\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                    columns: groupeActeColumns,\n                                    records: groupesActe,\n                                    noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\",\n                                    minHeight: 200\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: protocoleModalOpened,\n                onClose: closeProtocoleModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Nouveau protocole\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 484,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: protocoleForm.onSubmit(handleProtocoleSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                label: \"Titre du protocole de soins\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                ...protocoleForm.getInputProps('titre')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 20,\n                                                className: \"text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                fw: 600,\n                                                className: \"text-blue-700\",\n                                                children: \"Groupes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                        size: \"sm\",\n                                        className: \"text-gray-600 mb-2\",\n                                        children: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeProtocoleModal,\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Enregistrer et quitter\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"blue\",\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 495,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 480,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: acteSoinModalOpened,\n                onClose: closeActeSoinModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Acte de soins\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 533,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: acteSoinForm.onSubmit(handleActeSoinSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                label: \"Titre\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                ...acteSoinForm.getInputProps('titre')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                label: \"Groupe d'acte\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                data: groupesActe.map((g)=>({\n                                        value: g.id,\n                                        label: g.titre\n                                    })),\n                                ...acteSoinForm.getInputProps('groupeActe')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                        label: \"Acte\",\n                                        placeholder: \"\",\n                                        required: true,\n                                        styles: {\n                                            label: {\n                                                color: 'red'\n                                            }\n                                        },\n                                        data: [],\n                                        className: \"flex-1\",\n                                        ...acteSoinForm.getInputProps('acte')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ActionIcon, {\n                                        size: \"lg\",\n                                        className: \"mt-6 bg-blue-500 hover:bg-blue-600\",\n                                        onClick: openProcedureModal,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                        label: \"Motif\",\n                                        placeholder: \"\",\n                                        required: true,\n                                        styles: {\n                                            label: {\n                                                color: 'red'\n                                            }\n                                        },\n                                        data: [],\n                                        className: \"flex-1\",\n                                        ...acteSoinForm.getInputProps('motif')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-gray-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeActeSoinModal,\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 544,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 529,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: groupeActeModalOpened,\n                onClose: closeGroupeActeModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 617,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Groupe d'acte\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 616,\n                    columnNumber: 11\n                }, void 0),\n                size: \"md\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: groupeActeForm.onSubmit(handleGroupeActeSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                label: \"Titre\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                ...groupeActeForm.getInputProps('titre')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: 5,\n                                        children: \"Couleur\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorPicker, {\n                                                format: \"hex\",\n                                                ...groupeActeForm.getInputProps('couleur')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeGroupeActeModal,\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 627,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 612,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: procedureModalOpened,\n                onClose: closeProcedureModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Proc\\xe9dure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: procedureForm.onSubmit(handleProcedureSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Code\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...procedureForm.getInputProps('code')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Nom\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...procedureForm.getInputProps('nom')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 678,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.NumberInput, {\n                                            label: \"Honoraire\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...procedureForm.getInputProps('honoraire')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                            label: \"Services d\\xe9sign\\xe9s\",\n                                            placeholder: \"\",\n                                            data: [],\n                                            ...procedureForm.getInputProps('servicesDesignes')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Divider, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Code NGAP\",\n                                            placeholder: \"\",\n                                            ...procedureForm.getInputProps('codeNGAP')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Code CCAM\",\n                                            placeholder: \"\",\n                                            ...procedureForm.getInputProps('codeCCAM')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"TNR\",\n                                            placeholder: \"\",\n                                            ...procedureForm.getInputProps('tnr')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                                    label: \"Modalit\\xe9\",\n                                                    placeholder: \"\",\n                                                    data: modalites.map((m)=>({\n                                                            value: m.id,\n                                                            label: m.valeur\n                                                        })),\n                                                    className: \"flex-1\",\n                                                    ...procedureForm.getInputProps('modalite')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ActionIcon, {\n                                                    size: \"lg\",\n                                                    className: \"mt-6 bg-blue-500 hover:bg-blue-600\",\n                                                    onClick: openModaliteModal,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 738,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.Checkbox, {\n                                label: \"Remboursable\",\n                                ...procedureForm.getInputProps('remboursable', {\n                                    type: 'checkbox'\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 766,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeProcedureModal,\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 771,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 676,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 661,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: acteModalOpened,\n                onClose: closeActeModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 789,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 788,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: acteForm.onSubmit(handleActeSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Code\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...acteForm.getInputProps('code')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_26__.Textarea, {\n                                            label: \"Description\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...acteForm.getInputProps('description')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 801,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.NumberInput, {\n                                                    label: \"Dur\\xe9e (min)\",\n                                                    placeholder: \"\",\n                                                    min: 1,\n                                                    defaultValue: 15,\n                                                    className: \"flex-1\",\n                                                    ...acteForm.getInputProps('duree')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                            lineNumber: 834,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            size: \"sm\",\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                            lineNumber: 835,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 824,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 841,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"Couleur ray\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 840,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 822,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Divider, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                                    label: \"Agenda par d\\xe9faut\",\n                                                    placeholder: \"\",\n                                                    data: [],\n                                                    className: \"flex-1\",\n                                                    ...acteForm.getInputProps('agendaParDefaut')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ActionIcon, {\n                                                    size: \"lg\",\n                                                    className: \"mt-6 bg-blue-500 hover:bg-blue-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 859,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                            label: \"Services d\\xe9sign\\xe9s\",\n                                            placeholder: \"\",\n                                            data: [],\n                                            ...acteForm.getInputProps('servicesDesignes')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 867,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.Checkbox, {\n                                label: \"Couleur sombre\",\n                                ...acteForm.getInputProps('couleurSombre', {\n                                    type: 'checkbox'\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeActeModal,\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 800,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 799,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 784,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: modaliteModalOpened,\n                onClose: closeModaliteModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Ajouter Modalit\\xe9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 901,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 899,\n                    columnNumber: 11\n                }, void 0),\n                size: \"md\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: modaliteForm.onSubmit(handleModaliteSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                label: \"Valeur\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                ...modaliteForm.getInputProps('valeur')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 912,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_26__.Textarea, {\n                                label: \"Description\",\n                                placeholder: \"\",\n                                rows: 3,\n                                ...modaliteForm.getInputProps('description')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 920,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeModaliteModal,\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 911,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 910,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 895,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n        lineNumber: 374,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlanDeSoins, \"R1BZ7p/ynvx2jlsTYZnAlxH1ZPc=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm\n    ];\n});\n_c = PlanDeSoins;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlanDeSoins);\nvar _c;\n$RefreshReg$(_c, \"PlanDeSoins\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/PlanDeSoins.tsx\n"));

/***/ })

});