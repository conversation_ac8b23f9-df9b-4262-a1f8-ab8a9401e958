"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHome.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSettings.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCalendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconEdit.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst AccueilEtAgenda = ()=>{\n    _s();\n    // États pour les onglets\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    // États pour les modales\n    const [motifModalOpened, setMotifModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [agendaModalOpened, setAgendaModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [salleModalOpened, setSalleModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [evenementModalOpened, setEvenementModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMotif, setEditingMotif] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingAgenda, setEditingAgenda] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSalle, setEditingSalle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingEvenement, setEditingEvenement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour les formulaires\n    const [motifForm, setMotifForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: '',\n        description: '',\n        duration: 15,\n        color: '#9b4d93',\n        colorRayee: '#792485',\n        agendaDefaut: '',\n        servicesDesignes: '',\n        couleurSombre: false\n    });\n    const [agendaForm, setAgendaForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        servicesDesignes: '',\n        isResource: false\n    });\n    // États pour les paramètres généraux\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        heureOuverture: '08:00',\n        heureFermeture: '20:00',\n        premierJourSemaine: 'Lundi',\n        vueDefautCalendrier: 'Semaine',\n        minimumIntervalle: '15 minutes',\n        fuseauHoraire: 'Africa/Casablanca',\n        cacherJoursFermeture: false,\n        curseurAgendaHeureCurrent: true,\n        filtre24hCabinet: true,\n        utiliserAbbreviation: false,\n        utiliserListePatients: false,\n        afficherSeulementVisitesTerminees: false\n    });\n    // Données de test pour les motifs\n    const [motifs, setMotifs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            code: 'CNS',\n            description: 'Consultation',\n            duration: 15,\n            color: '#9b4d93',\n            colorRayee: '#792485'\n        },\n        {\n            id: 2,\n            code: 'CTR',\n            description: 'Contrôle',\n            duration: 15,\n            color: '#c05889',\n            colorRayee: '#7d1868'\n        },\n        {\n            id: 3,\n            code: 'CHI',\n            description: 'Chirurgie/Paro',\n            duration: 30,\n            color: '#c14317',\n            colorRayee: '#922a12'\n        },\n        {\n            id: 4,\n            code: 'COM',\n            description: 'Composite',\n            duration: 15,\n            color: '#2836cd',\n            colorRayee: '#091486'\n        },\n        {\n            id: 5,\n            code: 'DS',\n            description: 'Dépose Sutures',\n            duration: 15,\n            color: '#2e7d32',\n            colorRayee: '#1b5e20'\n        },\n        {\n            id: 6,\n            code: 'CS',\n            description: '1er Consultation',\n            duration: 15,\n            color: '#e91e63',\n            colorRayee: '#c2185b'\n        },\n        {\n            id: 7,\n            code: 'DEV',\n            description: 'Devis',\n            duration: 15,\n            color: 'rgb(23, 160, 131)',\n            colorRayee: '#024f3f'\n        },\n        {\n            id: 8,\n            code: 'END',\n            description: 'Endodontie',\n            duration: 60,\n            color: '#a08523',\n            colorRayee: '#564507'\n        },\n        {\n            id: 9,\n            code: 'FOR',\n            description: 'Formation',\n            duration: 60,\n            color: '#982552',\n            colorRayee: '#72183c'\n        },\n        {\n            id: 10,\n            code: 'IMP',\n            description: 'Implantologie',\n            duration: 60,\n            color: '#388983',\n            colorRayee: '#26645e'\n        },\n        {\n            id: 11,\n            code: 'ORT',\n            description: 'Orthodontie',\n            duration: 30,\n            color: '#464098',\n            colorRayee: '#1a7c81'\n        },\n        {\n            id: 12,\n            code: 'CM',\n            description: 'FS Chassis/Monta',\n            duration: 15,\n            color: '#6c407a',\n            colorRayee: '#9c4168'\n        },\n        {\n            id: 13,\n            code: 'POS',\n            description: 'PA Pose',\n            duration: 15,\n            color: '#b25953',\n            colorRayee: '#9c3e3e'\n        },\n        {\n            id: 14,\n            code: 'EMP',\n            description: 'PC TP EMP',\n            duration: 30,\n            color: '#4c8d57',\n            colorRayee: '#366e42'\n        },\n        {\n            id: 15,\n            code: 'ARM',\n            description: 'PC ESS Armature',\n            duration: 30,\n            color: '#3291ef',\n            colorRayee: '#0d667b'\n        },\n        {\n            id: 16,\n            code: 'SCE',\n            description: 'PC Scellement',\n            duration: 15,\n            color: '#c2185b',\n            colorRayee: '#880e4f'\n        },\n        {\n            id: 17,\n            code: 'PRO',\n            description: 'Prophylaxie',\n            duration: 15,\n            color: '#009688',\n            colorRayee: '#00695c'\n        }\n    ]);\n    // Données de test pour les agendas\n    const [agendas, setAgendas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // États pour la recherche\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Filtrer les motifs selon la recherche\n    const filteredMotifs = motifs.filter((motif)=>motif.code.toLowerCase().includes(searchTerm.toLowerCase()) || motif.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    // Jours de la semaine\n    const joursSemaine = [\n        'Lundi',\n        'Mardi',\n        'Mercredi',\n        'Jeudi',\n        'Vendredi',\n        'Samedi',\n        'Dimanche'\n    ];\n    // Options pour les selects\n    const vueCalendrierOptions = [\n        {\n            value: 'Semaine',\n            label: 'Semaine'\n        },\n        {\n            value: 'Mois',\n            label: 'Mois'\n        },\n        {\n            value: 'Jour',\n            label: 'Jour'\n        }\n    ];\n    const intervalleOptions = [\n        {\n            value: '5 minutes',\n            label: '5 minutes'\n        },\n        {\n            value: '10 minutes',\n            label: '10 minutes'\n        },\n        {\n            value: '15 minutes',\n            label: '15 minutes'\n        },\n        {\n            value: '30 minutes',\n            label: '30 minutes'\n        }\n    ];\n    const fuseauHoraireOptions = [\n        {\n            value: 'Africa/Casablanca',\n            label: 'Africa/Casablanca'\n        },\n        {\n            value: 'Europe/Paris',\n            label: 'Europe/Paris'\n        },\n        {\n            value: 'UTC',\n            label: 'UTC'\n        }\n    ];\n    const agendaDefautOptions = [\n        {\n            value: 'agenda1',\n            label: 'Agenda par défaut'\n        },\n        {\n            value: 'agenda2',\n            label: 'Agenda 2'\n        }\n    ];\n    const servicesDesignesOptions = [\n        {\n            value: 'service1',\n            label: 'Services désignés'\n        },\n        {\n            value: 'service2',\n            label: 'Service 2'\n        }\n    ];\n    // Fonctions pour gérer les modales\n    const openMotifModal = (motif)=>{\n        if (motif) {\n            setEditingMotif(motif);\n            setMotifForm({\n                code: motif.code,\n                description: motif.description,\n                duration: motif.duration,\n                color: motif.color,\n                colorRayee: motif.colorRayee,\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        } else {\n            setEditingMotif(null);\n            setMotifForm({\n                code: '',\n                description: '',\n                duration: 15,\n                color: '#9b4d93',\n                colorRayee: '#792485',\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        }\n        setMotifModalOpened(true);\n    };\n    const openAgendaModal = (agenda)=>{\n        if (agenda) {\n            setEditingAgenda(agenda);\n            setAgendaForm({\n                name: agenda.name,\n                description: agenda.description,\n                color: agenda.color,\n                servicesDesignes: '',\n                isResource: agenda.isResource\n            });\n        } else {\n            setEditingAgenda(null);\n            setAgendaForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                servicesDesignes: '',\n                isResource: false\n            });\n        }\n        setAgendaModalOpened(true);\n    };\n    // Fonctions pour sauvegarder\n    const handleSaveMotif = ()=>{\n        if (editingMotif) {\n            // Modifier un motif existant\n            setMotifs((prev)=>prev.map((m)=>m.id === editingMotif.id ? {\n                        ...m,\n                        ...motifForm\n                    } : m));\n        } else {\n            // Ajouter un nouveau motif\n            const newMotif = {\n                id: Date.now(),\n                code: motifForm.code,\n                description: motifForm.description,\n                duration: motifForm.duration,\n                color: motifForm.color,\n                colorRayee: motifForm.colorRayee\n            };\n            setMotifs((prev)=>[\n                    ...prev,\n                    newMotif\n                ]);\n        }\n        setMotifModalOpened(false);\n    };\n    const handleSaveAgenda = ()=>{\n        if (editingAgenda) {\n            // Modifier un agenda existant\n            setAgendas((prev)=>prev.map((a)=>a.id === editingAgenda.id ? {\n                        ...a,\n                        ...agendaForm\n                    } : a));\n        } else {\n            // Ajouter un nouvel agenda\n            const newAgenda = {\n                id: Date.now(),\n                name: agendaForm.name,\n                description: agendaForm.description,\n                color: agendaForm.color,\n                isResource: agendaForm.isResource\n            };\n            setAgendas((prev)=>[\n                    ...prev,\n                    newAgenda\n                ]);\n        }\n        setAgendaModalOpened(false);\n    };\n    // Fonction pour supprimer un motif\n    const handleDeleteMotif = (id)=>{\n        setMotifs((prev)=>prev.filter((m)=>m.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        gap: \"lg\",\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                order: 2,\n                className: \"text-gray-800 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24,\n                        className: \"text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Accueil et Agenda\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                value: activeTab,\n                onChange: (value)=>setActiveTab(value || 'general'),\n                variant: \"outline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.List, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"general\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"G\\xe9n\\xe9ral\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"motifs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Motifs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"code-couleurs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 56\n                                }, void 0),\n                                children: \"Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"agendas\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"Agendas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"salles\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"evenements\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 53\n                                }, void 0),\n                                children: \"\\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"general\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                gap: \"lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Horaires du centre\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure d'ouverture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureOuverture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureOuverture: e.target.value\n                                                                        })),\n                                                                placeholder: \"08:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure de fermeture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureFermeture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureFermeture: e.target.value\n                                                                        })),\n                                                                placeholder: \"20:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                        size: \"sm\",\n                                                        fw: 500,\n                                                        mb: \"xs\",\n                                                        children: \"Jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-wrap\",\n                                                        children: joursSemaine.map((jour)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                variant: \"light\",\n                                                                color: \"blue\",\n                                                                className: \"cursor-pointer\",\n                                                                children: jour\n                                                            }, jour, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Param\\xe8tres d'affichage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Premier jour de la semaine\",\n                                                            value: generalSettings.premierJourSemaine,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        premierJourSemaine: value || 'Lundi'\n                                                                    })),\n                                                            data: joursSemaine.map((jour)=>({\n                                                                    value: jour,\n                                                                    label: jour\n                                                                }))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Vue par d\\xe9faut du calendrier\",\n                                                            value: generalSettings.vueDefautCalendrier,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        vueDefautCalendrier: value || 'Semaine'\n                                                                    })),\n                                                            data: vueCalendrierOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Minimum intervalle de temps\",\n                                                            value: generalSettings.minimumIntervalle,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        minimumIntervalle: value || '15 minutes'\n                                                                    })),\n                                                            data: intervalleOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Fuseau horaire\",\n                                                            value: generalSettings.fuseauHoraire,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        fuseauHoraire: value || 'Africa/Casablanca'\n                                                                    })),\n                                                            data: fuseauHoraireOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                                gap: \"xs\",\n                                                mt: \"md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.cacherJoursFermeture,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    cacherJoursFermeture: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Cacher les jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.curseurAgendaHeureCurrent,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    curseurAgendaHeureCurrent: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Curseur d'agenda \\xe0 l'heure current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.filtre24hCabinet,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    filtre24hCabinet: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Filtre de 24h du cabinet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserAbbreviation,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserAbbreviation: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser abr\\xe9viation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserListePatients,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserListePatients: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser la liste des patients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.afficherSeulementVisitesTerminees,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    afficherSeulementVisitesTerminees: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Afficher seulement les visites termin\\xe9es dans 'Historique journalier'\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Rappel de rendez-vous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Dur\\xe9e avant RDV\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"5\",\n                                                            data: [\n                                                                {\n                                                                    value: '5',\n                                                                    label: 'Modèle SMS'\n                                                                },\n                                                                {\n                                                                    value: '10',\n                                                                    label: '10 minutes'\n                                                                },\n                                                                {\n                                                                    value: '15',\n                                                                    label: '15 minutes'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"Mod\\xe8le Email\",\n                                                            data: [\n                                                                {\n                                                                    value: 'email1',\n                                                                    label: 'Modèle Email'\n                                                                },\n                                                                {\n                                                                    value: 'email2',\n                                                                    label: 'Email 2'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"motifs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                placeholder: \"Rechercher\",\n                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openMotifModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Ajouter nouveau motif\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Motif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Dur\\xe9e (min)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur ray\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: filteredMotifs.map((motif)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: motif.code\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.colorRayee,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.colorRayee\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openMotifModal(motif),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 589,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 584,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                onClick: ()=>handleDeleteMotif(motif.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 598,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, motif.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 528,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"code-couleurs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 612,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"agendas\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"Agendas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openAgendaModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Nouvel agenda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Ressource\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: agendas.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                        colSpan: 5,\n                                                        className: \"text-center text-gray-500 py-8\",\n                                                        children: \"Aucun agenda trouv\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 21\n                                                }, undefined) : agendas.map((agenda)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: agenda.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: agenda.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 656,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: agenda.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 659,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: agenda.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 660,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                    color: agenda.isResource ? 'green' : 'gray',\n                                                                    children: agenda.isResource ? 'Oui' : 'Non'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                        label: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"blue\",\n                                                                            onClick: ()=>openAgendaModal(agenda),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 676,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 671,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 668,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, agenda.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"salles\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 691,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"evenements\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour \\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 700,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 699,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.Modal, {\n                opened: motifModalOpened,\n                onClose: ()=>setMotifModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 712,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 710,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Code \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 35\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.code,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Description \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.description,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Dur\\xe9e (min) \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.duration.toString(),\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    duration: parseInt(e.target.value) || 15\n                                                })),\n                                        type: \"number\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 748,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur ray\\xe9e\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.colorRayee,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Agenda par d\\xe9faut\",\n                                        value: motifForm.agendaDefaut,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    agendaDefaut: value || ''\n                                                })),\n                                        data: agendaDefautOptions,\n                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 16,\n                                            className: \"cursor-pointer text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 31\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: motifForm.servicesDesignes,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Switch, {\n                                    checked: motifForm.couleurSombre,\n                                    onChange: (e)=>setMotifForm((prev)=>({\n                                                ...prev,\n                                                couleurSombre: e.currentTarget.checked\n                                            })),\n                                    label: \"Couleur sombre\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setMotifModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveMotif,\n                                            disabled: !motifForm.code || !motifForm.description,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 802,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 794,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 718,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 706,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.Modal, {\n                opened: agendaModalOpened,\n                onClose: ()=>setAgendaModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 829,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Agenda\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Nom \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 34\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 840,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: agendaForm.name,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 848,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: agendaForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 837,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: \"Description\",\n                                        value: agendaForm.description,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: agendaForm.servicesDesignes,\n                                        onChange: (value)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Switch, {\n                                    checked: agendaForm.isResource,\n                                    onChange: (e)=>setAgendaForm((prev)=>({\n                                                ...prev,\n                                                isResource: e.currentTarget.checked\n                                            })),\n                                    label: \"Afficher comme ressource\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 879,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setAgendaModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 887,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveAgenda,\n                                            disabled: !agendaForm.name,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 894,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 878,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 836,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 824,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AccueilEtAgenda, \"9KL4RfdZHnr6bUkbHeQVxHecYZI=\");\n_c = AccueilEtAgenda;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AccueilEtAgenda);\nvar _c;\n$RefreshReg$(_c, \"AccueilEtAgenda\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx\n"));

/***/ })

});