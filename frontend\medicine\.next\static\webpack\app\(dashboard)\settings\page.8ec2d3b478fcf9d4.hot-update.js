"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBadge.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconBadge.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconBadge)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconBadge = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"badge\", \"IconBadge\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M17 17v-13l-5 3l-5 -3v13l5 3z\",\n            \"key\": \"svg-0\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconBadge.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25CYWRnZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDQSxDQUFlLG9GQUFxQixZQUFXLE9BQVMsY0FBYTtJQUFDO1FBQUM7UUFBTyxDQUFDO1lBQUEsSUFBSSxnQ0FBZ0M7WUFBQSxPQUFNLENBQU87UUFBQSxDQUFDO0tBQUM7Q0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcc3JjXFxpY29uc1xcSWNvbkJhZGdlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVSZWFjdENvbXBvbmVudCBmcm9tICcuLi9jcmVhdGVSZWFjdENvbXBvbmVudCc7XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVSZWFjdENvbXBvbmVudCgnb3V0bGluZScsICdiYWRnZScsICdJY29uQmFkZ2UnLCBbW1wicGF0aFwiLHtcImRcIjpcIk0xNyAxN3YtMTNsLTUgM2wtNSAtM3YxM2w1IDN6XCIsXCJrZXlcIjpcInN2Zy0wXCJ9XV0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBadge.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconListDetails.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconListDetails.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconListDetails)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconListDetails = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"list-details\", \"IconListDetails\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M13 5h8\",\n            \"key\": \"svg-0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M13 9h5\",\n            \"key\": \"svg-1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M13 15h8\",\n            \"key\": \"svg-2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M13 19h5\",\n            \"key\": \"svg-3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M3 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z\",\n            \"key\": \"svg-4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M3 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z\",\n            \"key\": \"svg-5\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconListDetails.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25MaXN0RGV0YWlscy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDQSxDQUFlLDJGQUFxQixDQUFXLHlCQUFnQixrQkFBbUI7SUFBQztRQUFDLE9BQU87UUFBQTtZQUFDLElBQUksVUFBVTtZQUFBLE9BQU0sT0FBTztRQUFDO0tBQUU7SUFBQTtRQUFDLE1BQU87UUFBQSxDQUFDO1lBQUEsR0FBSTtZQUFVLEtBQU07UUFBQSxDQUFRO0tBQUE7SUFBRTtRQUFDO1FBQU87WUFBQyxLQUFJLFVBQVc7WUFBQSxPQUFNLENBQU87UUFBQSxDQUFDO0tBQUU7SUFBQTtRQUFDLE1BQU87UUFBQTtZQUFDLEdBQUk7WUFBVyxDQUFNO1FBQVE7S0FBQTtJQUFFO1FBQUM7UUFBTyxDQUFDO1lBQUEsS0FBSSxDQUErRTtZQUFBLE1BQU0sUUFBTztRQUFBLENBQUM7S0FBRTtJQUFBO1FBQUMsQ0FBTztRQUFBO1lBQUMsQ0FBSSxvRkFBZ0Y7WUFBQSxLQUFNO1FBQUEsQ0FBUTtLQUFDO0NBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHNyY1xcaWNvbnNcXEljb25MaXN0RGV0YWlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlUmVhY3RDb21wb25lbnQgZnJvbSAnLi4vY3JlYXRlUmVhY3RDb21wb25lbnQnO1xuZXhwb3J0IGRlZmF1bHQgY3JlYXRlUmVhY3RDb21wb25lbnQoJ291dGxpbmUnLCAnbGlzdC1kZXRhaWxzJywgJ0ljb25MaXN0RGV0YWlscycsIFtbXCJwYXRoXCIse1wiZFwiOlwiTTEzIDVoOFwiLFwia2V5XCI6XCJzdmctMFwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xMyA5aDVcIixcImtleVwiOlwic3ZnLTFcIn1dLFtcInBhdGhcIix7XCJkXCI6XCJNMTMgMTVoOFwiLFwia2V5XCI6XCJzdmctMlwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xMyAxOWg1XCIsXCJrZXlcIjpcInN2Zy0zXCJ9XSxbXCJwYXRoXCIse1wiZFwiOlwiTTMgNG0wIDFhMSAxIDAgMCAxIDEgLTFoNGExIDEgMCAwIDEgMSAxdjRhMSAxIDAgMCAxIC0xIDFoLTRhMSAxIDAgMCAxIC0xIC0xelwiLFwia2V5XCI6XCJzdmctNFwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0zIDE0bTAgMWExIDEgMCAwIDEgMSAtMWg0YTEgMSAwIDAgMSAxIDF2NGExIDEgMCAwIDEgLTEgMWgtNGExIDEgMCAwIDEgLTEgLTF6XCIsXCJrZXlcIjpcInN2Zy01XCJ9XV0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconListDetails.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTextSize.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconTextSize.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconTextSize)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconTextSize = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"text-size\", \"IconTextSize\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M3 7v-2h13v2\",\n            \"key\": \"svg-0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M10 5v14\",\n            \"key\": \"svg-1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 19h-4\",\n            \"key\": \"svg-2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M15 13v-1h6v1\",\n            \"key\": \"svg-3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M18 12v7\",\n            \"key\": \"svg-4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M17 19h2\",\n            \"key\": \"svg-5\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconTextSize.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25UZXh0U2l6ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDQSxDQUFlLHdGQUFxQixDQUFXLHNCQUFhLGVBQWdCO0lBQUM7UUFBQyxPQUFPO1FBQUE7WUFBQyxJQUFJLGVBQWU7WUFBQSxPQUFNLE9BQU87UUFBQztLQUFFO0lBQUE7UUFBQyxNQUFPO1FBQUEsQ0FBQztZQUFBLEdBQUk7WUFBVyxLQUFNO1FBQUEsQ0FBUTtLQUFBO0lBQUU7UUFBQztRQUFPO1lBQUMsS0FBSSxXQUFZO1lBQUEsT0FBTSxDQUFPO1FBQUEsQ0FBQztLQUFFO0lBQUE7UUFBQyxNQUFPO1FBQUE7WUFBQyxHQUFJO1lBQWdCLENBQU07UUFBUTtLQUFBO0lBQUU7UUFBQztRQUFPLENBQUM7WUFBQSxLQUFJLENBQVc7WUFBQSxNQUFNLFFBQU87UUFBQSxDQUFDO0tBQUU7SUFBQTtRQUFDLENBQU87UUFBQTtZQUFDLENBQUksZUFBVztZQUFBLEtBQU07UUFBQSxDQUFRO0tBQUM7Q0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcc3JjXFxpY29uc1xcSWNvblRleHRTaXplLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVSZWFjdENvbXBvbmVudCBmcm9tICcuLi9jcmVhdGVSZWFjdENvbXBvbmVudCc7XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVSZWFjdENvbXBvbmVudCgnb3V0bGluZScsICd0ZXh0LXNpemUnLCAnSWNvblRleHRTaXplJywgW1tcInBhdGhcIix7XCJkXCI6XCJNMyA3di0yaDEzdjJcIixcImtleVwiOlwic3ZnLTBcIn1dLFtcInBhdGhcIix7XCJkXCI6XCJNMTAgNXYxNFwiLFwia2V5XCI6XCJzdmctMVwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xMiAxOWgtNFwiLFwia2V5XCI6XCJzdmctMlwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xNSAxM3YtMWg2djFcIixcImtleVwiOlwic3ZnLTNcIn1dLFtcInBhdGhcIix7XCJkXCI6XCJNMTggMTJ2N1wiLFwia2V5XCI6XCJzdmctNFwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xNyAxOWgyXCIsXCJrZXlcIjpcInN2Zy01XCJ9XV0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTextSize.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/FicheTechniqueSupplementaire.tsx":
/*!*************************************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/FicheTechniqueSupplementaire.tsx ***!
  \*************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=IconBadge,IconList,IconListDetails,IconSettings,IconTextSize!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBadge.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconBadge,IconList,IconListDetails,IconSettings,IconTextSize!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSettings.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=IconBadge,IconList,IconListDetails,IconSettings,IconTextSize!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTextSize.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconBadge,IconList,IconListDetails,IconSettings,IconTextSize!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconListDetails.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=IconBadge,IconList,IconListDetails,IconSettings,IconTextSize!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconList.mjs\");\n/* harmony import */ var mantine_datatable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mantine-datatable */ \"(app-pages-browser)/./node_modules/mantine-datatable/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst FicheTechniqueSupplementaire = ()=>{\n    _s();\n    // États pour les onglets et données\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('listes-de-choix');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [types, setTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [listItems, setListItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Gestionnaires pour les onglets\n    const handleTabChange = (value)=>{\n        if (value && value !== 'general') {\n            setActiveTab(value);\n        }\n    };\n    // Colonnes pour la table des éléments de liste\n    const listColumns = [\n        {\n            accessor: 'nom',\n            title: 'Nom'\n        },\n        {\n            accessor: 'description',\n            title: 'Description'\n        },\n        {\n            accessor: 'cache',\n            title: 'Caché',\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                    checked: record.cache,\n                    readOnly: true,\n                    size: \"sm\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            accessor: 'actions',\n            title: '',\n            width: 120,\n            render: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Group, {\n                    gap: \"xs\",\n                    justify: \"flex-end\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-teal-500 text-white p-4 shadow-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold\",\n                            children: \"Fiche technique Suppl\\xe9mentaire\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    shadow: \"none\",\n                    padding: 0,\n                    radius: 0,\n                    withBorder: false,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                        value: activeTab,\n                        onChange: handleTabChange,\n                        variant: \"default\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Tabs.List, {\n                                className: \"border-b border-gray-200 bg-white px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Tabs.Tab, {\n                                        value: \"general\",\n                                        disabled: true,\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        className: \"opacity-50 cursor-not-allowed\",\n                                        children: \"G\\xe9n\\xe9ral\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Tabs.Tab, {\n                                        value: \"champs\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        children: \"Champs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Tabs.Tab, {\n                                        value: \"listes-de-choix\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        children: \"Listes de choix\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Tabs.Panel, {\n                                value: \"general\",\n                                pt: \"md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        c: \"dimmed\",\n                                        children: \"Cet onglet est d\\xe9sactiv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Tabs.Panel, {\n                                value: \"champs\",\n                                pt: 0,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                            size: \"lg\",\n                                            fw: 600,\n                                            mb: \"md\",\n                                            children: \"Configuration des champs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                            c: \"dimmed\",\n                                            children: \"Contenu de l'onglet Champs \\xe0 impl\\xe9menter.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Tabs.Panel, {\n                                value: \"listes-de-choix\",\n                                pt: 0,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-[calc(100vh-140px)]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-80 bg-white border-r border-gray-200 flex flex-col\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-600 text-white p-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBadge_IconList_IconListDetails_IconSettings_IconTextSize_tabler_icons_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-lg font-semibold\",\n                                                                children: \"Types\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.ScrollArea, {\n                                                    className: \"flex-1 p-4\",\n                                                    children: types.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                        c: \"dimmed\",\n                                                        size: \"sm\",\n                                                        ta: \"center\",\n                                                        mt: \"xl\",\n                                                        children: \"Aucun type disponible\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                                                        gap: \"xs\",\n                                                        children: types.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded cursor-pointer transition-colors \".concat(selectedType === type.id ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'),\n                                                                onClick: ()=>setSelectedType(type.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    size: \"sm\",\n                                                                    fw: 500,\n                                                                    children: type.nom\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                                    lineNumber: 165,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, type.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 27\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 bg-white flex flex-col\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                                        columns: listColumns,\n                                                        records: listItems,\n                                                        noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\",\n                                                        minHeight: 400,\n                                                        striped: true,\n                                                        highlightOnHover: true,\n                                                        className: \"border-0\",\n                                                        styles: {\n                                                            header: {\n                                                                backgroundColor: '#f8f9fa',\n                                                                borderBottom: '1px solid #dee2e6'\n                                                            },\n                                                            table: {\n                                                                borderCollapse: 'separate',\n                                                                borderSpacing: 0\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\FicheTechniqueSupplementaire.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FicheTechniqueSupplementaire, \"RHA88zRrAw8h3sg67MtO+3/JX7g=\");\n_c = FicheTechniqueSupplementaire;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FicheTechniqueSupplementaire);\nvar _c;\n$RefreshReg$(_c, \"FicheTechniqueSupplementaire\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/FicheTechniqueSupplementaire.tsx\n"));

/***/ })

});