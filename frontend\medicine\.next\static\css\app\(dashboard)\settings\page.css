/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@mantine/tiptap/styles.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
.m_dd3f7539 {
  position: relative;
  border: calc(0.0625rem * var(--mantine-scale)) solid;
  border-radius: var(--mantine-radius-default);
}

  :where([data-mantine-color-scheme='light']) .m_dd3f7539 {
    border-color: var(--mantine-color-gray-4);
}

  :where([data-mantine-color-scheme='dark']) .m_dd3f7539 {
    border-color: var(--mantine-color-dark-4);
}

.m_d37966d9 {
  padding: 0;
  margin: 0;
}

.m_d37966d9 li > p {
    margin: 0;
  }

.m_d37966d9 ul li,
  .m_d37966d9 ol li {
    margin-top: calc(0.125rem * var(--mantine-scale));
  }

.m_d37966d9 p {
    margin-bottom: calc(0.4375rem * var(--mantine-scale));
  }

.m_d37966d9 h1,
  .m_d37966d9 h2,
  .m_d37966d9 h3,
  .m_d37966d9 h4,
  .m_d37966d9 h5,
  .m_d37966d9 h6,
  .m_d37966d9 p {
    margin-top: 0;
  }

.m_c2204cc2 {
  background-color: var(--mantine-color-body);
  border-radius: var(--mantine-radius-default);
}

.m_c2204cc2 .ProseMirror {
    outline: 0;
    padding: var(--mantine-spacing-md);
  }

.m_c2204cc2 .ProseMirror > *:last-child {
    margin-bottom: 0;
  }

.m_c2204cc2 .ProseMirror p.is-editor-empty:first-of-type::before {
    content: attr(data-placeholder);
    pointer-events: none;
    user-select: none;
    float: left;
    height: 0;
    color: var(--mantine-color-placeholder);
  }

.m_c2204cc2 pre {
    font-family: var(--mantine-font-family-monospace);
    border-radius: var(--mantine-radius-default);
    padding: var(--mantine-spacing-sm) var(--mantine-spacing-md);
  }

:where([data-mantine-color-scheme='light']) .m_c2204cc2 pre {
      background-color: var(--mantine-color-gray-0);
      color: var(--mantine-color-gray-9);
      --code-color-comment: var(--mantine-color-gray-5);
      --code-color-var: var(--mantine-color-red-7);
      --code-color-number: var(--mantine-color-blue-7);
      --code-color-title: var(--mantine-color-pink-7);
      --code-color-keyword: var(--mantine-color-violet-7);
}

:where([data-mantine-color-scheme='dark']) .m_c2204cc2 pre {
      background-color: var(--mantine-color-dark-8);
      color: var(--mantine-color-dark-1);
      --code-color-comment: var(--mantine-color-dark-2);
      --code-color-var: var(--mantine-color-red-5);
      --code-color-number: var(--mantine-color-cyan-5);
      --code-color-title: var(--mantine-color-yellow-5);
      --code-color-keyword: var(--mantine-color-violet-3);
}

.m_c2204cc2 pre code {
      background: none !important;
      color: inherit;
      font-size: var(--mantine-font-size-sm);
      padding: 0;
    }

.m_c2204cc2 pre .hljs-comment,
    .m_c2204cc2 pre .hljs-quote {
      color: var(--code-color-comment);
    }

.m_c2204cc2 pre .hljs-variable,
    .m_c2204cc2 pre .hljs-template-variable,
    .m_c2204cc2 pre .hljs-attribute,
    .m_c2204cc2 pre .hljs-tag,
    .m_c2204cc2 pre .hljs-regexp,
    .m_c2204cc2 pre .hljs-link,
    .m_c2204cc2 pre .hljs-name,
    .m_c2204cc2 pre .hljs-selector-id,
    .m_c2204cc2 pre .hljs-selector-class {
      color: var(--code-color-var);
    }

.m_c2204cc2 pre .hljs-number,
    .m_c2204cc2 pre .hljs-meta,
    .m_c2204cc2 pre .hljs-built_in,
    .m_c2204cc2 pre .hljs-builtin-name,
    .m_c2204cc2 pre .hljs-literal,
    .m_c2204cc2 pre .hljs-type,
    .m_c2204cc2 pre .hljs-params {
      color: var(--code-color-number);
    }

.m_c2204cc2 pre .hljs-string,
    .m_c2204cc2 pre .hljs-symbol,
    .m_c2204cc2 pre .hljs-bullet {
      color: var(--code-color-var);
    }

.m_c2204cc2 pre .hljs-title,
    .m_c2204cc2 pre .hljs-section {
      color: var(--code-color-title);
    }

.m_c2204cc2 pre .hljs-keyword,
    .m_c2204cc2 pre .hljs-selector-tag {
      color: var(--code-color-keyword);
    }

.m_c2204cc2 pre .hljs-emphasis {
      font-style: italic;
    }

.m_c2204cc2 pre .hljs-strong {
      font-weight: 700;
    }

.m_8a991b4f {
  background-color: var(--mantine-color-body);
}

.m_c2207da6 {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--mantine-radius-default);
  cursor: default;
}

.m_c2207da6:where([data-variant='default']) {
    border: calc(0.0625rem * var(--mantine-scale)) solid;
    min-width: calc(1.625rem * var(--mantine-scale));
    height: calc(1.625rem * var(--mantine-scale));
  }

.m_c2207da6:where([data-variant='subtle']) {
    --control-icon-size: calc(1.25rem * var(--mantine-scale));
    min-width: calc(2rem * var(--mantine-scale));
    height: calc(2rem * var(--mantine-scale));
  }

:where([data-mantine-color-scheme='light']) .m_c2207da6 {
    background-color: var(--mantine-color-white);
    border-color: var(--mantine-color-gray-4);
    color: var(--mantine-color-gray-7);
}

:where([data-mantine-color-scheme='dark']) .m_c2207da6 {
    border-color: var(--mantine-color-dark-4);
    color: var(--mantine-color-dark-1);
}

:where([data-mantine-color-scheme='dark']) .m_c2207da6:where([data-variant='default']) {
      background-color: var(--mantine-color-dark-6);
    }

:where([data-mantine-color-scheme='dark']) .m_c2207da6:where([data-variant='subtle']) {
      background-color: var(--mantine-color-dark-7);
    }

.m_c2207da6:where([data-disabled]) {
    cursor: not-allowed;
    color: var(--mantine-color-disabled-color);
    background-color: var(--mantine-color-disabled);
  }

.m_c2207da6:where([data-interactive]:not([data-disabled])) {
    cursor: pointer;
  }

@media (hover: hover) {

  .m_c2207da6:where([data-interactive]:not([data-disabled])):hover {
      color: var(--mantine-color-bright);
  }
        :where([data-mantine-color-scheme='light']) .m_c2207da6:where([data-interactive]:not([data-disabled])):hover:where([data-variant='default']) {
          background-color: var(--mantine-color-gray-0);
        }

        :where([data-mantine-color-scheme='light']) .m_c2207da6:where([data-interactive]:not([data-disabled])):hover:where([data-variant='subtle']) {
          background-color: var(--mantine-color-gray-1);
        }

      :where([data-mantine-color-scheme='dark']) .m_c2207da6:where([data-interactive]:not([data-disabled])):hover {
        background-color: var(--mantine-color-dark-5);
  }
}

@media (hover: none) {

  .m_c2207da6:where([data-interactive]:not([data-disabled])):active {
      color: var(--mantine-color-bright);
  }
        :where([data-mantine-color-scheme='light']) .m_c2207da6:where([data-interactive]:not([data-disabled])):active:where([data-variant='default']) {
          background-color: var(--mantine-color-gray-0);
        }

        :where([data-mantine-color-scheme='light']) .m_c2207da6:where([data-interactive]:not([data-disabled])):active:where([data-variant='subtle']) {
          background-color: var(--mantine-color-gray-1);
        }

      :where([data-mantine-color-scheme='dark']) .m_c2207da6:where([data-interactive]:not([data-disabled])):active {
        background-color: var(--mantine-color-dark-5);
  }
}

.m_c2207da6:where([data-active]) {
    background-color: var(--mantine-primary-color-light);
    color: var(--mantine-primary-color-light-color);
  }

@media (hover: hover) {

  .m_c2207da6:where([data-active]):hover {
      background-color: var(--mantine-primary-color-light-hover);
  }
}

@media (hover: none) {

  .m_c2207da6:where([data-active]):active {
      background-color: var(--mantine-primary-color-light-hover);
  }
}

.m_9cdfeb3f {
  width: var(--control-icon-size, 16px);
  height: var(--control-icon-size, 16px);
}

.m_2ab47ef2 {
  display: flex;
  background-color: var(--mantine-color-body);
}

.m_2ab47ef2:where([data-variant='default']) :where([data-rich-text-editor-control]) {
      border-radius: 0;
    }

.m_2ab47ef2:where([data-variant='default']) :where([data-rich-text-editor-control]):where(:not(:last-of-type)) {
        border-inline-end-width: 0;
      }

.m_2ab47ef2:where([data-variant='default']) :where([data-rich-text-editor-control]):where(:last-of-type) {
        border-start-end-radius: var(--mantine-radius-default);
        border-end-end-radius: var(--mantine-radius-default);
      }

.m_2ab47ef2:where([data-variant='default']) :where([data-rich-text-editor-control]):where(:first-of-type) {
        border-start-start-radius: var(--mantine-radius-default);
        border-end-start-radius: var(--mantine-radius-default);
      }

.m_b67b711e {
  display: flex;
}

.m_296cf94c {
  border-start-end-radius: 0;
  border-end-end-radius: 0;
  border-inline-end: 0;
}

.m_cfef614 {
  border: calc(0.0625rem * var(--mantine-scale)) solid;
  color: var(--mantine-color-text);
  height: calc(1.5rem * var(--mantine-scale));
  width: calc(1.5rem * var(--mantine-scale));
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--mantine-radius-default);
}

:where([data-mantine-color-scheme='light']) .m_cfef614 {
    background-color: var(--mantine-color-white);
    border-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme='dark']) .m_cfef614 {
    background-color: var(--mantine-color-dark-7);
    border-color: var(--mantine-color-dark-4);
}

.m_cfef614:where([data-active]) {
    background-color: var(--mantine-primary-color-light);
    color: var(--mantine-primary-color-filled);
  }

@media (hover: hover) {

  .m_cfef614:where([data-active]):hover {
      background-color: var(--mantine-primary-color-light-hover);
  }
}

@media (hover: none) {

  .m_cfef614:where([data-active]):active {
      background-color: var(--mantine-primary-color-light-hover);
  }
}

.m_3b28e7bb {
  border-start-start-radius: 0;
  border-end-start-radius: 0;
}

.m_4574a3c4 {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--mantine-spacing-sm);
  top: var(--rte-sticky-offset, 0px);
  background-color: var(--mantine-color-body);
  z-index: 1;
  border-start-end-radius: var(--mantine-radius-default);
  border-start-start-radius: var(--mantine-radius-default);
  border-bottom: calc(0.0625rem * var(--mantine-scale)) solid;
}

.m_4574a3c4:where([data-variant='default']) {
    padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  }

.m_4574a3c4:where([data-variant='subtle']) {
    padding: calc(0.25rem * var(--mantine-scale));
    row-gap: 0;
  }

:where([data-mantine-color-scheme='light']) .m_4574a3c4 {
    border-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme='dark']) .m_4574a3c4 {
    border-color: var(--mantine-color-dark-4);
}

.m_4574a3c4:where([data-sticky]) {
    position: sticky;
  }

.m_8b44009a {
  list-style-type: none;
  padding: 0;
  padding-inline-start: 0;
}

.m_8b44009a :where(li) {
    margin: 0;
    padding: 0;
    display: flex;
  }

.m_8b44009a :where(ul) {
    margin-top: calc(0.3125rem * var(--mantine-scale));
  }

.m_8b44009a p {
    margin: 0;
    padding: 0;
  }

.m_8b44009a :where(label) {
    display: inline-block;
  }

.m_8b44009a :where(input) {
    cursor: pointer;
    appearance: none;
    width: calc(1.125rem * var(--mantine-scale));
    height: calc(1.125rem * var(--mantine-scale));
    border: calc(0.0625rem * var(--mantine-scale)) solid;
    border-radius: var(--mantine-radius-default);
    vertical-align: middle;
    position: relative;
  }

:where([data-mantine-color-scheme='light']) .m_8b44009a :where(input) {
      background-color: var(--mantine-color-white);
      border-color: var(--mantine-color-gray-4);
}

:where([data-mantine-color-scheme='dark']) .m_8b44009a :where(input) {
      background-color: var(--mantine-color-dark-6);
      border-color: var(--mantine-color-dark-4);
}

.m_8b44009a :where(input):checked {
      background-color: var(--mantine-primary-color-filled);
      border-color: var(--mantine-primary-color-filled);
    }

.m_8b44009a :where(input):checked::before {
        position: absolute;
        content: '';
        inset: 0;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAxMCA3Ij48cGF0aCBmaWxsPSIjZmZmIiBkPSJNNCA0LjU4NkwxLjcwNyAyLjI5M0ExIDEgMCAxMC4yOTMgMy43MDdsMyAzYS45OTcuOTk3IDAgMDAxLjQxNCAwbDUtNUExIDEgMCAxMDguMjkzLjI5M0w0IDQuNTg2eiIvPjwvc3ZnPg==');
        background-repeat: no-repeat;
        background-size: calc(0.625rem * var(--mantine-scale));
        background-position: center;
      }

.m_8b44009a :where(li > label) {
    margin-inline-end: var(--mantine-spacing-sm);
  }

/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/tab.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
:root {
  color-scheme: light;
  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;
}

[data-theme="light"] {
  color-scheme: light;

  --rounded-box: 1rem;
  --rounded-btn: 0.5rem;
  --rounded-badge: 1.9rem;

  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;

  --border-color: #d9dcde;
  --b1: 100% 0 0;
  --b2: 96.1151% 0 0;
}
[data-theme="dark"] {
  color-scheme: dark;
  --rounded-box: 1rem;
  --rounded-btn: 0.5rem;
  --rounded-badge: 1.9rem;
  --animation-btn: 0.25s;

  --border-btn: 1px;
  --tab-border: 1px;
  --tab-radius: 0.5rem;

  --border-color: #25292d;
  --b1: 25.3267% 0.015896 252.417568;
  --b2: 23.2607% 0.013807 253.100675;
}
@media (hover: hover) {
  .tab[disabled],
  .tab[disabled]:hover {
    cursor: not-allowed;
    color: var(--fallback-bc, oklch(var(--bc) / var(--tw-text-opacity)));
    --tw-text-opacity: 0.2;
  }
}
.tabs {
  display: grid;
  align-items: flex-end;
}

.tabs-lifted:has(.tab-content[class^="rounded-"])
  .tab:first-child:not(:is(.tab-active, [aria-selected="true"])),
.tabs-lifted:has(.tab-content[class*=" rounded-"])
  .tab:first-child:not(:is(.tab-active, [aria-selected="true"])) {
  border-bottom-color: transparent;
}

.tab {
  position: relative;
  grid-row-start: 1;
  display: inline-flex;
  height: 39.6px;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  --tab-padding: 1rem;
  --tw-text-opacity: 0.5;
  --tab-color: var(--fallback-bc, oklch(var(--bc) / 1));
  --tab-bg: var(--content-background);
  --tab-border-color: var(--border-color);
  color: var(--text-daisy);
  padding-inline-start: var(--tab-padding, 1rem);
  padding-inline-end: var(--tab-padding, 1rem);
}

.tab-content {
  grid-column-start: 1;
  grid-column-end: span 9999;
  grid-row-start: 2;
  margin-top: calc(var(--tab-border) * -1);
  display: none;
  /* border-color: transparent; */
  border-width: var(--tab-border, 0);
}

:checked + .tab-content:nth-child(2),
:is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(2) {
  border-start-start-radius: 0;
}

.tabs-lifted > .tab:focus-visible {
  border-end-end-radius: 0;
  border-end-start-radius: 0;
}

.tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ) {
  border-color: var(--fallback-bc, oklch(var(--bc) / var(--tw-border-opacity)));
  --tw-border-opacity: 1;
  --tw-text-opacity: 1;
}

.tab:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.tab:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: -5px;
}

.tab-disabled,
.tab[disabled] {
  cursor: not-allowed;
  color: var(--fallback-bc, oklch(var(--bc) / var(--tw-text-opacity)));
  --tw-text-opacity: 0.2;
}

.tabs-lifted > .tab {
  border: var(--tab-border, 1px) solid transparent;
  border-width: 0 0 var(--tab-border, 1px);
  border-start-start-radius: var(--tab-radius, 0.5rem);
  border-start-end-radius: var(--tab-radius, 0.5rem);
  border-bottom-color: var(--tab-border-color);
  padding-inline-start: var(--tab-padding, 1rem);
  padding-inline-end: var(--tab-padding, 1rem);
  padding-top: var(--tab-border, 1px);
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ),
.tabs-lifted > .tab:is(input:checked) {
  background-color: var(--mantine-color-body);
  border-width: var(--tab-border, 1px) var(--tab-border, 1px) 0;
  border-inline-start-color: var(--tab-border-color);
  border-inline-end-color: var(--tab-border-color);
  border-top-color: var(--tab-border-color);
  padding-inline-start: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));
  padding-inline-end: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));
  padding-bottom: var(--tab-border, 1px);
  padding-top: 0;
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):before,
.tabs-lifted > .tab:is(input:checked):before {
  z-index: 1;
  content: "";
  display: block;
  position: absolute;
  width: calc(100% + var(--tab-radius, 0.5rem) * 2);
  height: var(--tab-radius, 0.5rem);
  bottom: 0;
  background-size: var(--tab-radius, 0.5rem);
  background-position:
    top left,
    top right;
  background-repeat: no-repeat;
  --tab-grad: calc(69% - var(--tab-border, 1px));
  --radius-start: radial-gradient(
    circle at top left,
    transparent var(--tab-grad),
    var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
    var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),
    var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)
  );
  --radius-end: radial-gradient(
    circle at top right,
    transparent var(--tab-grad),
    var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
    var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),
    var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)
  );
  background-image: var(--radius-start), var(--radius-end);
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):first-child:before,
.tabs-lifted > .tab:is(input:checked):first-child:before {
  background-image: var(--radius-end);
  background-position: top right;
}

[dir="rtl"]
  .tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):first-child:before,
[dir="rtl"] .tabs-lifted > .tab:is(input:checked):first-child:before {
  background-image: var(--radius-start);
  background-position: top left;
}

.tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):last-child:before,
.tabs-lifted > .tab:is(input:checked):last-child:before {
  background-image: var(--radius-start);
  background-position: top left;
}

[dir="rtl"]
  .tabs-lifted
  > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):last-child:before,
[dir="rtl"] .tabs-lifted > .tab:is(input:checked):last-child:before {
  background-image: var(--radius-end);
  background-position: top right;
}

.tabs-lifted
  > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled])
  + .tabs-lifted
  :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not(
    [disabled]
  ):before,
.tabs-lifted
  > .tab:is(input:checked)
  + .tabs-lifted
  .tab:is(input:checked):before {
  background-image: var(--radius-end);
  background-position: top right;
}

.tabs-boxed {
  border-radius: var(--rounded-btn, 0.5rem);
  --tw-bg-opacity: 1;
  background-color: var(--fallback-b2, oklch(var(--b2) / var(--tw-bg-opacity)));
  padding: 0.25rem;
}

.tabs-boxed .tab {
  border-radius: var(--rounded-btn, 0.5rem);
}

.tabs-boxed
  :is(.tab-active, [aria-selected="true"]):not(.tab-disabled):not([disabled]),
.tabs-boxed :is(input:checked) {
  --tw-bg-opacity: 1;
  background-color: var(--fallback-p, oklch(var(--p) / var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: var(--fallback-pc, oklch(var(--pc) / var(--tw-text-opacity)));
}

.tab-border-none {
  --tab-border: 0px;
}

.tab-border {
  --tab-border: 1px;
}

.tabs-md :where(.tab) {
  height: 2rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  --tab-padding: 1rem;
}

.tabs-lg :where(.tab) {
  height: 3rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 2;
  --tab-padding: 1.25rem;
}

.tabs-sm :where(.tab) {
  height: 1.5rem;
  font-size: 0.875rem;
  line-height: 0.75rem;
  --tab-padding: 0.75rem;
}

.tabs-xs :where(.tab) {
  height: 1.25rem;
  font-size: 0.75rem;
  line-height: 0.75rem;
  --tab-padding: 0.5rem;
}

.-mb-\[var\(--tab-border\)\] {
  margin-bottom: calc(var(--tab-border) * -1);
}

.\[--tab-bg\: oklch\(var\(--n\)\)\] {
  --tab-bg: oklch(var(--n));
}

.\[--tab-bg\: var\(--fallback-b1\,oklch\(var\(--b1\)\)\)\] {
  --tab-bg: var(--fallback-b1, oklch(var(--b1)));
}

.\[--tab-bg\: var\(--fallback-n\,oklch\(var\(--n\)\)\)\] {
  --tab-bg: var(--fallback-n, oklch(var(--n)));
}

.\[--tab-bg\: yellow\] {
  --tab-bg: yellow;
}

.\[--tab-border-color\: oklch\(var\(--n\)\)\] {
  --tab-border-color: oklch(var(--n));
}

.\[--tab-border-color\: orange\] {
  --tab-border-color: orange;
}

.\[--tab-border-color\: transparent\] {
  --tab-border-color: transparent;
}

.\[--tab-border-color\: var\(--fallback-n\,oklch\(var\(--n\)\)\)\] {
  --tab-border-color: var(--fallback-n, oklch(var(--n)));
}

.\[--tab-color\: var\(--fallback-nc\,oklch\(var\(--nc\)\)\)\] {
  --tab-color: var(--fallback-nc, oklch(var(--nc)));
}

.\[border-width\:var\(--tab-border\)\] {
  border-width: var(--tab-border);
}

.xl\:tabs-lg :where(.tab) {
  height: 3rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 2;
  --tab-padding: 1.25rem;
}
.tab-active {
  color: #3895c8;
  background-color: var(--content-background);
}
/* .border-base-300 {
  background-color: var(--mantine-color-body);
  border: var(--mantine-color-gray-3);
} */

.rounded-box {
  border-radius: var(--rounded-box, 1rem);
}
.tab-content {
  grid-column-start: 1;
  grid-column-end: span 9999;
  grid-row-start: 2;
  margin-top: calc(var(--tab-border) * -1);
  display: none;
  border-color: transparent;
  border-width: var(--tab-border, 0);
}
.\[border-width\:var\(--tab-border\)\] {
  border-width: var(--tab-border);
  border-color: var(--border-color);
}
.rounded-se-box {
  border-start-end-radius: var(--rounded-box, 1rem);
}
.rounded-b-box {
  border-bottom-right-radius: var(--rounded-box, 1rem);
  border-bottom-left-radius: var(--rounded-box, 1rem);
}
.overflow-x-auto {
  overflow-x: auto;
}
*:hover {
  scrollbar-color: color-mix(in oklch, currentColor 60%, transparent)
    transparent;
}
.preview {
  background-image: repeating-linear-gradient(
    45deg,
    var(--fallback-b1, oklch(var(--b1))),
    var(--fallback-b1, oklch(var(--b1))) 13px,
    var(--fallback-b2, oklch(var(--b2))) 13px,
    var(--fallback-b2, oklch(var(--b2))) 14px
  );
  background-size: 40px 40px;
}
.border-base-300 {
  --tw-border-opacity: 1;
  background-color: var(--mantine-color-body);
}
.bg-base-100 {
  --tw-bg-opacity: 1;
  background-color: var(oklch(var(--b1) / var(--tw-bg-opacity)));
}

