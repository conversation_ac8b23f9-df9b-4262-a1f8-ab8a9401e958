"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHome.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSettings.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCalendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconEdit.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst AccueilEtAgenda = ()=>{\n    _s();\n    // États pour les onglets\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    // États pour les modales\n    const [motifModalOpened, setMotifModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [agendaModalOpened, setAgendaModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMotif, setEditingMotif] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingAgenda, setEditingAgenda] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour les formulaires\n    const [motifForm, setMotifForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: '',\n        description: '',\n        duration: 15,\n        color: '#9b4d93',\n        colorRayee: '#792485',\n        agendaDefaut: '',\n        servicesDesignes: '',\n        couleurSombre: false\n    });\n    const [agendaForm, setAgendaForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        servicesDesignes: '',\n        isResource: false\n    });\n    // États pour les paramètres généraux\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        heureOuverture: '08:00',\n        heureFermeture: '20:00',\n        premierJourSemaine: 'Lundi',\n        vueDefautCalendrier: 'Semaine',\n        minimumIntervalle: '15 minutes',\n        fuseauHoraire: 'Africa/Casablanca',\n        cacherJoursFermeture: false,\n        curseurAgendaHeureCurrent: true,\n        filtre24hCabinet: true,\n        utiliserAbbreviation: false,\n        utiliserListePatients: false,\n        afficherSeulementVisitesTerminees: false\n    });\n    // Données de test pour les motifs\n    const [motifs, setMotifs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            code: 'CNS',\n            description: 'Consultation',\n            duration: 15,\n            color: '#9b4d93',\n            colorRayee: '#792485'\n        },\n        {\n            id: 2,\n            code: 'CTR',\n            description: 'Contrôle',\n            duration: 15,\n            color: '#c05889',\n            colorRayee: '#7d1868'\n        },\n        {\n            id: 3,\n            code: 'CHI',\n            description: 'Chirurgie/Paro',\n            duration: 30,\n            color: '#c14317',\n            colorRayee: '#922a12'\n        },\n        {\n            id: 4,\n            code: 'COM',\n            description: 'Composite',\n            duration: 15,\n            color: '#2836cd',\n            colorRayee: '#091486'\n        },\n        {\n            id: 5,\n            code: 'DS',\n            description: 'Dépose Sutures',\n            duration: 15,\n            color: '#2e7d32',\n            colorRayee: '#1b5e20'\n        },\n        {\n            id: 6,\n            code: 'CS',\n            description: '1er Consultation',\n            duration: 15,\n            color: '#e91e63',\n            colorRayee: '#c2185b'\n        },\n        {\n            id: 7,\n            code: 'DEV',\n            description: 'Devis',\n            duration: 15,\n            color: 'rgb(23, 160, 131)',\n            colorRayee: '#024f3f'\n        },\n        {\n            id: 8,\n            code: 'END',\n            description: 'Endodontie',\n            duration: 60,\n            color: '#a08523',\n            colorRayee: '#564507'\n        },\n        {\n            id: 9,\n            code: 'FOR',\n            description: 'Formation',\n            duration: 60,\n            color: '#982552',\n            colorRayee: '#72183c'\n        },\n        {\n            id: 10,\n            code: 'IMP',\n            description: 'Implantologie',\n            duration: 60,\n            color: '#388983',\n            colorRayee: '#26645e'\n        },\n        {\n            id: 11,\n            code: 'ORT',\n            description: 'Orthodontie',\n            duration: 30,\n            color: '#464098',\n            colorRayee: '#1a7c81'\n        },\n        {\n            id: 12,\n            code: 'CM',\n            description: 'FS Chassis/Monta',\n            duration: 15,\n            color: '#6c407a',\n            colorRayee: '#9c4168'\n        },\n        {\n            id: 13,\n            code: 'POS',\n            description: 'PA Pose',\n            duration: 15,\n            color: '#b25953',\n            colorRayee: '#9c3e3e'\n        },\n        {\n            id: 14,\n            code: 'EMP',\n            description: 'PC TP EMP',\n            duration: 30,\n            color: '#4c8d57',\n            colorRayee: '#366e42'\n        },\n        {\n            id: 15,\n            code: 'ARM',\n            description: 'PC ESS Armature',\n            duration: 30,\n            color: '#3291ef',\n            colorRayee: '#0d667b'\n        },\n        {\n            id: 16,\n            code: 'SCE',\n            description: 'PC Scellement',\n            duration: 15,\n            color: '#c2185b',\n            colorRayee: '#880e4f'\n        },\n        {\n            id: 17,\n            code: 'PRO',\n            description: 'Prophylaxie',\n            duration: 15,\n            color: '#009688',\n            colorRayee: '#00695c'\n        }\n    ]);\n    // Données de test pour les agendas\n    const [agendas, setAgendas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // États pour la recherche\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Filtrer les motifs selon la recherche\n    const filteredMotifs = motifs.filter((motif)=>motif.code.toLowerCase().includes(searchTerm.toLowerCase()) || motif.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    // Jours de la semaine\n    const joursSemaine = [\n        'Lundi',\n        'Mardi',\n        'Mercredi',\n        'Jeudi',\n        'Vendredi',\n        'Samedi',\n        'Dimanche'\n    ];\n    // Options pour les selects\n    const vueCalendrierOptions = [\n        {\n            value: 'Semaine',\n            label: 'Semaine'\n        },\n        {\n            value: 'Mois',\n            label: 'Mois'\n        },\n        {\n            value: 'Jour',\n            label: 'Jour'\n        }\n    ];\n    const intervalleOptions = [\n        {\n            value: '5 minutes',\n            label: '5 minutes'\n        },\n        {\n            value: '10 minutes',\n            label: '10 minutes'\n        },\n        {\n            value: '15 minutes',\n            label: '15 minutes'\n        },\n        {\n            value: '30 minutes',\n            label: '30 minutes'\n        }\n    ];\n    const fuseauHoraireOptions = [\n        {\n            value: 'Africa/Casablanca',\n            label: 'Africa/Casablanca'\n        },\n        {\n            value: 'Europe/Paris',\n            label: 'Europe/Paris'\n        },\n        {\n            value: 'UTC',\n            label: 'UTC'\n        }\n    ];\n    const agendaDefautOptions = [\n        {\n            value: 'agenda1',\n            label: 'Agenda par défaut'\n        },\n        {\n            value: 'agenda2',\n            label: 'Agenda 2'\n        }\n    ];\n    const servicesDesignesOptions = [\n        {\n            value: 'service1',\n            label: 'Services désignés'\n        },\n        {\n            value: 'service2',\n            label: 'Service 2'\n        }\n    ];\n    // Fonctions pour gérer les modales\n    const openMotifModal = (motif)=>{\n        if (motif) {\n            setEditingMotif(motif);\n            setMotifForm({\n                code: motif.code,\n                description: motif.description,\n                duration: motif.duration,\n                color: motif.color,\n                colorRayee: motif.colorRayee,\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        } else {\n            setEditingMotif(null);\n            setMotifForm({\n                code: '',\n                description: '',\n                duration: 15,\n                color: '#9b4d93',\n                colorRayee: '#792485',\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        }\n        setMotifModalOpened(true);\n    };\n    const openAgendaModal = (agenda)=>{\n        if (agenda) {\n            setEditingAgenda(agenda);\n            setAgendaForm({\n                name: agenda.name,\n                description: agenda.description,\n                color: agenda.color,\n                servicesDesignes: '',\n                isResource: agenda.isResource\n            });\n        } else {\n            setEditingAgenda(null);\n            setAgendaForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                servicesDesignes: '',\n                isResource: false\n            });\n        }\n        setAgendaModalOpened(true);\n    };\n    // Fonctions pour sauvegarder\n    const handleSaveMotif = ()=>{\n        if (editingMotif) {\n            // Modifier un motif existant\n            setMotifs((prev)=>prev.map((m)=>m.id === editingMotif.id ? {\n                        ...m,\n                        ...motifForm\n                    } : m));\n        } else {\n            // Ajouter un nouveau motif\n            const newMotif = {\n                id: Date.now(),\n                code: motifForm.code,\n                description: motifForm.description,\n                duration: motifForm.duration,\n                color: motifForm.color,\n                colorRayee: motifForm.colorRayee\n            };\n            setMotifs((prev)=>[\n                    ...prev,\n                    newMotif\n                ]);\n        }\n        setMotifModalOpened(false);\n    };\n    const handleSaveAgenda = ()=>{\n        if (editingAgenda) {\n            // Modifier un agenda existant\n            setAgendas((prev)=>prev.map((a)=>a.id === editingAgenda.id ? {\n                        ...a,\n                        ...agendaForm\n                    } : a));\n        } else {\n            // Ajouter un nouvel agenda\n            const newAgenda = {\n                id: Date.now(),\n                name: agendaForm.name,\n                description: agendaForm.description,\n                color: agendaForm.color,\n                isResource: agendaForm.isResource\n            };\n            setAgendas((prev)=>[\n                    ...prev,\n                    newAgenda\n                ]);\n        }\n        setAgendaModalOpened(false);\n    };\n    // Fonction pour supprimer un motif\n    const handleDeleteMotif = (id)=>{\n        setMotifs((prev)=>prev.filter((m)=>m.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        gap: \"lg\",\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                order: 2,\n                className: \"text-gray-800 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24,\n                        className: \"text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Accueil et Agenda\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                value: activeTab,\n                onChange: (value)=>setActiveTab(value || 'general'),\n                variant: \"outline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.List, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"general\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"G\\xe9n\\xe9ral\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"motifs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Motifs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"code-couleurs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 56\n                                }, void 0),\n                                children: \"Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"agendas\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"Agendas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"salles\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"evenements\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 53\n                                }, void 0),\n                                children: \"\\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"general\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                gap: \"lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Horaires du centre\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure d'ouverture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureOuverture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureOuverture: e.target.value\n                                                                        })),\n                                                                placeholder: \"08:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure de fermeture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureFermeture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureFermeture: e.target.value\n                                                                        })),\n                                                                placeholder: \"20:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                        size: \"sm\",\n                                                        fw: 500,\n                                                        mb: \"xs\",\n                                                        children: \"Jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-wrap\",\n                                                        children: joursSemaine.map((jour)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                variant: \"light\",\n                                                                color: \"blue\",\n                                                                className: \"cursor-pointer\",\n                                                                children: jour\n                                                            }, jour, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Param\\xe8tres d'affichage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Premier jour de la semaine\",\n                                                            value: generalSettings.premierJourSemaine,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        premierJourSemaine: value || 'Lundi'\n                                                                    })),\n                                                            data: joursSemaine.map((jour)=>({\n                                                                    value: jour,\n                                                                    label: jour\n                                                                }))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Vue par d\\xe9faut du calendrier\",\n                                                            value: generalSettings.vueDefautCalendrier,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        vueDefautCalendrier: value || 'Semaine'\n                                                                    })),\n                                                            data: vueCalendrierOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Minimum intervalle de temps\",\n                                                            value: generalSettings.minimumIntervalle,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        minimumIntervalle: value || '15 minutes'\n                                                                    })),\n                                                            data: intervalleOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Fuseau horaire\",\n                                                            value: generalSettings.fuseauHoraire,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        fuseauHoraire: value || 'Africa/Casablanca'\n                                                                    })),\n                                                            data: fuseauHoraireOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                                gap: \"xs\",\n                                                mt: \"md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.cacherJoursFermeture,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    cacherJoursFermeture: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Cacher les jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.curseurAgendaHeureCurrent,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    curseurAgendaHeureCurrent: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Curseur d'agenda \\xe0 l'heure current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.filtre24hCabinet,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    filtre24hCabinet: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Filtre de 24h du cabinet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserAbbreviation,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserAbbreviation: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser abr\\xe9viation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserListePatients,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserListePatients: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser la liste des patients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.afficherSeulementVisitesTerminees,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    afficherSeulementVisitesTerminees: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Afficher seulement les visites termin\\xe9es dans 'Historique journalier'\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Rappel de rendez-vous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Dur\\xe9e avant RDV\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"5\",\n                                                            data: [\n                                                                {\n                                                                    value: '5',\n                                                                    label: 'Modèle SMS'\n                                                                },\n                                                                {\n                                                                    value: '10',\n                                                                    label: '10 minutes'\n                                                                },\n                                                                {\n                                                                    value: '15',\n                                                                    label: '15 minutes'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"Mod\\xe8le Email\",\n                                                            data: [\n                                                                {\n                                                                    value: 'email1',\n                                                                    label: 'Modèle Email'\n                                                                },\n                                                                {\n                                                                    value: 'email2',\n                                                                    label: 'Email 2'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"motifs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                placeholder: \"Rechercher\",\n                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openMotifModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Ajouter nouveau motif\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Motif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Dur\\xe9e (min)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur ray\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: filteredMotifs.map((motif)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: motif.code\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 567,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.colorRayee,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.colorRayee\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openMotifModal(motif),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 585,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 580,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                onClick: ()=>handleDeleteMotif(motif.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 594,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 589,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, motif.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"code-couleurs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 608,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"agendas\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"Agendas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openAgendaModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Nouvel agenda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Ressource\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: agendas.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                        colSpan: 5,\n                                                        className: \"text-center text-gray-500 py-8\",\n                                                        children: \"Aucun agenda trouv\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 21\n                                                }, undefined) : agendas.map((agenda)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: agenda.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: agenda.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: agenda.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: agenda.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 656,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                    color: agenda.isResource ? 'green' : 'gray',\n                                                                    children: agenda.isResource ? 'Oui' : 'Non'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                        label: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"blue\",\n                                                                            onClick: ()=>openAgendaModal(agenda),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 672,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 667,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 666,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, agenda.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"salles\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"evenements\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour \\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.Modal, {\n                opened: motifModalOpened,\n                onClose: ()=>setMotifModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 707,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 706,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Code \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 35\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.code,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Description \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.description,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Dur\\xe9e (min) \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.duration.toString(),\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    duration: parseInt(e.target.value) || 15\n                                                })),\n                                        type: \"number\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 745,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur ray\\xe9e\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.colorRayee,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Agenda par d\\xe9faut\",\n                                        value: motifForm.agendaDefaut,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    agendaDefaut: value || ''\n                                                })),\n                                        data: agendaDefautOptions,\n                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 16,\n                                            className: \"cursor-pointer text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 31\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: motifForm.servicesDesignes,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Switch, {\n                                    checked: motifForm.couleurSombre,\n                                    onChange: (e)=>setMotifForm((prev)=>({\n                                                ...prev,\n                                                couleurSombre: e.currentTarget.checked\n                                            })),\n                                    label: \"Couleur sombre\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setMotifModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveMotif,\n                                            disabled: !motifForm.code || !motifForm.description,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 714,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 702,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.Modal, {\n                opened: agendaModalOpened,\n                onClose: ()=>setAgendaModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Agenda\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 826,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 824,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Nom \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 34\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: agendaForm.name,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 834,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: agendaForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: \"Description\",\n                                        value: agendaForm.description,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: agendaForm.servicesDesignes,\n                                        onChange: (value)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 856,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Switch, {\n                                    checked: agendaForm.isResource,\n                                    onChange: (e)=>setAgendaForm((prev)=>({\n                                                ...prev,\n                                                isResource: e.currentTarget.checked\n                                            })),\n                                    label: \"Afficher comme ressource\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setAgendaModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 883,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveAgenda,\n                                            disabled: !agendaForm.name,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 874,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 832,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 820,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AccueilEtAgenda, \"YSOJ0jA1GHUJCq2LwZ+lruLeNdc=\");\n_c = AccueilEtAgenda;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AccueilEtAgenda);\nvar _c;\n$RefreshReg$(_c, \"AccueilEtAgenda\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx\n"));

/***/ })

});