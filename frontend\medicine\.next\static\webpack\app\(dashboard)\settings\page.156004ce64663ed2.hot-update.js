"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHome.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSettings.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCalendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconEdit.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst AccueilEtAgenda = ()=>{\n    _s();\n    // États pour les onglets\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    // États pour les modales\n    const [motifModalOpened, setMotifModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [agendaModalOpened, setAgendaModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [salleModalOpened, setSalleModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [evenementModalOpened, setEvenementModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMotif, setEditingMotif] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingAgenda, setEditingAgenda] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSalle, setEditingSalle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingEvenement, setEditingEvenement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour les formulaires\n    const [motifForm, setMotifForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: '',\n        description: '',\n        duration: 15,\n        color: '#9b4d93',\n        colorRayee: '#792485',\n        agendaDefaut: '',\n        servicesDesignes: '',\n        couleurSombre: false\n    });\n    const [agendaForm, setAgendaForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        servicesDesignes: '',\n        isResource: false\n    });\n    const [salleForm, setSalleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        type: 'attente',\n        capacity: 1,\n        servicesDesignes: ''\n    });\n    const [evenementForm, setEvenementForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        dateDebut: '',\n        dateFin: '',\n        color: '#9b4d93',\n        indisponible: false,\n        permanent: false,\n        touteJournee: false,\n        tousLesJours: false,\n        cabinet: false\n    });\n    // États pour les paramètres généraux\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        heureOuverture: '08:00',\n        heureFermeture: '20:00',\n        premierJourSemaine: 'Lundi',\n        vueDefautCalendrier: 'Semaine',\n        minimumIntervalle: '15 minutes',\n        fuseauHoraire: 'Africa/Casablanca',\n        cacherJoursFermeture: false,\n        curseurAgendaHeureCurrent: true,\n        filtre24hCabinet: true,\n        utiliserAbbreviation: false,\n        utiliserListePatients: false,\n        afficherSeulementVisitesTerminees: false\n    });\n    // Données de test pour les motifs\n    const [motifs, setMotifs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            code: 'CNS',\n            description: 'Consultation',\n            duration: 15,\n            color: '#9b4d93',\n            colorRayee: '#792485'\n        },\n        {\n            id: 2,\n            code: 'CTR',\n            description: 'Contrôle',\n            duration: 15,\n            color: '#c05889',\n            colorRayee: '#7d1868'\n        },\n        {\n            id: 3,\n            code: 'CHI',\n            description: 'Chirurgie/Paro',\n            duration: 30,\n            color: '#c14317',\n            colorRayee: '#922a12'\n        },\n        {\n            id: 4,\n            code: 'COM',\n            description: 'Composite',\n            duration: 15,\n            color: '#2836cd',\n            colorRayee: '#091486'\n        },\n        {\n            id: 5,\n            code: 'DS',\n            description: 'Dépose Sutures',\n            duration: 15,\n            color: '#2e7d32',\n            colorRayee: '#1b5e20'\n        },\n        {\n            id: 6,\n            code: 'CS',\n            description: '1er Consultation',\n            duration: 15,\n            color: '#e91e63',\n            colorRayee: '#c2185b'\n        },\n        {\n            id: 7,\n            code: 'DEV',\n            description: 'Devis',\n            duration: 15,\n            color: 'rgb(23, 160, 131)',\n            colorRayee: '#024f3f'\n        },\n        {\n            id: 8,\n            code: 'END',\n            description: 'Endodontie',\n            duration: 60,\n            color: '#a08523',\n            colorRayee: '#564507'\n        },\n        {\n            id: 9,\n            code: 'FOR',\n            description: 'Formation',\n            duration: 60,\n            color: '#982552',\n            colorRayee: '#72183c'\n        },\n        {\n            id: 10,\n            code: 'IMP',\n            description: 'Implantologie',\n            duration: 60,\n            color: '#388983',\n            colorRayee: '#26645e'\n        },\n        {\n            id: 11,\n            code: 'ORT',\n            description: 'Orthodontie',\n            duration: 30,\n            color: '#464098',\n            colorRayee: '#1a7c81'\n        },\n        {\n            id: 12,\n            code: 'CM',\n            description: 'FS Chassis/Monta',\n            duration: 15,\n            color: '#6c407a',\n            colorRayee: '#9c4168'\n        },\n        {\n            id: 13,\n            code: 'POS',\n            description: 'PA Pose',\n            duration: 15,\n            color: '#b25953',\n            colorRayee: '#9c3e3e'\n        },\n        {\n            id: 14,\n            code: 'EMP',\n            description: 'PC TP EMP',\n            duration: 30,\n            color: '#4c8d57',\n            colorRayee: '#366e42'\n        },\n        {\n            id: 15,\n            code: 'ARM',\n            description: 'PC ESS Armature',\n            duration: 30,\n            color: '#3291ef',\n            colorRayee: '#0d667b'\n        },\n        {\n            id: 16,\n            code: 'SCE',\n            description: 'PC Scellement',\n            duration: 15,\n            color: '#c2185b',\n            colorRayee: '#880e4f'\n        },\n        {\n            id: 17,\n            code: 'PRO',\n            description: 'Prophylaxie',\n            duration: 15,\n            color: '#009688',\n            colorRayee: '#00695c'\n        }\n    ]);\n    // Données de test pour les agendas\n    const [agendas, setAgendas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'Domicile',\n            description: '',\n            color: '#f4511e',\n            isResource: false\n        },\n        {\n            id: 2,\n            name: 'Clinique',\n            description: '',\n            color: '#43a047',\n            isResource: false\n        },\n        {\n            id: 3,\n            name: 'Cabinet',\n            description: '',\n            color: '#0097a7',\n            isResource: false\n        }\n    ]);\n    // Données de test pour les docteurs (Code couleurs médecine)\n    const [docteurs, setDocteurs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'Docteur',\n            color: '#d50000'\n        }\n    ]);\n    // Données de test pour les salles\n    const [salles, setSalles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'SLT',\n            description: 'Salle d\\'attente',\n            color: '#039be5',\n            type: 'attente',\n            capacity: 30\n        },\n        {\n            id: 2,\n            name: 'FTL',\n            description: 'Fauteuil 1',\n            color: '#ff5722',\n            type: 'consultation',\n            capacity: 1\n        },\n        {\n            id: 3,\n            name: 'FT 1',\n            description: '',\n            color: '#000000',\n            type: 'consultation',\n            capacity: 1\n        },\n        {\n            id: 4,\n            name: 'SALLE DE CONSULTATION 2',\n            description: '',\n            color: '#000000',\n            type: 'consultation',\n            capacity: 1\n        }\n    ]);\n    // Données de test pour les événements\n    const [evenements, setEvenements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: 'CONGRES',\n            medecins: 'DEMO DEMO',\n            dateDebut: '21/04/2025',\n            dateFin: '23/04/2025',\n            color: '#f52dbe',\n            indisponible: true,\n            permanent: false,\n            touteJournee: true,\n            tousLesJours: false,\n            cabinet: false\n        }\n    ]);\n    // États pour la recherche\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Filtrer les motifs selon la recherche\n    const filteredMotifs = motifs.filter((motif)=>motif.code.toLowerCase().includes(searchTerm.toLowerCase()) || motif.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    // Jours de la semaine\n    const joursSemaine = [\n        'Lundi',\n        'Mardi',\n        'Mercredi',\n        'Jeudi',\n        'Vendredi',\n        'Samedi',\n        'Dimanche'\n    ];\n    // Options pour les selects\n    const vueCalendrierOptions = [\n        {\n            value: 'Semaine',\n            label: 'Semaine'\n        },\n        {\n            value: 'Mois',\n            label: 'Mois'\n        },\n        {\n            value: 'Jour',\n            label: 'Jour'\n        }\n    ];\n    const intervalleOptions = [\n        {\n            value: '5 minutes',\n            label: '5 minutes'\n        },\n        {\n            value: '10 minutes',\n            label: '10 minutes'\n        },\n        {\n            value: '15 minutes',\n            label: '15 minutes'\n        },\n        {\n            value: '30 minutes',\n            label: '30 minutes'\n        }\n    ];\n    const fuseauHoraireOptions = [\n        {\n            value: 'Africa/Casablanca',\n            label: 'Africa/Casablanca'\n        },\n        {\n            value: 'Europe/Paris',\n            label: 'Europe/Paris'\n        },\n        {\n            value: 'UTC',\n            label: 'UTC'\n        }\n    ];\n    const agendaDefautOptions = [\n        {\n            value: 'agenda1',\n            label: 'Agenda par défaut'\n        },\n        {\n            value: 'agenda2',\n            label: 'Agenda 2'\n        }\n    ];\n    const servicesDesignesOptions = [\n        {\n            value: 'service1',\n            label: 'Services désignés'\n        },\n        {\n            value: 'service2',\n            label: 'Service 2'\n        }\n    ];\n    // Fonctions pour gérer les modales\n    const openMotifModal = (motif)=>{\n        if (motif) {\n            setEditingMotif(motif);\n            setMotifForm({\n                code: motif.code,\n                description: motif.description,\n                duration: motif.duration,\n                color: motif.color,\n                colorRayee: motif.colorRayee,\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        } else {\n            setEditingMotif(null);\n            setMotifForm({\n                code: '',\n                description: '',\n                duration: 15,\n                color: '#9b4d93',\n                colorRayee: '#792485',\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        }\n        setMotifModalOpened(true);\n    };\n    const openAgendaModal = (agenda)=>{\n        if (agenda) {\n            setEditingAgenda(agenda);\n            setAgendaForm({\n                name: agenda.name,\n                description: agenda.description,\n                color: agenda.color,\n                servicesDesignes: '',\n                isResource: agenda.isResource\n            });\n        } else {\n            setEditingAgenda(null);\n            setAgendaForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                servicesDesignes: '',\n                isResource: false\n            });\n        }\n        setAgendaModalOpened(true);\n    };\n    const openSalleModal = (salle)=>{\n        if (salle) {\n            setEditingSalle(salle);\n            setSalleForm({\n                name: salle.name,\n                description: salle.description,\n                color: salle.color,\n                type: salle.type,\n                capacity: salle.capacity,\n                servicesDesignes: ''\n            });\n        } else {\n            setEditingSalle(null);\n            setSalleForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                type: 'attente',\n                capacity: 1,\n                servicesDesignes: ''\n            });\n        }\n        setSalleModalOpened(true);\n    };\n    const openEvenementModal = (evenement)=>{\n        if (evenement) {\n            setEditingEvenement(evenement);\n            setEvenementForm({\n                title: evenement.title,\n                dateDebut: evenement.dateDebut,\n                dateFin: evenement.dateFin,\n                color: evenement.color,\n                indisponible: evenement.indisponible,\n                permanent: evenement.permanent,\n                touteJournee: evenement.touteJournee,\n                tousLesJours: evenement.tousLesJours,\n                cabinet: evenement.cabinet\n            });\n        } else {\n            setEditingEvenement(null);\n            setEvenementForm({\n                title: '',\n                dateDebut: '',\n                dateFin: '',\n                color: '#9b4d93',\n                indisponible: false,\n                permanent: false,\n                touteJournee: false,\n                tousLesJours: false,\n                cabinet: false\n            });\n        }\n        setEvenementModalOpened(true);\n    };\n    // Fonctions pour sauvegarder\n    const handleSaveMotif = ()=>{\n        if (editingMotif) {\n            // Modifier un motif existant\n            setMotifs((prev)=>prev.map((m)=>m.id === editingMotif.id ? {\n                        ...m,\n                        ...motifForm\n                    } : m));\n        } else {\n            // Ajouter un nouveau motif\n            const newMotif = {\n                id: Date.now(),\n                code: motifForm.code,\n                description: motifForm.description,\n                duration: motifForm.duration,\n                color: motifForm.color,\n                colorRayee: motifForm.colorRayee\n            };\n            setMotifs((prev)=>[\n                    ...prev,\n                    newMotif\n                ]);\n        }\n        setMotifModalOpened(false);\n    };\n    const handleSaveAgenda = ()=>{\n        if (editingAgenda) {\n            // Modifier un agenda existant\n            setAgendas((prev)=>prev.map((a)=>a.id === editingAgenda.id ? {\n                        ...a,\n                        ...agendaForm\n                    } : a));\n        } else {\n            // Ajouter un nouvel agenda\n            const newAgenda = {\n                id: Date.now(),\n                name: agendaForm.name,\n                description: agendaForm.description,\n                color: agendaForm.color,\n                isResource: agendaForm.isResource\n            };\n            setAgendas((prev)=>[\n                    ...prev,\n                    newAgenda\n                ]);\n        }\n        setAgendaModalOpened(false);\n    };\n    // Fonction pour supprimer un motif\n    const handleDeleteMotif = (id)=>{\n        setMotifs((prev)=>prev.filter((m)=>m.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        gap: \"lg\",\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                order: 2,\n                className: \"text-gray-800 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24,\n                        className: \"text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Accueil et Agenda\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                value: activeTab,\n                onChange: (value)=>setActiveTab(value || 'general'),\n                variant: \"outline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.List, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"general\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"G\\xe9n\\xe9ral\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"motifs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Motifs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"code-couleurs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 56\n                                }, void 0),\n                                children: \"Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"agendas\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"Agendas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"salles\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"evenements\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 53\n                                }, void 0),\n                                children: \"\\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"general\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                gap: \"lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Horaires du centre\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure d'ouverture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureOuverture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureOuverture: e.target.value\n                                                                        })),\n                                                                placeholder: \"08:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure de fermeture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureFermeture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureFermeture: e.target.value\n                                                                        })),\n                                                                placeholder: \"20:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                        size: \"sm\",\n                                                        fw: 500,\n                                                        mb: \"xs\",\n                                                        children: \"Jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-wrap\",\n                                                        children: joursSemaine.map((jour)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                variant: \"light\",\n                                                                color: \"blue\",\n                                                                className: \"cursor-pointer\",\n                                                                children: jour\n                                                            }, jour, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Param\\xe8tres d'affichage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Premier jour de la semaine\",\n                                                            value: generalSettings.premierJourSemaine,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        premierJourSemaine: value || 'Lundi'\n                                                                    })),\n                                                            data: joursSemaine.map((jour)=>({\n                                                                    value: jour,\n                                                                    label: jour\n                                                                }))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Vue par d\\xe9faut du calendrier\",\n                                                            value: generalSettings.vueDefautCalendrier,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        vueDefautCalendrier: value || 'Semaine'\n                                                                    })),\n                                                            data: vueCalendrierOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Minimum intervalle de temps\",\n                                                            value: generalSettings.minimumIntervalle,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        minimumIntervalle: value || '15 minutes'\n                                                                    })),\n                                                            data: intervalleOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Fuseau horaire\",\n                                                            value: generalSettings.fuseauHoraire,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        fuseauHoraire: value || 'Africa/Casablanca'\n                                                                    })),\n                                                            data: fuseauHoraireOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                                gap: \"xs\",\n                                                mt: \"md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.cacherJoursFermeture,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    cacherJoursFermeture: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Cacher les jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.curseurAgendaHeureCurrent,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    curseurAgendaHeureCurrent: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Curseur d'agenda \\xe0 l'heure current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.filtre24hCabinet,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    filtre24hCabinet: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Filtre de 24h du cabinet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserAbbreviation,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserAbbreviation: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser abr\\xe9viation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserListePatients,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserListePatients: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser la liste des patients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.afficherSeulementVisitesTerminees,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    afficherSeulementVisitesTerminees: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Afficher seulement les visites termin\\xe9es dans 'Historique journalier'\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Rappel de rendez-vous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Dur\\xe9e avant RDV\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"5\",\n                                                            data: [\n                                                                {\n                                                                    value: '5',\n                                                                    label: 'Modèle SMS'\n                                                                },\n                                                                {\n                                                                    value: '10',\n                                                                    label: '10 minutes'\n                                                                },\n                                                                {\n                                                                    value: '15',\n                                                                    label: '15 minutes'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"Mod\\xe8le Email\",\n                                                            data: [\n                                                                {\n                                                                    value: 'email1',\n                                                                    label: 'Modèle Email'\n                                                                },\n                                                                {\n                                                                    value: 'email2',\n                                                                    label: 'Email 2'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"motifs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                placeholder: \"Rechercher\",\n                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openMotifModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Ajouter nouveau motif\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Motif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Dur\\xe9e (min)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur ray\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: filteredMotifs.map((motif)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: motif.code\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 682,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.colorRayee,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.colorRayee\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 689,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 686,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openMotifModal(motif),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 700,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 695,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 694,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                onClick: ()=>handleDeleteMotif(motif.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 704,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 703,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, motif.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"code-couleurs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"agendas\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"Agendas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openAgendaModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Nouvel agenda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Ressource\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: agendas.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                        colSpan: 5,\n                                                        className: \"text-center text-gray-500 py-8\",\n                                                        children: \"Aucun agenda trouv\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 21\n                                                }, undefined) : agendas.map((agenda)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: agenda.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: agenda.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: agenda.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 770,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: agenda.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                    color: agenda.isResource ? 'green' : 'gray',\n                                                                    children: agenda.isResource ? 'Oui' : 'Non'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 775,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                        label: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"blue\",\n                                                                            onClick: ()=>openAgendaModal(agenda),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 787,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 782,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 781,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, agenda.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 731,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"salles\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 804,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 803,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 802,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"evenements\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour \\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 811,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 810,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 809,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.Modal, {\n                opened: motifModalOpened,\n                onClose: ()=>setMotifModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 822,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 823,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 821,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Code \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 35\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.code,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Description \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 841,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.description,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 839,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Dur\\xe9e (min) \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.duration.toString(),\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    duration: parseInt(e.target.value) || 15\n                                                })),\n                                        type: \"number\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 859,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur ray\\xe9e\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.colorRayee,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 871,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Agenda par d\\xe9faut\",\n                                        value: motifForm.agendaDefaut,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    agendaDefaut: value || ''\n                                                })),\n                                        data: agendaDefautOptions,\n                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 16,\n                                            className: \"cursor-pointer text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 31\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: motifForm.servicesDesignes,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 895,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 885,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Switch, {\n                                    checked: motifForm.couleurSombre,\n                                    onChange: (e)=>setMotifForm((prev)=>({\n                                                ...prev,\n                                                couleurSombre: e.currentTarget.checked\n                                            })),\n                                    label: \"Couleur sombre\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setMotifModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveMotif,\n                                            disabled: !motifForm.code || !motifForm.description,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 905,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 829,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 817,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.Modal, {\n                opened: agendaModalOpened,\n                onClose: ()=>setAgendaModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 940,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Agenda\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 941,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 939,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Nom \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 34\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: agendaForm.name,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 950,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: agendaForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 948,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: \"Description\",\n                                        value: agendaForm.description,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: agendaForm.servicesDesignes,\n                                        onChange: (value)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 980,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 979,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 971,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Switch, {\n                                    checked: agendaForm.isResource,\n                                    onChange: (e)=>setAgendaForm((prev)=>({\n                                                ...prev,\n                                                isResource: e.currentTarget.checked\n                                            })),\n                                    label: \"Afficher comme ressource\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 990,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setAgendaModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 998,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveAgenda,\n                                            disabled: !agendaForm.name,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1005,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 989,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 947,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 935,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n        lineNumber: 413,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AccueilEtAgenda, \"9TmtZyRHMHLof8M+DHEVpq/AogA=\");\n_c = AccueilEtAgenda;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AccueilEtAgenda);\nvar _c;\n$RefreshReg$(_c, \"AccueilEtAgenda\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvcGFyYW1ldGVyc19kZV9iYXNlL0FjY3VlaWxFdEFnZW5kYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdDO0FBcUJqQjtBQVVNO0FBaUQ3QixNQUFNNkIsa0JBQWtCOztJQUN0Qix5QkFBeUI7SUFDekIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUc5QiwrQ0FBUUEsQ0FBUztJQUVuRCx5QkFBeUI7SUFDekIsTUFBTSxDQUFDK0Isa0JBQWtCQyxvQkFBb0IsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ2lDLG1CQUFtQkMscUJBQXFCLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUNtQyxrQkFBa0JDLG9CQUFvQixHQUFHcEMsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDcUMsc0JBQXNCQyx3QkFBd0IsR0FBR3RDLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU0sQ0FBQ3VDLGNBQWNDLGdCQUFnQixHQUFHeEMsK0NBQVFBLENBQWU7SUFDL0QsTUFBTSxDQUFDeUMsZUFBZUMsaUJBQWlCLEdBQUcxQywrQ0FBUUEsQ0FBZ0I7SUFDbEUsTUFBTSxDQUFDMkMsY0FBY0MsZ0JBQWdCLEdBQUc1QywrQ0FBUUEsQ0FBZTtJQUMvRCxNQUFNLENBQUM2QyxrQkFBa0JDLG9CQUFvQixHQUFHOUMsK0NBQVFBLENBQW1CO0lBRTNFLDZCQUE2QjtJQUM3QixNQUFNLENBQUMrQyxXQUFXQyxhQUFhLEdBQUdoRCwrQ0FBUUEsQ0FBQztRQUN6Q2lELE1BQU07UUFDTkMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsWUFBWTtRQUNaQyxjQUFjO1FBQ2RDLGtCQUFrQjtRQUNsQkMsZUFBZTtJQUNqQjtJQUVBLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHMUQsK0NBQVFBLENBQUM7UUFDM0MyRCxNQUFNO1FBQ05ULGFBQWE7UUFDYkUsT0FBTztRQUNQRyxrQkFBa0I7UUFDbEJLLFlBQVk7SUFDZDtJQUVBLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHOUQsK0NBQVFBLENBQUM7UUFDekMyRCxNQUFNO1FBQ05ULGFBQWE7UUFDYkUsT0FBTztRQUNQVyxNQUFNO1FBQ05DLFVBQVU7UUFDVlQsa0JBQWtCO0lBQ3BCO0lBRUEsTUFBTSxDQUFDVSxlQUFlQyxpQkFBaUIsR0FBR2xFLCtDQUFRQSxDQUFDO1FBQ2pEbUUsT0FBTztRQUNQQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVGpCLE9BQU87UUFDUGtCLGNBQWM7UUFDZEMsV0FBVztRQUNYQyxjQUFjO1FBQ2RDLGNBQWM7UUFDZEMsU0FBUztJQUNYO0lBRUEscUNBQXFDO0lBQ3JDLE1BQU0sQ0FBQ0MsaUJBQWlCQyxtQkFBbUIsR0FBRzVFLCtDQUFRQSxDQUFDO1FBQ3JENkUsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLG9CQUFvQjtRQUNwQkMscUJBQXFCO1FBQ3JCQyxtQkFBbUI7UUFDbkJDLGVBQWU7UUFDZkMsc0JBQXNCO1FBQ3RCQywyQkFBMkI7UUFDM0JDLGtCQUFrQjtRQUNsQkMsc0JBQXNCO1FBQ3RCQyx1QkFBdUI7UUFDdkJDLG1DQUFtQztJQUNyQztJQUVBLGtDQUFrQztJQUNsQyxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBRzFGLCtDQUFRQSxDQUFVO1FBQzVDO1lBQUUyRixJQUFJO1lBQUcxQyxNQUFNO1lBQU9DLGFBQWE7WUFBZ0JDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDekc7WUFBRXNDLElBQUk7WUFBRzFDLE1BQU07WUFBT0MsYUFBYTtZQUFZQyxVQUFVO1lBQUlDLE9BQU87WUFBV0MsWUFBWTtRQUFVO1FBQ3JHO1lBQUVzQyxJQUFJO1lBQUcxQyxNQUFNO1lBQU9DLGFBQWE7WUFBa0JDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDM0c7WUFBRXNDLElBQUk7WUFBRzFDLE1BQU07WUFBT0MsYUFBYTtZQUFhQyxVQUFVO1lBQUlDLE9BQU87WUFBV0MsWUFBWTtRQUFVO1FBQ3RHO1lBQUVzQyxJQUFJO1lBQUcxQyxNQUFNO1lBQU1DLGFBQWE7WUFBa0JDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDMUc7WUFBRXNDLElBQUk7WUFBRzFDLE1BQU07WUFBTUMsYUFBYTtZQUFvQkMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUM1RztZQUFFc0MsSUFBSTtZQUFHMUMsTUFBTTtZQUFPQyxhQUFhO1lBQVNDLFVBQVU7WUFBSUMsT0FBTztZQUFxQkMsWUFBWTtRQUFVO1FBQzVHO1lBQUVzQyxJQUFJO1lBQUcxQyxNQUFNO1lBQU9DLGFBQWE7WUFBY0MsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUN2RztZQUFFc0MsSUFBSTtZQUFHMUMsTUFBTTtZQUFPQyxhQUFhO1lBQWFDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDdEc7WUFBRXNDLElBQUk7WUFBSTFDLE1BQU07WUFBT0MsYUFBYTtZQUFpQkMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUMzRztZQUFFc0MsSUFBSTtZQUFJMUMsTUFBTTtZQUFPQyxhQUFhO1lBQWVDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDekc7WUFBRXNDLElBQUk7WUFBSTFDLE1BQU07WUFBTUMsYUFBYTtZQUFvQkMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUM3RztZQUFFc0MsSUFBSTtZQUFJMUMsTUFBTTtZQUFPQyxhQUFhO1lBQVdDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDckc7WUFBRXNDLElBQUk7WUFBSTFDLE1BQU07WUFBT0MsYUFBYTtZQUFhQyxVQUFVO1lBQUlDLE9BQU87WUFBV0MsWUFBWTtRQUFVO1FBQ3ZHO1lBQUVzQyxJQUFJO1lBQUkxQyxNQUFNO1lBQU9DLGFBQWE7WUFBbUJDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDN0c7WUFBRXNDLElBQUk7WUFBSTFDLE1BQU07WUFBT0MsYUFBYTtZQUFpQkMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUMzRztZQUFFc0MsSUFBSTtZQUFJMUMsTUFBTTtZQUFPQyxhQUFhO1lBQWVDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7S0FDMUc7SUFFRCxtQ0FBbUM7SUFDbkMsTUFBTSxDQUFDdUMsU0FBU0MsV0FBVyxHQUFHN0YsK0NBQVFBLENBQVc7UUFDL0M7WUFBRTJGLElBQUk7WUFBR2hDLE1BQU07WUFBWVQsYUFBYTtZQUFJRSxPQUFPO1lBQVdRLFlBQVk7UUFBTTtRQUNoRjtZQUFFK0IsSUFBSTtZQUFHaEMsTUFBTTtZQUFZVCxhQUFhO1lBQUlFLE9BQU87WUFBV1EsWUFBWTtRQUFNO1FBQ2hGO1lBQUUrQixJQUFJO1lBQUdoQyxNQUFNO1lBQVdULGFBQWE7WUFBSUUsT0FBTztZQUFXUSxZQUFZO1FBQU07S0FDaEY7SUFFRCw2REFBNkQ7SUFDN0QsTUFBTSxDQUFDa0MsVUFBVUMsWUFBWSxHQUFHL0YsK0NBQVFBLENBQVk7UUFDbEQ7WUFBRTJGLElBQUk7WUFBR2hDLE1BQU07WUFBV1AsT0FBTztRQUFVO0tBQzVDO0lBRUQsa0NBQWtDO0lBQ2xDLE1BQU0sQ0FBQzRDLFFBQVFDLFVBQVUsR0FBR2pHLCtDQUFRQSxDQUFVO1FBQzVDO1lBQUUyRixJQUFJO1lBQUdoQyxNQUFNO1lBQU9ULGFBQWE7WUFBb0JFLE9BQU87WUFBV1csTUFBTTtZQUFXQyxVQUFVO1FBQUc7UUFDdkc7WUFBRTJCLElBQUk7WUFBR2hDLE1BQU07WUFBT1QsYUFBYTtZQUFjRSxPQUFPO1lBQVdXLE1BQU07WUFBZ0JDLFVBQVU7UUFBRTtRQUNyRztZQUFFMkIsSUFBSTtZQUFHaEMsTUFBTTtZQUFRVCxhQUFhO1lBQUlFLE9BQU87WUFBV1csTUFBTTtZQUFnQkMsVUFBVTtRQUFFO1FBQzVGO1lBQUUyQixJQUFJO1lBQUdoQyxNQUFNO1lBQTJCVCxhQUFhO1lBQUlFLE9BQU87WUFBV1csTUFBTTtZQUFnQkMsVUFBVTtRQUFFO0tBQ2hIO0lBRUQsc0NBQXNDO0lBQ3RDLE1BQU0sQ0FBQ2tDLFlBQVlDLGNBQWMsR0FBR25HLCtDQUFRQSxDQUFjO1FBQ3hEO1lBQ0UyRixJQUFJO1lBQ0p4QixPQUFPO1lBQ1BpQyxVQUFVO1lBQ1ZoQyxXQUFXO1lBQ1hDLFNBQVM7WUFDVGpCLE9BQU87WUFDUGtCLGNBQWM7WUFDZEMsV0FBVztZQUNYQyxjQUFjO1lBQ2RDLGNBQWM7WUFDZEMsU0FBUztRQUNYO0tBQ0Q7SUFFRCwwQkFBMEI7SUFDMUIsTUFBTSxDQUFDMkIsWUFBWUMsY0FBYyxHQUFHdEcsK0NBQVFBLENBQUM7SUFFN0Msd0NBQXdDO0lBQ3hDLE1BQU11RyxpQkFBaUJkLE9BQU9lLE1BQU0sQ0FBQ0MsQ0FBQUEsUUFDbkNBLE1BQU14RCxJQUFJLENBQUN5RCxXQUFXLEdBQUdDLFFBQVEsQ0FBQ04sV0FBV0ssV0FBVyxPQUN4REQsTUFBTXZELFdBQVcsQ0FBQ3dELFdBQVcsR0FBR0MsUUFBUSxDQUFDTixXQUFXSyxXQUFXO0lBR2pFLHNCQUFzQjtJQUN0QixNQUFNRSxlQUFlO1FBQUM7UUFBUztRQUFTO1FBQVk7UUFBUztRQUFZO1FBQVU7S0FBVztJQUU5RiwyQkFBMkI7SUFDM0IsTUFBTUMsdUJBQXVCO1FBQzNCO1lBQUVDLE9BQU87WUFBV0MsT0FBTztRQUFVO1FBQ3JDO1lBQUVELE9BQU87WUFBUUMsT0FBTztRQUFPO1FBQy9CO1lBQUVELE9BQU87WUFBUUMsT0FBTztRQUFPO0tBQ2hDO0lBRUQsTUFBTUMsb0JBQW9CO1FBQ3hCO1lBQUVGLE9BQU87WUFBYUMsT0FBTztRQUFZO1FBQ3pDO1lBQUVELE9BQU87WUFBY0MsT0FBTztRQUFhO1FBQzNDO1lBQUVELE9BQU87WUFBY0MsT0FBTztRQUFhO1FBQzNDO1lBQUVELE9BQU87WUFBY0MsT0FBTztRQUFhO0tBQzVDO0lBRUQsTUFBTUUsdUJBQXVCO1FBQzNCO1lBQUVILE9BQU87WUFBcUJDLE9BQU87UUFBb0I7UUFDekQ7WUFBRUQsT0FBTztZQUFnQkMsT0FBTztRQUFlO1FBQy9DO1lBQUVELE9BQU87WUFBT0MsT0FBTztRQUFNO0tBQzlCO0lBRUQsTUFBTUcsc0JBQXNCO1FBQzFCO1lBQUVKLE9BQU87WUFBV0MsT0FBTztRQUFvQjtRQUMvQztZQUFFRCxPQUFPO1lBQVdDLE9BQU87UUFBVztLQUN2QztJQUVELE1BQU1JLDBCQUEwQjtRQUM5QjtZQUFFTCxPQUFPO1lBQVlDLE9BQU87UUFBb0I7UUFDaEQ7WUFBRUQsT0FBTztZQUFZQyxPQUFPO1FBQVk7S0FDekM7SUFFRCxtQ0FBbUM7SUFDbkMsTUFBTUssaUJBQWlCLENBQUNYO1FBQ3RCLElBQUlBLE9BQU87WUFDVGpFLGdCQUFnQmlFO1lBQ2hCekQsYUFBYTtnQkFDWEMsTUFBTXdELE1BQU14RCxJQUFJO2dCQUNoQkMsYUFBYXVELE1BQU12RCxXQUFXO2dCQUM5QkMsVUFBVXNELE1BQU10RCxRQUFRO2dCQUN4QkMsT0FBT3FELE1BQU1yRCxLQUFLO2dCQUNsQkMsWUFBWW9ELE1BQU1wRCxVQUFVO2dCQUM1QkMsY0FBYztnQkFDZEMsa0JBQWtCO2dCQUNsQkMsZUFBZTtZQUNqQjtRQUNGLE9BQU87WUFDTGhCLGdCQUFnQjtZQUNoQlEsYUFBYTtnQkFDWEMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsVUFBVTtnQkFDVkMsT0FBTztnQkFDUEMsWUFBWTtnQkFDWkMsY0FBYztnQkFDZEMsa0JBQWtCO2dCQUNsQkMsZUFBZTtZQUNqQjtRQUNGO1FBQ0F4QixvQkFBb0I7SUFDdEI7SUFFQSxNQUFNcUYsa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUlBLFFBQVE7WUFDVjVFLGlCQUFpQjRFO1lBQ2pCNUQsY0FBYztnQkFDWkMsTUFBTTJELE9BQU8zRCxJQUFJO2dCQUNqQlQsYUFBYW9FLE9BQU9wRSxXQUFXO2dCQUMvQkUsT0FBT2tFLE9BQU9sRSxLQUFLO2dCQUNuQkcsa0JBQWtCO2dCQUNsQkssWUFBWTBELE9BQU8xRCxVQUFVO1lBQy9CO1FBQ0YsT0FBTztZQUNMbEIsaUJBQWlCO1lBQ2pCZ0IsY0FBYztnQkFDWkMsTUFBTTtnQkFDTlQsYUFBYTtnQkFDYkUsT0FBTztnQkFDUEcsa0JBQWtCO2dCQUNsQkssWUFBWTtZQUNkO1FBQ0Y7UUFDQTFCLHFCQUFxQjtJQUN2QjtJQUVBLE1BQU1xRixpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsT0FBTztZQUNUNUUsZ0JBQWdCNEU7WUFDaEIxRCxhQUFhO2dCQUNYSCxNQUFNNkQsTUFBTTdELElBQUk7Z0JBQ2hCVCxhQUFhc0UsTUFBTXRFLFdBQVc7Z0JBQzlCRSxPQUFPb0UsTUFBTXBFLEtBQUs7Z0JBQ2xCVyxNQUFNeUQsTUFBTXpELElBQUk7Z0JBQ2hCQyxVQUFVd0QsTUFBTXhELFFBQVE7Z0JBQ3hCVCxrQkFBa0I7WUFDcEI7UUFDRixPQUFPO1lBQ0xYLGdCQUFnQjtZQUNoQmtCLGFBQWE7Z0JBQ1hILE1BQU07Z0JBQ05ULGFBQWE7Z0JBQ2JFLE9BQU87Z0JBQ1BXLE1BQU07Z0JBQ05DLFVBQVU7Z0JBQ1ZULGtCQUFrQjtZQUNwQjtRQUNGO1FBQ0FuQixvQkFBb0I7SUFDdEI7SUFFQSxNQUFNcUYscUJBQXFCLENBQUNDO1FBQzFCLElBQUlBLFdBQVc7WUFDYjVFLG9CQUFvQjRFO1lBQ3BCeEQsaUJBQWlCO2dCQUNmQyxPQUFPdUQsVUFBVXZELEtBQUs7Z0JBQ3RCQyxXQUFXc0QsVUFBVXRELFNBQVM7Z0JBQzlCQyxTQUFTcUQsVUFBVXJELE9BQU87Z0JBQzFCakIsT0FBT3NFLFVBQVV0RSxLQUFLO2dCQUN0QmtCLGNBQWNvRCxVQUFVcEQsWUFBWTtnQkFDcENDLFdBQVdtRCxVQUFVbkQsU0FBUztnQkFDOUJDLGNBQWNrRCxVQUFVbEQsWUFBWTtnQkFDcENDLGNBQWNpRCxVQUFVakQsWUFBWTtnQkFDcENDLFNBQVNnRCxVQUFVaEQsT0FBTztZQUM1QjtRQUNGLE9BQU87WUFDTDVCLG9CQUFvQjtZQUNwQm9CLGlCQUFpQjtnQkFDZkMsT0FBTztnQkFDUEMsV0FBVztnQkFDWEMsU0FBUztnQkFDVGpCLE9BQU87Z0JBQ1BrQixjQUFjO2dCQUNkQyxXQUFXO2dCQUNYQyxjQUFjO2dCQUNkQyxjQUFjO2dCQUNkQyxTQUFTO1lBQ1g7UUFDRjtRQUNBcEMsd0JBQXdCO0lBQzFCO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU1xRixrQkFBa0I7UUFDdEIsSUFBSXBGLGNBQWM7WUFDaEIsNkJBQTZCO1lBQzdCbUQsVUFBVWtDLENBQUFBLE9BQVFBLEtBQUtDLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFDekJBLEVBQUVuQyxFQUFFLEtBQUtwRCxhQUFhb0QsRUFBRSxHQUNwQjt3QkFBRSxHQUFHbUMsQ0FBQzt3QkFBRSxHQUFHL0UsU0FBUztvQkFBQyxJQUNyQitFO1FBRVIsT0FBTztZQUNMLDJCQUEyQjtZQUMzQixNQUFNQyxXQUFrQjtnQkFDdEJwQyxJQUFJcUMsS0FBS0MsR0FBRztnQkFDWmhGLE1BQU1GLFVBQVVFLElBQUk7Z0JBQ3BCQyxhQUFhSCxVQUFVRyxXQUFXO2dCQUNsQ0MsVUFBVUosVUFBVUksUUFBUTtnQkFDNUJDLE9BQU9MLFVBQVVLLEtBQUs7Z0JBQ3RCQyxZQUFZTixVQUFVTSxVQUFVO1lBQ2xDO1lBQ0FxQyxVQUFVa0MsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1HO2lCQUFTO1FBQ3ZDO1FBQ0EvRixvQkFBb0I7SUFDdEI7SUFFQSxNQUFNa0csbUJBQW1CO1FBQ3ZCLElBQUl6RixlQUFlO1lBQ2pCLDhCQUE4QjtZQUM5Qm9ELFdBQVcrQixDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLENBQUNNLENBQUFBLElBQzFCQSxFQUFFeEMsRUFBRSxLQUFLbEQsY0FBY2tELEVBQUUsR0FDckI7d0JBQUUsR0FBR3dDLENBQUM7d0JBQUUsR0FBRzFFLFVBQVU7b0JBQUMsSUFDdEIwRTtRQUVSLE9BQU87WUFDTCwyQkFBMkI7WUFDM0IsTUFBTUMsWUFBb0I7Z0JBQ3hCekMsSUFBSXFDLEtBQUtDLEdBQUc7Z0JBQ1p0RSxNQUFNRixXQUFXRSxJQUFJO2dCQUNyQlQsYUFBYU8sV0FBV1AsV0FBVztnQkFDbkNFLE9BQU9LLFdBQVdMLEtBQUs7Z0JBQ3ZCUSxZQUFZSCxXQUFXRyxVQUFVO1lBQ25DO1lBQ0FpQyxXQUFXK0IsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1RO2lCQUFVO1FBQ3pDO1FBQ0FsRyxxQkFBcUI7SUFDdkI7SUFFQSxtQ0FBbUM7SUFDbkMsTUFBTW1HLG9CQUFvQixDQUFDMUM7UUFDekJELFVBQVVrQyxDQUFBQSxPQUFRQSxLQUFLcEIsTUFBTSxDQUFDc0IsQ0FBQUEsSUFBS0EsRUFBRW5DLEVBQUUsS0FBS0E7SUFDOUM7SUFFQSxxQkFDRSw4REFBQzFGLGdEQUFLQTtRQUFDcUksS0FBSTtRQUFLQyxXQUFVOzswQkFDeEIsOERBQUNySSxnREFBS0E7Z0JBQUNzSSxPQUFPO2dCQUFHRCxXQUFVOztrQ0FDekIsOERBQUM3RywyS0FBUUE7d0JBQUMrRyxNQUFNO3dCQUFJRixXQUFVOzs7Ozs7b0JBQWtCOzs7Ozs7OzBCQUlsRCw4REFBQ3BJLCtDQUFJQTtnQkFBQzJHLE9BQU9qRjtnQkFBVzZHLFVBQVUsQ0FBQzVCLFFBQVVoRixhQUFhZ0YsU0FBUztnQkFBWTZCLFNBQVE7O2tDQUNyRiw4REFBQ3hJLCtDQUFJQSxDQUFDeUksSUFBSTs7MENBQ1IsOERBQUN6SSwrQ0FBSUEsQ0FBQzBJLEdBQUc7Z0NBQUMvQixPQUFNO2dDQUFVZ0MsMkJBQWEsOERBQUNySCwyS0FBWUE7b0NBQUNnSCxNQUFNOzs7Ozs7MENBQVE7Ozs7OzswQ0FHbkUsOERBQUN0SSwrQ0FBSUEsQ0FBQzBJLEdBQUc7Z0NBQUMvQixPQUFNO2dDQUFTZ0MsMkJBQWEsOERBQUN0SCwyS0FBWUE7b0NBQUNpSCxNQUFNOzs7Ozs7MENBQVE7Ozs7OzswQ0FHbEUsOERBQUN0SSwrQ0FBSUEsQ0FBQzBJLEdBQUc7Z0NBQUMvQixPQUFNO2dDQUFnQmdDLDJCQUFhLDhEQUFDbkgsMktBQWVBO29DQUFDOEcsTUFBTTs7Ozs7OzBDQUFROzs7Ozs7MENBRzVFLDhEQUFDdEksK0NBQUlBLENBQUMwSSxHQUFHO2dDQUFDL0IsT0FBTTtnQ0FBVWdDLDJCQUFhLDhEQUFDdEgsMktBQVlBO29DQUFDaUgsTUFBTTs7Ozs7OzBDQUFROzs7Ozs7MENBR25FLDhEQUFDdEksK0NBQUlBLENBQUMwSSxHQUFHO2dDQUFDL0IsT0FBTTtnQ0FBU2dDLDJCQUFhLDhEQUFDckgsMktBQVlBO29DQUFDZ0gsTUFBTTs7Ozs7OzBDQUFROzs7Ozs7MENBR2xFLDhEQUFDdEksK0NBQUlBLENBQUMwSSxHQUFHO2dDQUFDL0IsT0FBTTtnQ0FBYWdDLDJCQUFhLDhEQUFDckgsMktBQVlBO29DQUFDZ0gsTUFBTTs7Ozs7OzBDQUFROzs7Ozs7Ozs7Ozs7a0NBTXhFLDhEQUFDdEksK0NBQUlBLENBQUM0SSxLQUFLO3dCQUFDakMsT0FBTTt3QkFBVWtDLElBQUc7a0NBQzdCLDRFQUFDNUksZ0RBQUtBOzRCQUFDNkksR0FBRTs0QkFBS0MsVUFBVTtzQ0FDdEIsNEVBQUNqSixnREFBS0E7Z0NBQUNxSSxLQUFJOztrREFFVCw4REFBQ2E7OzBEQUNDLDhEQUFDakosZ0RBQUtBO2dEQUFDc0ksT0FBTztnREFBR0QsV0FBVTswREFBc0I7Ozs7OzswREFHakQsOERBQUNsSSxnREFBSUE7O2tFQUNILDhEQUFDQSxnREFBSUEsQ0FBQytJLEdBQUc7d0RBQUNDLE1BQU07OzBFQUNkLDhEQUFDL0ksZ0RBQUlBO2dFQUFDbUksTUFBSztnRUFBS2EsSUFBSTtnRUFBS0MsSUFBRzswRUFBSzs7Ozs7OzBFQUdqQyw4REFBQ2hKLHFEQUFTQTtnRUFDUnVHLE9BQU9uQyxnQkFBZ0JFLGNBQWM7Z0VBQ3JDNkQsVUFBVSxDQUFDYyxJQUFNNUUsbUJBQW1CZ0QsQ0FBQUEsT0FBUzs0RUFDM0MsR0FBR0EsSUFBSTs0RUFDUC9DLGdCQUFnQjJFLEVBQUVDLE1BQU0sQ0FBQzNDLEtBQUs7d0VBQ2hDO2dFQUNBNEMsYUFBWTs7Ozs7Ozs7Ozs7O2tFQUdoQiw4REFBQ3JKLGdEQUFJQSxDQUFDK0ksR0FBRzt3REFBQ0MsTUFBTTs7MEVBQ2QsOERBQUMvSSxnREFBSUE7Z0VBQUNtSSxNQUFLO2dFQUFLYSxJQUFJO2dFQUFLQyxJQUFHOzBFQUFLOzs7Ozs7MEVBR2pDLDhEQUFDaEoscURBQVNBO2dFQUNSdUcsT0FBT25DLGdCQUFnQkcsY0FBYztnRUFDckM0RCxVQUFVLENBQUNjLElBQU01RSxtQkFBbUJnRCxDQUFBQSxPQUFTOzRFQUMzQyxHQUFHQSxJQUFJOzRFQUNQOUMsZ0JBQWdCMEUsRUFBRUMsTUFBTSxDQUFDM0MsS0FBSzt3RUFDaEM7Z0VBQ0E0QyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTWxCLDhEQUFDUDtnREFBSVosV0FBVTs7a0VBQ2IsOERBQUNqSSxnREFBSUE7d0RBQUNtSSxNQUFLO3dEQUFLYSxJQUFJO3dEQUFLQyxJQUFHO2tFQUFLOzs7Ozs7a0VBR2pDLDhEQUFDSjt3REFBSVosV0FBVTtrRUFDWjNCLGFBQWFpQixHQUFHLENBQUMsQ0FBQzhCLHFCQUNqQiw4REFBQ3pJLGlEQUFLQTtnRUFFSnlILFNBQVE7Z0VBQ1J2RixPQUFNO2dFQUNObUYsV0FBVTswRUFFVG9COytEQUxJQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFhZiw4REFBQ1I7OzBEQUNDLDhEQUFDakosZ0RBQUtBO2dEQUFDc0ksT0FBTztnREFBR0QsV0FBVTswREFBc0I7Ozs7OzswREFHakQsOERBQUNsSSxnREFBSUE7O2tFQUNILDhEQUFDQSxnREFBSUEsQ0FBQytJLEdBQUc7d0RBQUNDLE1BQU07a0VBQ2QsNEVBQUM3SSxrREFBTUE7NERBQ0x1RyxPQUFNOzREQUNORCxPQUFPbkMsZ0JBQWdCSSxrQkFBa0I7NERBQ3pDMkQsVUFBVSxDQUFDNUIsUUFBVWxDLG1CQUFtQmdELENBQUFBLE9BQVM7d0VBQy9DLEdBQUdBLElBQUk7d0VBQ1A3QyxvQkFBb0IrQixTQUFTO29FQUMvQjs0REFDQThDLE1BQU1oRCxhQUFhaUIsR0FBRyxDQUFDOEIsQ0FBQUEsT0FBUztvRUFBRTdDLE9BQU82QztvRUFBTTVDLE9BQU80QztnRUFBSzs7Ozs7Ozs7Ozs7a0VBRy9ELDhEQUFDdEosZ0RBQUlBLENBQUMrSSxHQUFHO3dEQUFDQyxNQUFNO2tFQUNkLDRFQUFDN0ksa0RBQU1BOzREQUNMdUcsT0FBTTs0REFDTkQsT0FBT25DLGdCQUFnQkssbUJBQW1COzREQUMxQzBELFVBQVUsQ0FBQzVCLFFBQVVsQyxtQkFBbUJnRCxDQUFBQSxPQUFTO3dFQUMvQyxHQUFHQSxJQUFJO3dFQUNQNUMscUJBQXFCOEIsU0FBUztvRUFDaEM7NERBQ0E4QyxNQUFNL0M7Ozs7Ozs7Ozs7O2tFQUdWLDhEQUFDeEcsZ0RBQUlBLENBQUMrSSxHQUFHO3dEQUFDQyxNQUFNO2tFQUNkLDRFQUFDN0ksa0RBQU1BOzREQUNMdUcsT0FBTTs0REFDTkQsT0FBT25DLGdCQUFnQk0saUJBQWlCOzREQUN4Q3lELFVBQVUsQ0FBQzVCLFFBQVVsQyxtQkFBbUJnRCxDQUFBQSxPQUFTO3dFQUMvQyxHQUFHQSxJQUFJO3dFQUNQM0MsbUJBQW1CNkIsU0FBUztvRUFDOUI7NERBQ0E4QyxNQUFNNUM7Ozs7Ozs7Ozs7O2tFQUdWLDhEQUFDM0csZ0RBQUlBLENBQUMrSSxHQUFHO3dEQUFDQyxNQUFNO2tFQUNkLDRFQUFDN0ksa0RBQU1BOzREQUNMdUcsT0FBTTs0REFDTkQsT0FBT25DLGdCQUFnQk8sYUFBYTs0REFDcEN3RCxVQUFVLENBQUM1QixRQUFVbEMsbUJBQW1CZ0QsQ0FBQUEsT0FBUzt3RUFDL0MsR0FBR0EsSUFBSTt3RUFDUDFDLGVBQWU0QixTQUFTO29FQUMxQjs0REFDQThDLE1BQU0zQzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTVosOERBQUNoSCxnREFBS0E7Z0RBQUNxSSxLQUFJO2dEQUFLdUIsSUFBRzs7a0VBQ2pCLDhEQUFDOUksb0RBQVFBO3dEQUNQK0ksU0FBU25GLGdCQUFnQlEsb0JBQW9CO3dEQUM3Q3VELFVBQVUsQ0FBQ2MsSUFBTTVFLG1CQUFtQmdELENBQUFBLE9BQVM7b0VBQzNDLEdBQUdBLElBQUk7b0VBQ1B6QyxzQkFBc0JxRSxFQUFFTyxhQUFhLENBQUNELE9BQU87Z0VBQy9DO3dEQUNBL0MsT0FBTTs7Ozs7O2tFQUVSLDhEQUFDaEcsb0RBQVFBO3dEQUNQK0ksU0FBU25GLGdCQUFnQlMseUJBQXlCO3dEQUNsRHNELFVBQVUsQ0FBQ2MsSUFBTTVFLG1CQUFtQmdELENBQUFBLE9BQVM7b0VBQzNDLEdBQUdBLElBQUk7b0VBQ1B4QywyQkFBMkJvRSxFQUFFTyxhQUFhLENBQUNELE9BQU87Z0VBQ3BEO3dEQUNBL0MsT0FBTTs7Ozs7O2tFQUVSLDhEQUFDaEcsb0RBQVFBO3dEQUNQK0ksU0FBU25GLGdCQUFnQlUsZ0JBQWdCO3dEQUN6Q3FELFVBQVUsQ0FBQ2MsSUFBTTVFLG1CQUFtQmdELENBQUFBLE9BQVM7b0VBQzNDLEdBQUdBLElBQUk7b0VBQ1B2QyxrQkFBa0JtRSxFQUFFTyxhQUFhLENBQUNELE9BQU87Z0VBQzNDO3dEQUNBL0MsT0FBTTs7Ozs7O2tFQUVSLDhEQUFDaEcsb0RBQVFBO3dEQUNQK0ksU0FBU25GLGdCQUFnQlcsb0JBQW9CO3dEQUM3Q29ELFVBQVUsQ0FBQ2MsSUFBTTVFLG1CQUFtQmdELENBQUFBLE9BQVM7b0VBQzNDLEdBQUdBLElBQUk7b0VBQ1B0QyxzQkFBc0JrRSxFQUFFTyxhQUFhLENBQUNELE9BQU87Z0VBQy9DO3dEQUNBL0MsT0FBTTs7Ozs7O2tFQUVSLDhEQUFDaEcsb0RBQVFBO3dEQUNQK0ksU0FBU25GLGdCQUFnQlkscUJBQXFCO3dEQUM5Q21ELFVBQVUsQ0FBQ2MsSUFBTTVFLG1CQUFtQmdELENBQUFBLE9BQVM7b0VBQzNDLEdBQUdBLElBQUk7b0VBQ1ByQyx1QkFBdUJpRSxFQUFFTyxhQUFhLENBQUNELE9BQU87Z0VBQ2hEO3dEQUNBL0MsT0FBTTs7Ozs7O2tFQUVSLDhEQUFDaEcsb0RBQVFBO3dEQUNQK0ksU0FBU25GLGdCQUFnQmEsaUNBQWlDO3dEQUMxRGtELFVBQVUsQ0FBQ2MsSUFBTTVFLG1CQUFtQmdELENBQUFBLE9BQVM7b0VBQzNDLEdBQUdBLElBQUk7b0VBQ1BwQyxtQ0FBbUNnRSxFQUFFTyxhQUFhLENBQUNELE9BQU87Z0VBQzVEO3dEQUNBL0MsT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU1aLDhEQUFDb0M7OzBEQUNDLDhEQUFDakosZ0RBQUtBO2dEQUFDc0ksT0FBTztnREFBR0QsV0FBVTswREFBc0I7Ozs7OzswREFHakQsOERBQUNqSSxnREFBSUE7Z0RBQUNtSSxNQUFLO2dEQUFLYSxJQUFJO2dEQUFLQyxJQUFHOzBEQUFLOzs7Ozs7MERBR2pDLDhEQUFDbEosZ0RBQUlBOztrRUFDSCw4REFBQ0EsZ0RBQUlBLENBQUMrSSxHQUFHO3dEQUFDQyxNQUFNO2tFQUNkLDRFQUFDN0ksa0RBQU1BOzREQUNMa0osYUFBWTs0REFDWkUsTUFBTTtnRUFDSjtvRUFBRTlDLE9BQU87b0VBQUtDLE9BQU87Z0VBQWE7Z0VBQ2xDO29FQUFFRCxPQUFPO29FQUFNQyxPQUFPO2dFQUFhO2dFQUNuQztvRUFBRUQsT0FBTztvRUFBTUMsT0FBTztnRUFBYTs2REFDcEM7Ozs7Ozs7Ozs7O2tFQUdMLDhEQUFDMUcsZ0RBQUlBLENBQUMrSSxHQUFHO3dEQUFDQyxNQUFNO2tFQUNkLDRFQUFDN0ksa0RBQU1BOzREQUNMa0osYUFBWTs0REFDWkUsTUFBTTtnRUFDSjtvRUFBRTlDLE9BQU87b0VBQVVDLE9BQU87Z0VBQWU7Z0VBQ3pDO29FQUFFRCxPQUFPO29FQUFVQyxPQUFPO2dFQUFVOzZEQUNyQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVVmLDhEQUFDNUcsK0NBQUlBLENBQUM0SSxLQUFLO3dCQUFDakMsT0FBTTt3QkFBU2tDLElBQUc7a0NBQzVCLDRFQUFDNUksZ0RBQUtBOzRCQUFDNkksR0FBRTs0QkFBS0MsVUFBVTs7OENBQ3RCLDhEQUFDQztvQ0FBSVosV0FBVTs7c0RBQ2IsOERBQUNZOzRDQUFJWixXQUFVO3NEQUNiLDRFQUFDaEkscURBQVNBO2dEQUNSbUosYUFBWTtnREFDWlosMkJBQWEsOERBQUN2SCw0S0FBVUE7b0RBQUNrSCxNQUFNOzs7Ozs7Z0RBQy9CM0IsT0FBT1Q7Z0RBQ1BxQyxVQUFVLENBQUNjLElBQU1sRCxjQUFja0QsRUFBRUMsTUFBTSxDQUFDM0MsS0FBSztnREFDN0N5QixXQUFVOzs7Ozs7Ozs7OztzREFHZCw4REFBQzdILGtEQUFNQTs0Q0FDTG9JLDJCQUFhLDhEQUFDMUgsNEtBQVFBO2dEQUFDcUgsTUFBTTs7Ozs7OzRDQUM3QnVCLFNBQVMsSUFBTTVDOzRDQUNmbUIsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7OzhDQUtILDhEQUFDdEgsc0RBQVVBOzhDQUNULDRFQUFDTCxpREFBS0E7d0NBQUNxSixPQUFPO3dDQUFDQyxnQkFBZ0I7d0NBQUNDLGVBQWU7OzBEQUM3Qyw4REFBQ3ZKLGlEQUFLQSxDQUFDd0osS0FBSzswREFDViw0RUFBQ3hKLGlEQUFLQSxDQUFDeUosRUFBRTs7c0VBQ1AsOERBQUN6SixpREFBS0EsQ0FBQzBKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzFKLGlEQUFLQSxDQUFDMEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDMUosaURBQUtBLENBQUMwSixFQUFFO3NFQUFDOzs7Ozs7c0VBQ1YsOERBQUMxSixpREFBS0EsQ0FBQzBKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzFKLGlEQUFLQSxDQUFDMEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDMUosaURBQUtBLENBQUMwSixFQUFFOzREQUFDQyxPQUFPO2dFQUFFQyxPQUFPOzREQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzswREFHbEMsOERBQUM1SixpREFBS0EsQ0FBQzZKLEtBQUs7MERBQ1RsRSxlQUFlc0IsR0FBRyxDQUFDLENBQUNwQixzQkFDbkIsOERBQUM3RixpREFBS0EsQ0FBQ3lKLEVBQUU7OzBFQUNQLDhEQUFDekosaURBQUtBLENBQUM4SixFQUFFOzBFQUNQLDRFQUFDcEssZ0RBQUlBO29FQUFDZ0osSUFBSTs4RUFBTTdDLE1BQU14RCxJQUFJOzs7Ozs7Ozs7OzswRUFFNUIsOERBQUNyQyxpREFBS0EsQ0FBQzhKLEVBQUU7MEVBQUVqRSxNQUFNdkQsV0FBVzs7Ozs7OzBFQUM1Qiw4REFBQ3RDLGlEQUFLQSxDQUFDOEosRUFBRTswRUFBRWpFLE1BQU10RCxRQUFROzs7Ozs7MEVBQ3pCLDhEQUFDdkMsaURBQUtBLENBQUM4SixFQUFFOzBFQUNQLDRFQUFDdkI7b0VBQUlaLFdBQVU7O3NGQUNiLDhEQUFDcEgsdURBQVdBOzRFQUFDaUMsT0FBT3FELE1BQU1yRCxLQUFLOzRFQUFFcUYsTUFBTTs7Ozs7O3NGQUN2Qyw4REFBQ25JLGdEQUFJQTs0RUFBQ21JLE1BQUs7c0ZBQU1oQyxNQUFNckQsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBR2hDLDhEQUFDeEMsaURBQUtBLENBQUM4SixFQUFFOzBFQUNQLDRFQUFDdkI7b0VBQUlaLFdBQVU7O3NGQUNiLDhEQUFDcEgsdURBQVdBOzRFQUFDaUMsT0FBT3FELE1BQU1wRCxVQUFVOzRFQUFFb0YsTUFBTTs7Ozs7O3NGQUM1Qyw4REFBQ25JLGdEQUFJQTs0RUFBQ21JLE1BQUs7c0ZBQU1oQyxNQUFNcEQsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBR3JDLDhEQUFDekMsaURBQUtBLENBQUM4SixFQUFFOzBFQUNQLDRFQUFDNUosaURBQUtBO29FQUFDd0gsS0FBSTtvRUFBS3FDLFNBQVE7O3NGQUN0Qiw4REFBQzNKLG1EQUFPQTs0RUFBQytGLE9BQU07c0ZBQ2IsNEVBQUNsRyxzREFBVUE7Z0ZBQ1Q4SCxTQUFRO2dGQUNSdkYsT0FBTTtnRkFDTjRHLFNBQVMsSUFBTTVDLGVBQWVYOzBGQUU5Qiw0RUFBQ3BGLDRLQUFRQTtvRkFBQ29ILE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBR3BCLDhEQUFDekgsbURBQU9BOzRFQUFDK0YsT0FBTTtzRkFDYiw0RUFBQ2xHLHNEQUFVQTtnRkFDVDhILFNBQVE7Z0ZBQ1J2RixPQUFNO2dGQUNONEcsU0FBUyxJQUFNM0Isa0JBQWtCNUIsTUFBTWQsRUFBRTswRkFFekMsNEVBQUNyRSw0S0FBU0E7b0ZBQUNtSCxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VEQW5DWmhDLE1BQU1kLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQWlEbkMsOERBQUN4RiwrQ0FBSUEsQ0FBQzRJLEtBQUs7d0JBQUNqQyxPQUFNO3dCQUFnQmtDLElBQUc7a0NBQ25DLDRFQUFDNUksZ0RBQUtBOzRCQUFDNkksR0FBRTs0QkFBS0MsVUFBVTtzQ0FDdEIsNEVBQUM1SSxnREFBSUE7MENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS1YsOERBQUNILCtDQUFJQSxDQUFDNEksS0FBSzt3QkFBQ2pDLE9BQU07d0JBQVVrQyxJQUFHO2tDQUM3Qiw0RUFBQzVJLGdEQUFLQTs0QkFBQzZJLEdBQUU7NEJBQUtDLFVBQVU7OzhDQUN0Qiw4REFBQ0M7b0NBQUlaLFdBQVU7O3NEQUNiLDhEQUFDckksZ0RBQUtBOzRDQUFDc0ksT0FBTztzREFBRzs7Ozs7O3NEQUNqQiw4REFBQzlILGtEQUFNQTs0Q0FDTG9JLDJCQUFhLDhEQUFDMUgsNEtBQVFBO2dEQUFDcUgsTUFBTTs7Ozs7OzRDQUM3QnVCLFNBQVMsSUFBTTNDOzRDQUNma0IsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7OzhDQUtILDhEQUFDdEgsc0RBQVVBOzhDQUNULDRFQUFDTCxpREFBS0E7d0NBQUNxSixPQUFPO3dDQUFDQyxnQkFBZ0I7d0NBQUNDLGVBQWU7OzBEQUM3Qyw4REFBQ3ZKLGlEQUFLQSxDQUFDd0osS0FBSzswREFDViw0RUFBQ3hKLGlEQUFLQSxDQUFDeUosRUFBRTs7c0VBQ1AsOERBQUN6SixpREFBS0EsQ0FBQzBKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzFKLGlEQUFLQSxDQUFDMEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDMUosaURBQUtBLENBQUMwSixFQUFFO3NFQUFDOzs7Ozs7c0VBQ1YsOERBQUMxSixpREFBS0EsQ0FBQzBKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzFKLGlEQUFLQSxDQUFDMEosRUFBRTs0REFBQ0MsT0FBTztnRUFBRUMsT0FBTzs0REFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR2xDLDhEQUFDNUosaURBQUtBLENBQUM2SixLQUFLOzBEQUNUN0UsUUFBUWdGLE1BQU0sS0FBSyxrQkFDbEIsOERBQUNoSyxpREFBS0EsQ0FBQ3lKLEVBQUU7OERBQ1AsNEVBQUN6SixpREFBS0EsQ0FBQzhKLEVBQUU7d0RBQUNHLFNBQVM7d0RBQUd0QyxXQUFVO2tFQUFpQzs7Ozs7Ozs7OztnRUFLbkUzQyxRQUFRaUMsR0FBRyxDQUFDLENBQUNQLHVCQUNYLDhEQUFDMUcsaURBQUtBLENBQUN5SixFQUFFOzswRUFDUCw4REFBQ3pKLGlEQUFLQSxDQUFDOEosRUFBRTswRUFDUCw0RUFBQ3BLLGdEQUFJQTtvRUFBQ2dKLElBQUk7OEVBQU1oQyxPQUFPM0QsSUFBSTs7Ozs7Ozs7Ozs7MEVBRTdCLDhEQUFDL0MsaURBQUtBLENBQUM4SixFQUFFOzBFQUFFcEQsT0FBT3BFLFdBQVc7Ozs7OzswRUFDN0IsOERBQUN0QyxpREFBS0EsQ0FBQzhKLEVBQUU7MEVBQ1AsNEVBQUN2QjtvRUFBSVosV0FBVTs7c0ZBQ2IsOERBQUNwSCx1REFBV0E7NEVBQUNpQyxPQUFPa0UsT0FBT2xFLEtBQUs7NEVBQUVxRixNQUFNOzs7Ozs7c0ZBQ3hDLDhEQUFDbkksZ0RBQUlBOzRFQUFDbUksTUFBSztzRkFBTW5CLE9BQU9sRSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHakMsOERBQUN4QyxpREFBS0EsQ0FBQzhKLEVBQUU7MEVBQ1AsNEVBQUN4SixpREFBS0E7b0VBQUNrQyxPQUFPa0UsT0FBTzFELFVBQVUsR0FBRyxVQUFVOzhFQUN6QzBELE9BQU8xRCxVQUFVLEdBQUcsUUFBUTs7Ozs7Ozs7Ozs7MEVBR2pDLDhEQUFDaEQsaURBQUtBLENBQUM4SixFQUFFOzBFQUNQLDRFQUFDNUosaURBQUtBO29FQUFDd0gsS0FBSTtvRUFBS3FDLFNBQVE7OEVBQ3RCLDRFQUFDM0osbURBQU9BO3dFQUFDK0YsT0FBTTtrRkFDYiw0RUFBQ2xHLHNEQUFVQTs0RUFDVDhILFNBQVE7NEVBQ1J2RixPQUFNOzRFQUNONEcsU0FBUyxJQUFNM0MsZ0JBQWdCQztzRkFFL0IsNEVBQUNqRyw0S0FBUUE7Z0ZBQUNvSCxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dURBeEJYbkIsT0FBTzNCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQXVDdEMsOERBQUN4RiwrQ0FBSUEsQ0FBQzRJLEtBQUs7d0JBQUNqQyxPQUFNO3dCQUFTa0MsSUFBRztrQ0FDNUIsNEVBQUM1SSxnREFBS0E7NEJBQUM2SSxHQUFFOzRCQUFLQyxVQUFVO3NDQUN0Qiw0RUFBQzVJLGdEQUFJQTswQ0FBQzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLViw4REFBQ0gsK0NBQUlBLENBQUM0SSxLQUFLO3dCQUFDakMsT0FBTTt3QkFBYWtDLElBQUc7a0NBQ2hDLDRFQUFDNUksZ0RBQUtBOzRCQUFDNkksR0FBRTs0QkFBS0MsVUFBVTtzQ0FDdEIsNEVBQUM1SSxnREFBSUE7MENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTVosOERBQUNLLGlEQUFLQTtnQkFDSm1LLFFBQVEvSTtnQkFDUmdKLFNBQVMsSUFBTS9JLG9CQUFvQjtnQkFDbkNtQyxxQkFDRSw4REFBQ2dGO29CQUFJWixXQUFVOztzQ0FDYiw4REFBQy9HLDJLQUFZQTs0QkFBQ2lILE1BQU07Ozs7OztzQ0FDcEIsOERBQUNuSSxnREFBSUE7NEJBQUNnSixJQUFJO3NDQUFLOzs7Ozs7Ozs7Ozs7Z0JBR25CYixNQUFLO2dCQUNMdUMsaUJBQWlCOzBCQUVqQiw0RUFBQy9LLGdEQUFLQTtvQkFBQ3FJLEtBQUk7O3NDQUNULDhEQUFDakksZ0RBQUlBOzs4Q0FDSCw4REFBQ0EsZ0RBQUlBLENBQUMrSSxHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDOUkscURBQVNBO3dDQUNSd0cscUJBQU8sOERBQUNzQzs7Z0RBQUs7OERBQUssOERBQUNBO29EQUFLZCxXQUFVOzhEQUFlOzs7Ozs7Ozs7Ozs7d0NBQ2pEekIsT0FBTy9ELFVBQVVFLElBQUk7d0NBQ3JCeUYsVUFBVSxDQUFDYyxJQUFNeEcsYUFBYTRFLENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRTNFLE1BQU11RyxFQUFFQyxNQUFNLENBQUMzQyxLQUFLO2dEQUFDO3dDQUN2RW1FLFFBQVE7Ozs7Ozs7Ozs7OzhDQUdaLDhEQUFDNUssZ0RBQUlBLENBQUMrSSxHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDOUkscURBQVNBO3dDQUNSd0cscUJBQU8sOERBQUNzQzs7Z0RBQUs7OERBQVksOERBQUNBO29EQUFLZCxXQUFVOzhEQUFlOzs7Ozs7Ozs7Ozs7d0NBQ3hEekIsT0FBTy9ELFVBQVVHLFdBQVc7d0NBQzVCd0YsVUFBVSxDQUFDYyxJQUFNeEcsYUFBYTRFLENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRTFFLGFBQWFzRyxFQUFFQyxNQUFNLENBQUMzQyxLQUFLO2dEQUFDO3dDQUM5RW1FLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtkLDhEQUFDNUssZ0RBQUlBOzs4Q0FDSCw4REFBQ0EsZ0RBQUlBLENBQUMrSSxHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDOUkscURBQVNBO3dDQUNSd0cscUJBQU8sOERBQUNzQzs7Z0RBQUs7OERBQVksOERBQUNBO29EQUFLZCxXQUFVOzhEQUFlOzs7Ozs7Ozs7Ozs7d0NBQ3hEekIsT0FBTy9ELFVBQVVJLFFBQVEsQ0FBQytILFFBQVE7d0NBQ2xDeEMsVUFBVSxDQUFDYyxJQUFNeEcsYUFBYTRFLENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRXpFLFVBQVVnSSxTQUFTM0IsRUFBRUMsTUFBTSxDQUFDM0MsS0FBSyxLQUFLO2dEQUFHO3dDQUMzRi9DLE1BQUs7d0NBQ0xrSCxRQUFROzs7Ozs7Ozs7Ozs4Q0FHWiw4REFBQzVLLGdEQUFJQSxDQUFDK0ksR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQ0Y7OzBEQUNDLDhEQUFDN0ksZ0RBQUlBO2dEQUFDbUksTUFBSztnREFBS2EsSUFBSTtnREFBS0MsSUFBRzswREFBSzs7Ozs7OzBEQUNqQyw4REFBQ0o7Z0RBQUlaLFdBQVU7MERBQ2IsNEVBQUNwSCx1REFBV0E7b0RBQ1ZpQyxPQUFPTCxVQUFVSyxLQUFLO29EQUN0QnFGLE1BQU07b0RBQ05GLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS2xCLDhEQUFDbEksZ0RBQUlBLENBQUMrSSxHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDRjs7MERBQ0MsOERBQUM3SSxnREFBSUE7Z0RBQUNtSSxNQUFLO2dEQUFLYSxJQUFJO2dEQUFLQyxJQUFHOzBEQUFLOzs7Ozs7MERBQ2pDLDhEQUFDSjtnREFBSVosV0FBVTswREFDYiw0RUFBQ3BILHVEQUFXQTtvREFDVmlDLE9BQU9MLFVBQVVNLFVBQVU7b0RBQzNCb0YsTUFBTTtvREFDTkYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPcEIsOERBQUNsSSxnREFBSUE7OzhDQUNILDhEQUFDQSxnREFBSUEsQ0FBQytJLEdBQUc7b0NBQUNDLE1BQU07OENBQ2QsNEVBQUM3SSxrREFBTUE7d0NBQ0x1RyxPQUFNO3dDQUNORCxPQUFPL0QsVUFBVU8sWUFBWTt3Q0FDN0JvRixVQUFVLENBQUM1QixRQUFVOUQsYUFBYTRFLENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRXRFLGNBQWN3RCxTQUFTO2dEQUFHO3dDQUNoRjhDLE1BQU0xQzt3Q0FDTmtFLDRCQUFjLDhEQUFDaEssNEtBQVFBOzRDQUFDcUgsTUFBTTs0Q0FBSUYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHaEQsOERBQUNsSSxnREFBSUEsQ0FBQytJLEdBQUc7b0NBQUNDLE1BQU07OENBQ2QsNEVBQUM3SSxrREFBTUE7d0NBQ0x1RyxPQUFNO3dDQUNORCxPQUFPL0QsVUFBVVEsZ0JBQWdCO3dDQUNqQ21GLFVBQVUsQ0FBQzVCLFFBQVU5RCxhQUFhNEUsQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFckUsa0JBQWtCdUQsU0FBUztnREFBRzt3Q0FDcEY4QyxNQUFNekM7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtaLDhEQUFDZ0M7NEJBQUlaLFdBQVU7OzhDQUNiLDhEQUFDOUgsa0RBQU1BO29DQUNMcUosU0FBUy9HLFVBQVVTLGFBQWE7b0NBQ2hDa0YsVUFBVSxDQUFDYyxJQUFNeEcsYUFBYTRFLENBQUFBLE9BQVM7Z0RBQUUsR0FBR0EsSUFBSTtnREFBRXBFLGVBQWVnRyxFQUFFTyxhQUFhLENBQUNELE9BQU87NENBQUM7b0NBQ3pGL0MsT0FBTTtvQ0FDTnNFLGVBQWM7Ozs7Ozs4Q0FHaEIsOERBQUN2SyxpREFBS0E7O3NEQUNKLDhEQUFDSixrREFBTUE7NENBQ0xpSSxTQUFROzRDQUNSdkYsT0FBTTs0Q0FDTjRHLFNBQVMsSUFBTWhJLG9CQUFvQjtzREFDcEM7Ozs7OztzREFHRCw4REFBQ3RCLGtEQUFNQTs0Q0FDTGlJLFNBQVE7NENBQ1J2RixPQUFNOzRDQUNONEcsU0FBU3JDOzRDQUNUMkQsVUFBVSxDQUFDdkksVUFBVUUsSUFBSSxJQUFJLENBQUNGLFVBQVVHLFdBQVc7c0RBQ3BEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTVCw4REFBQ3ZDLGlEQUFLQTtnQkFDSm1LLFFBQVE3STtnQkFDUjhJLFNBQVMsSUFBTTdJLHFCQUFxQjtnQkFDcENpQyxxQkFDRSw4REFBQ2dGO29CQUFJWixXQUFVOztzQ0FDYiw4REFBQy9HLDJLQUFZQTs0QkFBQ2lILE1BQU07Ozs7OztzQ0FDcEIsOERBQUNuSSxnREFBSUE7NEJBQUNnSixJQUFJO3NDQUFLOzs7Ozs7Ozs7Ozs7Z0JBR25CYixNQUFLO2dCQUNMdUMsaUJBQWlCOzBCQUVqQiw0RUFBQy9LLGdEQUFLQTtvQkFBQ3FJLEtBQUk7O3NDQUNULDhEQUFDakksZ0RBQUlBOzs4Q0FDSCw4REFBQ0EsZ0RBQUlBLENBQUMrSSxHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDOUkscURBQVNBO3dDQUNSd0cscUJBQU8sOERBQUNzQzs7Z0RBQUs7OERBQUksOERBQUNBO29EQUFLZCxXQUFVOzhEQUFlOzs7Ozs7Ozs7Ozs7d0NBQ2hEekIsT0FBT3JELFdBQVdFLElBQUk7d0NBQ3RCK0UsVUFBVSxDQUFDYyxJQUFNOUYsY0FBY2tFLENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRWpFLE1BQU02RixFQUFFQyxNQUFNLENBQUMzQyxLQUFLO2dEQUFDO3dDQUN4RW1FLFFBQVE7Ozs7Ozs7Ozs7OzhDQUdaLDhEQUFDNUssZ0RBQUlBLENBQUMrSSxHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDRjs7MERBQ0MsOERBQUM3SSxnREFBSUE7Z0RBQUNtSSxNQUFLO2dEQUFLYSxJQUFJO2dEQUFLQyxJQUFHOzBEQUFLOzs7Ozs7MERBQ2pDLDhEQUFDSjtnREFBSVosV0FBVTswREFDYiw0RUFBQ3BILHVEQUFXQTtvREFDVmlDLE9BQU9LLFdBQVdMLEtBQUs7b0RBQ3ZCcUYsTUFBTTtvREFDTkYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPcEIsOERBQUNsSSxnREFBSUE7OzhDQUNILDhEQUFDQSxnREFBSUEsQ0FBQytJLEdBQUc7b0NBQUNDLE1BQU07OENBQ2QsNEVBQUM5SSxxREFBU0E7d0NBQ1J3RyxPQUFNO3dDQUNORCxPQUFPckQsV0FBV1AsV0FBVzt3Q0FDN0J3RixVQUFVLENBQUNjLElBQU05RixjQUFja0UsQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFMUUsYUFBYXNHLEVBQUVDLE1BQU0sQ0FBQzNDLEtBQUs7Z0RBQUM7Ozs7Ozs7Ozs7OzhDQUduRiw4REFBQ3pHLGdEQUFJQSxDQUFDK0ksR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQzdJLGtEQUFNQTt3Q0FDTHVHLE9BQU07d0NBQ05ELE9BQU9yRCxXQUFXRixnQkFBZ0I7d0NBQ2xDbUYsVUFBVSxDQUFDNUIsUUFBVXBELGNBQWNrRSxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUVyRSxrQkFBa0J1RCxTQUFTO2dEQUFHO3dDQUNyRjhDLE1BQU16Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS1osOERBQUNnQzs0QkFBSVosV0FBVTs7OENBQ2IsOERBQUM5SCxrREFBTUE7b0NBQ0xxSixTQUFTckcsV0FBV0csVUFBVTtvQ0FDOUI4RSxVQUFVLENBQUNjLElBQU05RixjQUFja0UsQ0FBQUEsT0FBUztnREFBRSxHQUFHQSxJQUFJO2dEQUFFaEUsWUFBWTRGLEVBQUVPLGFBQWEsQ0FBQ0QsT0FBTzs0Q0FBQztvQ0FDdkYvQyxPQUFNO29DQUNOc0UsZUFBYzs7Ozs7OzhDQUdoQiw4REFBQ3ZLLGlEQUFLQTs7c0RBQ0osOERBQUNKLGtEQUFNQTs0Q0FDTGlJLFNBQVE7NENBQ1J2RixPQUFNOzRDQUNONEcsU0FBUyxJQUFNOUgscUJBQXFCO3NEQUNyQzs7Ozs7O3NEQUdELDhEQUFDeEIsa0RBQU1BOzRDQUNMaUksU0FBUTs0Q0FDUnZGLE9BQU07NENBQ040RyxTQUFTOUI7NENBQ1RvRCxVQUFVLENBQUM3SCxXQUFXRSxJQUFJO3NEQUMzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTZjtHQTE2Qk0vQjtLQUFBQTtBQTQ2Qk4saUVBQWVBLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHN2cFxcZnJvbnRlbmRcXG1lZGljaW5lXFxzcmNcXGFwcFxcKGRhc2hib2FyZClcXHNldHRpbmdzXFxwYXJhbWV0ZXJzX2RlX2Jhc2VcXEFjY3VlaWxFdEFnZW5kYS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQge1xyXG4gIFN0YWNrLFxyXG4gIFRpdGxlLFxyXG4gIFRhYnMsXHJcbiAgUGFwZXIsXHJcbiAgR3JpZCxcclxuICBUZXh0LFxyXG4gIFRleHRJbnB1dCxcclxuICBTZWxlY3QsXHJcbiAgU3dpdGNoLFxyXG4gIEJ1dHRvbixcclxuICBNb2RhbCxcclxuICBUYWJsZSxcclxuICBBY3Rpb25JY29uLFxyXG4gIEdyb3VwLFxyXG4gIENoZWNrYm94LFxyXG4gIFRvb2x0aXAsXHJcbiAgU2Nyb2xsQXJlYSxcclxuICBCYWRnZSxcclxuICBDb2xvclN3YXRjaCxcclxufSBmcm9tICdAbWFudGluZS9jb3JlJztcclxuaW1wb3J0IHtcclxuICBJY29uUGx1cyxcclxuICBJY29uRWRpdCxcclxuICBJY29uVHJhc2gsXHJcbiAgSWNvblNlYXJjaCxcclxuICBJY29uQ2FsZW5kYXIsXHJcbiAgSWNvblNldHRpbmdzLFxyXG4gIEljb25Ib21lLFxyXG4gIEljb25Db2xvclBpY2tlcixcclxufSBmcm9tICdAdGFibGVyL2ljb25zLXJlYWN0JztcclxuXHJcbi8vIFR5cGVzIHBvdXIgbGVzIGRvbm7DqWVzXHJcbmludGVyZmFjZSBNb3RpZiB7XHJcbiAgaWQ6IG51bWJlcjtcclxuICBjb2RlOiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICBkdXJhdGlvbjogbnVtYmVyO1xyXG4gIGNvbG9yOiBzdHJpbmc7XHJcbiAgY29sb3JSYXllZTogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgQWdlbmRhIHtcclxuICBpZDogbnVtYmVyO1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGNvbG9yOiBzdHJpbmc7XHJcbiAgaXNSZXNvdXJjZTogYm9vbGVhbjtcclxufVxyXG5cclxuaW50ZXJmYWNlIERvY3RldXIge1xyXG4gIGlkOiBudW1iZXI7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGNvbG9yOiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBTYWxsZSB7XHJcbiAgaWQ6IG51bWJlcjtcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgZGVzY3JpcHRpb246IHN0cmluZztcclxuICBjb2xvcjogc3RyaW5nO1xyXG4gIHR5cGU6ICdhdHRlbnRlJyB8ICdjb25zdWx0YXRpb24nO1xyXG4gIGNhcGFjaXR5OiBudW1iZXI7XHJcbn1cclxuXHJcbmludGVyZmFjZSBFdmVuZW1lbnQge1xyXG4gIGlkOiBudW1iZXI7XHJcbiAgdGl0bGU6IHN0cmluZztcclxuICBtZWRlY2luczogc3RyaW5nO1xyXG4gIGRhdGVEZWJ1dDogc3RyaW5nO1xyXG4gIGRhdGVGaW46IHN0cmluZztcclxuICBjb2xvcjogc3RyaW5nO1xyXG4gIGluZGlzcG9uaWJsZTogYm9vbGVhbjtcclxuICBwZXJtYW5lbnQ6IGJvb2xlYW47XHJcbiAgdG91dGVKb3VybmVlOiBib29sZWFuO1xyXG4gIHRvdXNMZXNKb3VyczogYm9vbGVhbjtcclxuICBjYWJpbmV0OiBib29sZWFuO1xyXG59XHJcblxyXG5jb25zdCBBY2N1ZWlsRXRBZ2VuZGEgPSAoKSA9PiB7XHJcbiAgLy8gw4l0YXRzIHBvdXIgbGVzIG9uZ2xldHNcclxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8c3RyaW5nPignZ2VuZXJhbCcpO1xyXG5cclxuICAvLyDDiXRhdHMgcG91ciBsZXMgbW9kYWxlc1xyXG4gIGNvbnN0IFttb3RpZk1vZGFsT3BlbmVkLCBzZXRNb3RpZk1vZGFsT3BlbmVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbYWdlbmRhTW9kYWxPcGVuZWQsIHNldEFnZW5kYU1vZGFsT3BlbmVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc2FsbGVNb2RhbE9wZW5lZCwgc2V0U2FsbGVNb2RhbE9wZW5lZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2V2ZW5lbWVudE1vZGFsT3BlbmVkLCBzZXRFdmVuZW1lbnRNb2RhbE9wZW5lZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2VkaXRpbmdNb3RpZiwgc2V0RWRpdGluZ01vdGlmXSA9IHVzZVN0YXRlPE1vdGlmIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2VkaXRpbmdBZ2VuZGEsIHNldEVkaXRpbmdBZ2VuZGFdID0gdXNlU3RhdGU8QWdlbmRhIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2VkaXRpbmdTYWxsZSwgc2V0RWRpdGluZ1NhbGxlXSA9IHVzZVN0YXRlPFNhbGxlIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2VkaXRpbmdFdmVuZW1lbnQsIHNldEVkaXRpbmdFdmVuZW1lbnRdID0gdXNlU3RhdGU8RXZlbmVtZW50IHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIC8vIMOJdGF0cyBwb3VyIGxlcyBmb3JtdWxhaXJlc1xyXG4gIGNvbnN0IFttb3RpZkZvcm0sIHNldE1vdGlmRm9ybV0gPSB1c2VTdGF0ZSh7XHJcbiAgICBjb2RlOiAnJyxcclxuICAgIGRlc2NyaXB0aW9uOiAnJyxcclxuICAgIGR1cmF0aW9uOiAxNSxcclxuICAgIGNvbG9yOiAnIzliNGQ5MycsXHJcbiAgICBjb2xvclJheWVlOiAnIzc5MjQ4NScsXHJcbiAgICBhZ2VuZGFEZWZhdXQ6ICcnLFxyXG4gICAgc2VydmljZXNEZXNpZ25lczogJycsXHJcbiAgICBjb3VsZXVyU29tYnJlOiBmYWxzZSxcclxuICB9KTtcclxuXHJcbiAgY29uc3QgW2FnZW5kYUZvcm0sIHNldEFnZW5kYUZvcm1dID0gdXNlU3RhdGUoe1xyXG4gICAgbmFtZTogJycsXHJcbiAgICBkZXNjcmlwdGlvbjogJycsXHJcbiAgICBjb2xvcjogJyM5YjRkOTMnLFxyXG4gICAgc2VydmljZXNEZXNpZ25lczogJycsXHJcbiAgICBpc1Jlc291cmNlOiBmYWxzZSxcclxuICB9KTtcclxuXHJcbiAgY29uc3QgW3NhbGxlRm9ybSwgc2V0U2FsbGVGb3JtXSA9IHVzZVN0YXRlKHtcclxuICAgIG5hbWU6ICcnLFxyXG4gICAgZGVzY3JpcHRpb246ICcnLFxyXG4gICAgY29sb3I6ICcjOWI0ZDkzJyxcclxuICAgIHR5cGU6ICdhdHRlbnRlJyBhcyAnYXR0ZW50ZScgfCAnY29uc3VsdGF0aW9uJyxcclxuICAgIGNhcGFjaXR5OiAxLFxyXG4gICAgc2VydmljZXNEZXNpZ25lczogJycsXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IFtldmVuZW1lbnRGb3JtLCBzZXRFdmVuZW1lbnRGb3JtXSA9IHVzZVN0YXRlKHtcclxuICAgIHRpdGxlOiAnJyxcclxuICAgIGRhdGVEZWJ1dDogJycsXHJcbiAgICBkYXRlRmluOiAnJyxcclxuICAgIGNvbG9yOiAnIzliNGQ5MycsXHJcbiAgICBpbmRpc3BvbmlibGU6IGZhbHNlLFxyXG4gICAgcGVybWFuZW50OiBmYWxzZSxcclxuICAgIHRvdXRlSm91cm5lZTogZmFsc2UsXHJcbiAgICB0b3VzTGVzSm91cnM6IGZhbHNlLFxyXG4gICAgY2FiaW5ldDogZmFsc2UsXHJcbiAgfSk7XHJcblxyXG4gIC8vIMOJdGF0cyBwb3VyIGxlcyBwYXJhbcOodHJlcyBnw6luw6lyYXV4XHJcbiAgY29uc3QgW2dlbmVyYWxTZXR0aW5ncywgc2V0R2VuZXJhbFNldHRpbmdzXSA9IHVzZVN0YXRlKHtcclxuICAgIGhldXJlT3V2ZXJ0dXJlOiAnMDg6MDAnLFxyXG4gICAgaGV1cmVGZXJtZXR1cmU6ICcyMDowMCcsXHJcbiAgICBwcmVtaWVySm91clNlbWFpbmU6ICdMdW5kaScsXHJcbiAgICB2dWVEZWZhdXRDYWxlbmRyaWVyOiAnU2VtYWluZScsXHJcbiAgICBtaW5pbXVtSW50ZXJ2YWxsZTogJzE1IG1pbnV0ZXMnLFxyXG4gICAgZnVzZWF1SG9yYWlyZTogJ0FmcmljYS9DYXNhYmxhbmNhJyxcclxuICAgIGNhY2hlckpvdXJzRmVybWV0dXJlOiBmYWxzZSxcclxuICAgIGN1cnNldXJBZ2VuZGFIZXVyZUN1cnJlbnQ6IHRydWUsXHJcbiAgICBmaWx0cmUyNGhDYWJpbmV0OiB0cnVlLFxyXG4gICAgdXRpbGlzZXJBYmJyZXZpYXRpb246IGZhbHNlLFxyXG4gICAgdXRpbGlzZXJMaXN0ZVBhdGllbnRzOiBmYWxzZSxcclxuICAgIGFmZmljaGVyU2V1bGVtZW50VmlzaXRlc1Rlcm1pbmVlczogZmFsc2UsXHJcbiAgfSk7XHJcblxyXG4gIC8vIERvbm7DqWVzIGRlIHRlc3QgcG91ciBsZXMgbW90aWZzXHJcbiAgY29uc3QgW21vdGlmcywgc2V0TW90aWZzXSA9IHVzZVN0YXRlPE1vdGlmW10+KFtcclxuICAgIHsgaWQ6IDEsIGNvZGU6ICdDTlMnLCBkZXNjcmlwdGlvbjogJ0NvbnN1bHRhdGlvbicsIGR1cmF0aW9uOiAxNSwgY29sb3I6ICcjOWI0ZDkzJywgY29sb3JSYXllZTogJyM3OTI0ODUnIH0sXHJcbiAgICB7IGlkOiAyLCBjb2RlOiAnQ1RSJywgZGVzY3JpcHRpb246ICdDb250csO0bGUnLCBkdXJhdGlvbjogMTUsIGNvbG9yOiAnI2MwNTg4OScsIGNvbG9yUmF5ZWU6ICcjN2QxODY4JyB9LFxyXG4gICAgeyBpZDogMywgY29kZTogJ0NISScsIGRlc2NyaXB0aW9uOiAnQ2hpcnVyZ2llL1Bhcm8nLCBkdXJhdGlvbjogMzAsIGNvbG9yOiAnI2MxNDMxNycsIGNvbG9yUmF5ZWU6ICcjOTIyYTEyJyB9LFxyXG4gICAgeyBpZDogNCwgY29kZTogJ0NPTScsIGRlc2NyaXB0aW9uOiAnQ29tcG9zaXRlJywgZHVyYXRpb246IDE1LCBjb2xvcjogJyMyODM2Y2QnLCBjb2xvclJheWVlOiAnIzA5MTQ4NicgfSxcclxuICAgIHsgaWQ6IDUsIGNvZGU6ICdEUycsIGRlc2NyaXB0aW9uOiAnRMOpcG9zZSBTdXR1cmVzJywgZHVyYXRpb246IDE1LCBjb2xvcjogJyMyZTdkMzInLCBjb2xvclJheWVlOiAnIzFiNWUyMCcgfSxcclxuICAgIHsgaWQ6IDYsIGNvZGU6ICdDUycsIGRlc2NyaXB0aW9uOiAnMWVyIENvbnN1bHRhdGlvbicsIGR1cmF0aW9uOiAxNSwgY29sb3I6ICcjZTkxZTYzJywgY29sb3JSYXllZTogJyNjMjE4NWInIH0sXHJcbiAgICB7IGlkOiA3LCBjb2RlOiAnREVWJywgZGVzY3JpcHRpb246ICdEZXZpcycsIGR1cmF0aW9uOiAxNSwgY29sb3I6ICdyZ2IoMjMsIDE2MCwgMTMxKScsIGNvbG9yUmF5ZWU6ICcjMDI0ZjNmJyB9LFxyXG4gICAgeyBpZDogOCwgY29kZTogJ0VORCcsIGRlc2NyaXB0aW9uOiAnRW5kb2RvbnRpZScsIGR1cmF0aW9uOiA2MCwgY29sb3I6ICcjYTA4NTIzJywgY29sb3JSYXllZTogJyM1NjQ1MDcnIH0sXHJcbiAgICB7IGlkOiA5LCBjb2RlOiAnRk9SJywgZGVzY3JpcHRpb246ICdGb3JtYXRpb24nLCBkdXJhdGlvbjogNjAsIGNvbG9yOiAnIzk4MjU1MicsIGNvbG9yUmF5ZWU6ICcjNzIxODNjJyB9LFxyXG4gICAgeyBpZDogMTAsIGNvZGU6ICdJTVAnLCBkZXNjcmlwdGlvbjogJ0ltcGxhbnRvbG9naWUnLCBkdXJhdGlvbjogNjAsIGNvbG9yOiAnIzM4ODk4MycsIGNvbG9yUmF5ZWU6ICcjMjY2NDVlJyB9LFxyXG4gICAgeyBpZDogMTEsIGNvZGU6ICdPUlQnLCBkZXNjcmlwdGlvbjogJ09ydGhvZG9udGllJywgZHVyYXRpb246IDMwLCBjb2xvcjogJyM0NjQwOTgnLCBjb2xvclJheWVlOiAnIzFhN2M4MScgfSxcclxuICAgIHsgaWQ6IDEyLCBjb2RlOiAnQ00nLCBkZXNjcmlwdGlvbjogJ0ZTIENoYXNzaXMvTW9udGEnLCBkdXJhdGlvbjogMTUsIGNvbG9yOiAnIzZjNDA3YScsIGNvbG9yUmF5ZWU6ICcjOWM0MTY4JyB9LFxyXG4gICAgeyBpZDogMTMsIGNvZGU6ICdQT1MnLCBkZXNjcmlwdGlvbjogJ1BBIFBvc2UnLCBkdXJhdGlvbjogMTUsIGNvbG9yOiAnI2IyNTk1MycsIGNvbG9yUmF5ZWU6ICcjOWMzZTNlJyB9LFxyXG4gICAgeyBpZDogMTQsIGNvZGU6ICdFTVAnLCBkZXNjcmlwdGlvbjogJ1BDIFRQIEVNUCcsIGR1cmF0aW9uOiAzMCwgY29sb3I6ICcjNGM4ZDU3JywgY29sb3JSYXllZTogJyMzNjZlNDInIH0sXHJcbiAgICB7IGlkOiAxNSwgY29kZTogJ0FSTScsIGRlc2NyaXB0aW9uOiAnUEMgRVNTIEFybWF0dXJlJywgZHVyYXRpb246IDMwLCBjb2xvcjogJyMzMjkxZWYnLCBjb2xvclJheWVlOiAnIzBkNjY3YicgfSxcclxuICAgIHsgaWQ6IDE2LCBjb2RlOiAnU0NFJywgZGVzY3JpcHRpb246ICdQQyBTY2VsbGVtZW50JywgZHVyYXRpb246IDE1LCBjb2xvcjogJyNjMjE4NWInLCBjb2xvclJheWVlOiAnIzg4MGU0ZicgfSxcclxuICAgIHsgaWQ6IDE3LCBjb2RlOiAnUFJPJywgZGVzY3JpcHRpb246ICdQcm9waHlsYXhpZScsIGR1cmF0aW9uOiAxNSwgY29sb3I6ICcjMDA5Njg4JywgY29sb3JSYXllZTogJyMwMDY5NWMnIH0sXHJcbiAgXSk7XHJcblxyXG4gIC8vIERvbm7DqWVzIGRlIHRlc3QgcG91ciBsZXMgYWdlbmRhc1xyXG4gIGNvbnN0IFthZ2VuZGFzLCBzZXRBZ2VuZGFzXSA9IHVzZVN0YXRlPEFnZW5kYVtdPihbXHJcbiAgICB7IGlkOiAxLCBuYW1lOiAnRG9taWNpbGUnLCBkZXNjcmlwdGlvbjogJycsIGNvbG9yOiAnI2Y0NTExZScsIGlzUmVzb3VyY2U6IGZhbHNlIH0sXHJcbiAgICB7IGlkOiAyLCBuYW1lOiAnQ2xpbmlxdWUnLCBkZXNjcmlwdGlvbjogJycsIGNvbG9yOiAnIzQzYTA0NycsIGlzUmVzb3VyY2U6IGZhbHNlIH0sXHJcbiAgICB7IGlkOiAzLCBuYW1lOiAnQ2FiaW5ldCcsIGRlc2NyaXB0aW9uOiAnJywgY29sb3I6ICcjMDA5N2E3JywgaXNSZXNvdXJjZTogZmFsc2UgfSxcclxuICBdKTtcclxuXHJcbiAgLy8gRG9ubsOpZXMgZGUgdGVzdCBwb3VyIGxlcyBkb2N0ZXVycyAoQ29kZSBjb3VsZXVycyBtw6lkZWNpbmUpXHJcbiAgY29uc3QgW2RvY3RldXJzLCBzZXREb2N0ZXVyc10gPSB1c2VTdGF0ZTxEb2N0ZXVyW10+KFtcclxuICAgIHsgaWQ6IDEsIG5hbWU6ICdEb2N0ZXVyJywgY29sb3I6ICcjZDUwMDAwJyB9LFxyXG4gIF0pO1xyXG5cclxuICAvLyBEb25uw6llcyBkZSB0ZXN0IHBvdXIgbGVzIHNhbGxlc1xyXG4gIGNvbnN0IFtzYWxsZXMsIHNldFNhbGxlc10gPSB1c2VTdGF0ZTxTYWxsZVtdPihbXHJcbiAgICB7IGlkOiAxLCBuYW1lOiAnU0xUJywgZGVzY3JpcHRpb246ICdTYWxsZSBkXFwnYXR0ZW50ZScsIGNvbG9yOiAnIzAzOWJlNScsIHR5cGU6ICdhdHRlbnRlJywgY2FwYWNpdHk6IDMwIH0sXHJcbiAgICB7IGlkOiAyLCBuYW1lOiAnRlRMJywgZGVzY3JpcHRpb246ICdGYXV0ZXVpbCAxJywgY29sb3I6ICcjZmY1NzIyJywgdHlwZTogJ2NvbnN1bHRhdGlvbicsIGNhcGFjaXR5OiAxIH0sXHJcbiAgICB7IGlkOiAzLCBuYW1lOiAnRlQgMScsIGRlc2NyaXB0aW9uOiAnJywgY29sb3I6ICcjMDAwMDAwJywgdHlwZTogJ2NvbnN1bHRhdGlvbicsIGNhcGFjaXR5OiAxIH0sXHJcbiAgICB7IGlkOiA0LCBuYW1lOiAnU0FMTEUgREUgQ09OU1VMVEFUSU9OIDInLCBkZXNjcmlwdGlvbjogJycsIGNvbG9yOiAnIzAwMDAwMCcsIHR5cGU6ICdjb25zdWx0YXRpb24nLCBjYXBhY2l0eTogMSB9LFxyXG4gIF0pO1xyXG5cclxuICAvLyBEb25uw6llcyBkZSB0ZXN0IHBvdXIgbGVzIMOpdsOpbmVtZW50c1xyXG4gIGNvbnN0IFtldmVuZW1lbnRzLCBzZXRFdmVuZW1lbnRzXSA9IHVzZVN0YXRlPEV2ZW5lbWVudFtdPihbXHJcbiAgICB7XHJcbiAgICAgIGlkOiAxLFxyXG4gICAgICB0aXRsZTogJ0NPTkdSRVMnLFxyXG4gICAgICBtZWRlY2luczogJ0RFTU8gREVNTycsXHJcbiAgICAgIGRhdGVEZWJ1dDogJzIxLzA0LzIwMjUnLFxyXG4gICAgICBkYXRlRmluOiAnMjMvMDQvMjAyNScsXHJcbiAgICAgIGNvbG9yOiAnI2Y1MmRiZScsXHJcbiAgICAgIGluZGlzcG9uaWJsZTogdHJ1ZSxcclxuICAgICAgcGVybWFuZW50OiBmYWxzZSxcclxuICAgICAgdG91dGVKb3VybmVlOiB0cnVlLFxyXG4gICAgICB0b3VzTGVzSm91cnM6IGZhbHNlLFxyXG4gICAgICBjYWJpbmV0OiBmYWxzZVxyXG4gICAgfSxcclxuICBdKTtcclxuXHJcbiAgLy8gw4l0YXRzIHBvdXIgbGEgcmVjaGVyY2hlXHJcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoJycpO1xyXG5cclxuICAvLyBGaWx0cmVyIGxlcyBtb3RpZnMgc2Vsb24gbGEgcmVjaGVyY2hlXHJcbiAgY29uc3QgZmlsdGVyZWRNb3RpZnMgPSBtb3RpZnMuZmlsdGVyKG1vdGlmID0+XHJcbiAgICBtb3RpZi5jb2RlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxyXG4gICAgbW90aWYuZGVzY3JpcHRpb24udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpXHJcbiAgKTtcclxuXHJcbiAgLy8gSm91cnMgZGUgbGEgc2VtYWluZVxyXG4gIGNvbnN0IGpvdXJzU2VtYWluZSA9IFsnTHVuZGknLCAnTWFyZGknLCAnTWVyY3JlZGknLCAnSmV1ZGknLCAnVmVuZHJlZGknLCAnU2FtZWRpJywgJ0RpbWFuY2hlJ107XHJcblxyXG4gIC8vIE9wdGlvbnMgcG91ciBsZXMgc2VsZWN0c1xyXG4gIGNvbnN0IHZ1ZUNhbGVuZHJpZXJPcHRpb25zID0gW1xyXG4gICAgeyB2YWx1ZTogJ1NlbWFpbmUnLCBsYWJlbDogJ1NlbWFpbmUnIH0sXHJcbiAgICB7IHZhbHVlOiAnTW9pcycsIGxhYmVsOiAnTW9pcycgfSxcclxuICAgIHsgdmFsdWU6ICdKb3VyJywgbGFiZWw6ICdKb3VyJyB9LFxyXG4gIF07XHJcblxyXG4gIGNvbnN0IGludGVydmFsbGVPcHRpb25zID0gW1xyXG4gICAgeyB2YWx1ZTogJzUgbWludXRlcycsIGxhYmVsOiAnNSBtaW51dGVzJyB9LFxyXG4gICAgeyB2YWx1ZTogJzEwIG1pbnV0ZXMnLCBsYWJlbDogJzEwIG1pbnV0ZXMnIH0sXHJcbiAgICB7IHZhbHVlOiAnMTUgbWludXRlcycsIGxhYmVsOiAnMTUgbWludXRlcycgfSxcclxuICAgIHsgdmFsdWU6ICczMCBtaW51dGVzJywgbGFiZWw6ICczMCBtaW51dGVzJyB9LFxyXG4gIF07XHJcblxyXG4gIGNvbnN0IGZ1c2VhdUhvcmFpcmVPcHRpb25zID0gW1xyXG4gICAgeyB2YWx1ZTogJ0FmcmljYS9DYXNhYmxhbmNhJywgbGFiZWw6ICdBZnJpY2EvQ2FzYWJsYW5jYScgfSxcclxuICAgIHsgdmFsdWU6ICdFdXJvcGUvUGFyaXMnLCBsYWJlbDogJ0V1cm9wZS9QYXJpcycgfSxcclxuICAgIHsgdmFsdWU6ICdVVEMnLCBsYWJlbDogJ1VUQycgfSxcclxuICBdO1xyXG5cclxuICBjb25zdCBhZ2VuZGFEZWZhdXRPcHRpb25zID0gW1xyXG4gICAgeyB2YWx1ZTogJ2FnZW5kYTEnLCBsYWJlbDogJ0FnZW5kYSBwYXIgZMOpZmF1dCcgfSxcclxuICAgIHsgdmFsdWU6ICdhZ2VuZGEyJywgbGFiZWw6ICdBZ2VuZGEgMicgfSxcclxuICBdO1xyXG5cclxuICBjb25zdCBzZXJ2aWNlc0Rlc2lnbmVzT3B0aW9ucyA9IFtcclxuICAgIHsgdmFsdWU6ICdzZXJ2aWNlMScsIGxhYmVsOiAnU2VydmljZXMgZMOpc2lnbsOpcycgfSxcclxuICAgIHsgdmFsdWU6ICdzZXJ2aWNlMicsIGxhYmVsOiAnU2VydmljZSAyJyB9LFxyXG4gIF07XHJcblxyXG4gIC8vIEZvbmN0aW9ucyBwb3VyIGfDqXJlciBsZXMgbW9kYWxlc1xyXG4gIGNvbnN0IG9wZW5Nb3RpZk1vZGFsID0gKG1vdGlmPzogTW90aWYpID0+IHtcclxuICAgIGlmIChtb3RpZikge1xyXG4gICAgICBzZXRFZGl0aW5nTW90aWYobW90aWYpO1xyXG4gICAgICBzZXRNb3RpZkZvcm0oe1xyXG4gICAgICAgIGNvZGU6IG1vdGlmLmNvZGUsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IG1vdGlmLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgIGR1cmF0aW9uOiBtb3RpZi5kdXJhdGlvbixcclxuICAgICAgICBjb2xvcjogbW90aWYuY29sb3IsXHJcbiAgICAgICAgY29sb3JSYXllZTogbW90aWYuY29sb3JSYXllZSxcclxuICAgICAgICBhZ2VuZGFEZWZhdXQ6ICcnLFxyXG4gICAgICAgIHNlcnZpY2VzRGVzaWduZXM6ICcnLFxyXG4gICAgICAgIGNvdWxldXJTb21icmU6IGZhbHNlLFxyXG4gICAgICB9KTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldEVkaXRpbmdNb3RpZihudWxsKTtcclxuICAgICAgc2V0TW90aWZGb3JtKHtcclxuICAgICAgICBjb2RlOiAnJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJycsXHJcbiAgICAgICAgZHVyYXRpb246IDE1LFxyXG4gICAgICAgIGNvbG9yOiAnIzliNGQ5MycsXHJcbiAgICAgICAgY29sb3JSYXllZTogJyM3OTI0ODUnLFxyXG4gICAgICAgIGFnZW5kYURlZmF1dDogJycsXHJcbiAgICAgICAgc2VydmljZXNEZXNpZ25lczogJycsXHJcbiAgICAgICAgY291bGV1clNvbWJyZTogZmFsc2UsXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgc2V0TW90aWZNb2RhbE9wZW5lZCh0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBvcGVuQWdlbmRhTW9kYWwgPSAoYWdlbmRhPzogQWdlbmRhKSA9PiB7XHJcbiAgICBpZiAoYWdlbmRhKSB7XHJcbiAgICAgIHNldEVkaXRpbmdBZ2VuZGEoYWdlbmRhKTtcclxuICAgICAgc2V0QWdlbmRhRm9ybSh7XHJcbiAgICAgICAgbmFtZTogYWdlbmRhLm5hbWUsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IGFnZW5kYS5kZXNjcmlwdGlvbixcclxuICAgICAgICBjb2xvcjogYWdlbmRhLmNvbG9yLFxyXG4gICAgICAgIHNlcnZpY2VzRGVzaWduZXM6ICcnLFxyXG4gICAgICAgIGlzUmVzb3VyY2U6IGFnZW5kYS5pc1Jlc291cmNlLFxyXG4gICAgICB9KTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldEVkaXRpbmdBZ2VuZGEobnVsbCk7XHJcbiAgICAgIHNldEFnZW5kYUZvcm0oe1xyXG4gICAgICAgIG5hbWU6ICcnLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnJyxcclxuICAgICAgICBjb2xvcjogJyM5YjRkOTMnLFxyXG4gICAgICAgIHNlcnZpY2VzRGVzaWduZXM6ICcnLFxyXG4gICAgICAgIGlzUmVzb3VyY2U6IGZhbHNlLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICAgIHNldEFnZW5kYU1vZGFsT3BlbmVkKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IG9wZW5TYWxsZU1vZGFsID0gKHNhbGxlPzogU2FsbGUpID0+IHtcclxuICAgIGlmIChzYWxsZSkge1xyXG4gICAgICBzZXRFZGl0aW5nU2FsbGUoc2FsbGUpO1xyXG4gICAgICBzZXRTYWxsZUZvcm0oe1xyXG4gICAgICAgIG5hbWU6IHNhbGxlLm5hbWUsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IHNhbGxlLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgIGNvbG9yOiBzYWxsZS5jb2xvcixcclxuICAgICAgICB0eXBlOiBzYWxsZS50eXBlLFxyXG4gICAgICAgIGNhcGFjaXR5OiBzYWxsZS5jYXBhY2l0eSxcclxuICAgICAgICBzZXJ2aWNlc0Rlc2lnbmVzOiAnJyxcclxuICAgICAgfSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRFZGl0aW5nU2FsbGUobnVsbCk7XHJcbiAgICAgIHNldFNhbGxlRm9ybSh7XHJcbiAgICAgICAgbmFtZTogJycsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICcnLFxyXG4gICAgICAgIGNvbG9yOiAnIzliNGQ5MycsXHJcbiAgICAgICAgdHlwZTogJ2F0dGVudGUnLFxyXG4gICAgICAgIGNhcGFjaXR5OiAxLFxyXG4gICAgICAgIHNlcnZpY2VzRGVzaWduZXM6ICcnLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICAgIHNldFNhbGxlTW9kYWxPcGVuZWQodHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgb3BlbkV2ZW5lbWVudE1vZGFsID0gKGV2ZW5lbWVudD86IEV2ZW5lbWVudCkgPT4ge1xyXG4gICAgaWYgKGV2ZW5lbWVudCkge1xyXG4gICAgICBzZXRFZGl0aW5nRXZlbmVtZW50KGV2ZW5lbWVudCk7XHJcbiAgICAgIHNldEV2ZW5lbWVudEZvcm0oe1xyXG4gICAgICAgIHRpdGxlOiBldmVuZW1lbnQudGl0bGUsXHJcbiAgICAgICAgZGF0ZURlYnV0OiBldmVuZW1lbnQuZGF0ZURlYnV0LFxyXG4gICAgICAgIGRhdGVGaW46IGV2ZW5lbWVudC5kYXRlRmluLFxyXG4gICAgICAgIGNvbG9yOiBldmVuZW1lbnQuY29sb3IsXHJcbiAgICAgICAgaW5kaXNwb25pYmxlOiBldmVuZW1lbnQuaW5kaXNwb25pYmxlLFxyXG4gICAgICAgIHBlcm1hbmVudDogZXZlbmVtZW50LnBlcm1hbmVudCxcclxuICAgICAgICB0b3V0ZUpvdXJuZWU6IGV2ZW5lbWVudC50b3V0ZUpvdXJuZWUsXHJcbiAgICAgICAgdG91c0xlc0pvdXJzOiBldmVuZW1lbnQudG91c0xlc0pvdXJzLFxyXG4gICAgICAgIGNhYmluZXQ6IGV2ZW5lbWVudC5jYWJpbmV0LFxyXG4gICAgICB9KTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldEVkaXRpbmdFdmVuZW1lbnQobnVsbCk7XHJcbiAgICAgIHNldEV2ZW5lbWVudEZvcm0oe1xyXG4gICAgICAgIHRpdGxlOiAnJyxcclxuICAgICAgICBkYXRlRGVidXQ6ICcnLFxyXG4gICAgICAgIGRhdGVGaW46ICcnLFxyXG4gICAgICAgIGNvbG9yOiAnIzliNGQ5MycsXHJcbiAgICAgICAgaW5kaXNwb25pYmxlOiBmYWxzZSxcclxuICAgICAgICBwZXJtYW5lbnQ6IGZhbHNlLFxyXG4gICAgICAgIHRvdXRlSm91cm5lZTogZmFsc2UsXHJcbiAgICAgICAgdG91c0xlc0pvdXJzOiBmYWxzZSxcclxuICAgICAgICBjYWJpbmV0OiBmYWxzZSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgICBzZXRFdmVuZW1lbnRNb2RhbE9wZW5lZCh0cnVlKTtcclxuICB9O1xyXG5cclxuICAvLyBGb25jdGlvbnMgcG91ciBzYXV2ZWdhcmRlclxyXG4gIGNvbnN0IGhhbmRsZVNhdmVNb3RpZiA9ICgpID0+IHtcclxuICAgIGlmIChlZGl0aW5nTW90aWYpIHtcclxuICAgICAgLy8gTW9kaWZpZXIgdW4gbW90aWYgZXhpc3RhbnRcclxuICAgICAgc2V0TW90aWZzKHByZXYgPT4gcHJldi5tYXAobSA9PlxyXG4gICAgICAgIG0uaWQgPT09IGVkaXRpbmdNb3RpZi5pZFxyXG4gICAgICAgICAgPyB7IC4uLm0sIC4uLm1vdGlmRm9ybSB9XHJcbiAgICAgICAgICA6IG1cclxuICAgICAgKSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBBam91dGVyIHVuIG5vdXZlYXUgbW90aWZcclxuICAgICAgY29uc3QgbmV3TW90aWY6IE1vdGlmID0ge1xyXG4gICAgICAgIGlkOiBEYXRlLm5vdygpLFxyXG4gICAgICAgIGNvZGU6IG1vdGlmRm9ybS5jb2RlLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBtb3RpZkZvcm0uZGVzY3JpcHRpb24sXHJcbiAgICAgICAgZHVyYXRpb246IG1vdGlmRm9ybS5kdXJhdGlvbixcclxuICAgICAgICBjb2xvcjogbW90aWZGb3JtLmNvbG9yLFxyXG4gICAgICAgIGNvbG9yUmF5ZWU6IG1vdGlmRm9ybS5jb2xvclJheWVlLFxyXG4gICAgICB9O1xyXG4gICAgICBzZXRNb3RpZnMocHJldiA9PiBbLi4ucHJldiwgbmV3TW90aWZdKTtcclxuICAgIH1cclxuICAgIHNldE1vdGlmTW9kYWxPcGVuZWQoZmFsc2UpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNhdmVBZ2VuZGEgPSAoKSA9PiB7XHJcbiAgICBpZiAoZWRpdGluZ0FnZW5kYSkge1xyXG4gICAgICAvLyBNb2RpZmllciB1biBhZ2VuZGEgZXhpc3RhbnRcclxuICAgICAgc2V0QWdlbmRhcyhwcmV2ID0+IHByZXYubWFwKGEgPT5cclxuICAgICAgICBhLmlkID09PSBlZGl0aW5nQWdlbmRhLmlkXHJcbiAgICAgICAgICA/IHsgLi4uYSwgLi4uYWdlbmRhRm9ybSB9XHJcbiAgICAgICAgICA6IGFcclxuICAgICAgKSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBBam91dGVyIHVuIG5vdXZlbCBhZ2VuZGFcclxuICAgICAgY29uc3QgbmV3QWdlbmRhOiBBZ2VuZGEgPSB7XHJcbiAgICAgICAgaWQ6IERhdGUubm93KCksXHJcbiAgICAgICAgbmFtZTogYWdlbmRhRm9ybS5uYW1lLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBhZ2VuZGFGb3JtLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgIGNvbG9yOiBhZ2VuZGFGb3JtLmNvbG9yLFxyXG4gICAgICAgIGlzUmVzb3VyY2U6IGFnZW5kYUZvcm0uaXNSZXNvdXJjZSxcclxuICAgICAgfTtcclxuICAgICAgc2V0QWdlbmRhcyhwcmV2ID0+IFsuLi5wcmV2LCBuZXdBZ2VuZGFdKTtcclxuICAgIH1cclxuICAgIHNldEFnZW5kYU1vZGFsT3BlbmVkKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICAvLyBGb25jdGlvbiBwb3VyIHN1cHByaW1lciB1biBtb3RpZlxyXG4gIGNvbnN0IGhhbmRsZURlbGV0ZU1vdGlmID0gKGlkOiBudW1iZXIpID0+IHtcclxuICAgIHNldE1vdGlmcyhwcmV2ID0+IHByZXYuZmlsdGVyKG0gPT4gbS5pZCAhPT0gaWQpKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPFN0YWNrIGdhcD1cImxnXCIgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgIDxUaXRsZSBvcmRlcj17Mn0gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTgwMCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgIDxJY29uSG9tZSBzaXplPXsyNH0gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiIC8+XHJcbiAgICAgICAgQWNjdWVpbCBldCBBZ2VuZGFcclxuICAgICAgPC9UaXRsZT5cclxuXHJcbiAgICAgIDxUYWJzIHZhbHVlPXthY3RpdmVUYWJ9IG9uQ2hhbmdlPXsodmFsdWUpID0+IHNldEFjdGl2ZVRhYih2YWx1ZSB8fCAnZ2VuZXJhbCcpfSB2YXJpYW50PVwib3V0bGluZVwiPlxyXG4gICAgICAgIDxUYWJzLkxpc3Q+XHJcbiAgICAgICAgICA8VGFicy5UYWIgdmFsdWU9XCJnZW5lcmFsXCIgbGVmdFNlY3Rpb249ezxJY29uU2V0dGluZ3Mgc2l6ZT17MTZ9IC8+fT5cclxuICAgICAgICAgICAgR8OpbsOpcmFsXHJcbiAgICAgICAgICA8L1RhYnMuVGFiPlxyXG4gICAgICAgICAgPFRhYnMuVGFiIHZhbHVlPVwibW90aWZzXCIgbGVmdFNlY3Rpb249ezxJY29uQ2FsZW5kYXIgc2l6ZT17MTZ9IC8+fT5cclxuICAgICAgICAgICAgTW90aWZzXHJcbiAgICAgICAgICA8L1RhYnMuVGFiPlxyXG4gICAgICAgICAgPFRhYnMuVGFiIHZhbHVlPVwiY29kZS1jb3VsZXVyc1wiIGxlZnRTZWN0aW9uPXs8SWNvbkNvbG9yUGlja2VyIHNpemU9ezE2fSAvPn0+XHJcbiAgICAgICAgICAgIENvZGUgY291bGV1cnMgbcOpZGVjaW5cclxuICAgICAgICAgIDwvVGFicy5UYWI+XHJcbiAgICAgICAgICA8VGFicy5UYWIgdmFsdWU9XCJhZ2VuZGFzXCIgbGVmdFNlY3Rpb249ezxJY29uQ2FsZW5kYXIgc2l6ZT17MTZ9IC8+fT5cclxuICAgICAgICAgICAgQWdlbmRhc1xyXG4gICAgICAgICAgPC9UYWJzLlRhYj5cclxuICAgICAgICAgIDxUYWJzLlRhYiB2YWx1ZT1cInNhbGxlc1wiIGxlZnRTZWN0aW9uPXs8SWNvblNldHRpbmdzIHNpemU9ezE2fSAvPn0+XHJcbiAgICAgICAgICAgIFNhbGxlc1xyXG4gICAgICAgICAgPC9UYWJzLlRhYj5cclxuICAgICAgICAgIDxUYWJzLlRhYiB2YWx1ZT1cImV2ZW5lbWVudHNcIiBsZWZ0U2VjdGlvbj17PEljb25TZXR0aW5ncyBzaXplPXsxNn0gLz59PlxyXG4gICAgICAgICAgICDDiXbDqW5lbWVudHNcclxuICAgICAgICAgIDwvVGFicy5UYWI+XHJcbiAgICAgICAgPC9UYWJzLkxpc3Q+XHJcblxyXG4gICAgICAgIHsvKiBPbmdsZXQgR8OpbsOpcmFsICovfVxyXG4gICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwiZ2VuZXJhbFwiIHB0PVwibWRcIj5cclxuICAgICAgICAgIDxQYXBlciBwPVwibWRcIiB3aXRoQm9yZGVyPlxyXG4gICAgICAgICAgICA8U3RhY2sgZ2FwPVwibGdcIj5cclxuICAgICAgICAgICAgICB7LyogSG9yYWlyZXMgZHUgY2VudHJlICovfVxyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8VGl0bGUgb3JkZXI9ezR9IGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgbWItbWRcIj5cclxuICAgICAgICAgICAgICAgICAgSG9yYWlyZXMgZHUgY2VudHJlXHJcbiAgICAgICAgICAgICAgICA8L1RpdGxlPlxyXG4gICAgICAgICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBmdz17NTAwfSBtYj1cInhzXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICBIZXVyZSBkJ291dmVydHVyZShzKVxyXG4gICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbFNldHRpbmdzLmhldXJlT3V2ZXJ0dXJlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZXVyZU91dmVydHVyZTogZS50YXJnZXQudmFsdWVcclxuICAgICAgICAgICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMDg6MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBmdz17NTAwfSBtYj1cInhzXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICBIZXVyZSBkZSBmZXJtZXR1cmUocylcclxuICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2dlbmVyYWxTZXR0aW5ncy5oZXVyZUZlcm1ldHVyZX1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgICAgaGV1cmVGZXJtZXR1cmU6IGUudGFyZ2V0LnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjIwOjAwXCJcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgICAgICAgIHsvKiBKb3VycyBkZSBmZXJtZXR1cmUgKi99XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTRcIj5cclxuICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0gbWI9XCJ4c1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIEpvdXJzIGRlIGZlcm1ldHVyZVxyXG4gICAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMiBmbGV4LXdyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgICB7am91cnNTZW1haW5lLm1hcCgoam91cikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPEJhZGdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17am91cn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImxpZ2h0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7am91cn1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIHsvKiBQYXJhbcOodHJlcyBkJ2FmZmljaGFnZSAqL31cclxuICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgPFRpdGxlIG9yZGVyPXs0fSBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIG1iLW1kXCI+XHJcbiAgICAgICAgICAgICAgICAgIFBhcmFtw6h0cmVzIGQnYWZmaWNoYWdlXHJcbiAgICAgICAgICAgICAgICA8L1RpdGxlPlxyXG4gICAgICAgICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlByZW1pZXIgam91ciBkZSBsYSBzZW1haW5lXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtnZW5lcmFsU2V0dGluZ3MucHJlbWllckpvdXJTZW1haW5lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWx1ZSkgPT4gc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgICAgcHJlbWllckpvdXJTZW1haW5lOiB2YWx1ZSB8fCAnTHVuZGknXHJcbiAgICAgICAgICAgICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBkYXRhPXtqb3Vyc1NlbWFpbmUubWFwKGpvdXIgPT4gKHsgdmFsdWU6IGpvdXIsIGxhYmVsOiBqb3VyIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJWdWUgcGFyIGTDqWZhdXQgZHUgY2FsZW5kcmllclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbFNldHRpbmdzLnZ1ZURlZmF1dENhbGVuZHJpZXJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2dWVEZWZhdXRDYWxlbmRyaWVyOiB2YWx1ZSB8fCAnU2VtYWluZSdcclxuICAgICAgICAgICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICAgIGRhdGE9e3Z1ZUNhbGVuZHJpZXJPcHRpb25zfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIk1pbmltdW0gaW50ZXJ2YWxsZSBkZSB0ZW1wc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbFNldHRpbmdzLm1pbmltdW1JbnRlcnZhbGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWx1ZSkgPT4gc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWluaW11bUludGVydmFsbGU6IHZhbHVlIHx8ICcxNSBtaW51dGVzJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgZGF0YT17aW50ZXJ2YWxsZU9wdGlvbnN9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiRnVzZWF1IGhvcmFpcmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2dlbmVyYWxTZXR0aW5ncy5mdXNlYXVIb3JhaXJlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWx1ZSkgPT4gc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgICAgZnVzZWF1SG9yYWlyZTogdmFsdWUgfHwgJ0FmcmljYS9DYXNhYmxhbmNhJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgZGF0YT17ZnVzZWF1SG9yYWlyZU9wdGlvbnN9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICAgICAgICB7LyogT3B0aW9ucyBkJ2FmZmljaGFnZSAqL31cclxuICAgICAgICAgICAgICAgIDxTdGFjayBnYXA9XCJ4c1wiIG10PVwibWRcIj5cclxuICAgICAgICAgICAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Z2VuZXJhbFNldHRpbmdzLmNhY2hlckpvdXJzRmVybWV0dXJlfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICBjYWNoZXJKb3Vyc0Zlcm1ldHVyZTogZS5jdXJyZW50VGFyZ2V0LmNoZWNrZWRcclxuICAgICAgICAgICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJDYWNoZXIgbGVzIGpvdXJzIGRlIGZlcm1ldHVyZVwiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxDaGVja2JveFxyXG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2dlbmVyYWxTZXR0aW5ncy5jdXJzZXVyQWdlbmRhSGV1cmVDdXJyZW50fVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICBjdXJzZXVyQWdlbmRhSGV1cmVDdXJyZW50OiBlLmN1cnJlbnRUYXJnZXQuY2hlY2tlZFxyXG4gICAgICAgICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkN1cnNldXIgZCdhZ2VuZGEgw6AgbCdoZXVyZSBjdXJyZW50XCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Z2VuZXJhbFNldHRpbmdzLmZpbHRyZTI0aENhYmluZXR9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgIGZpbHRyZTI0aENhYmluZXQ6IGUuY3VycmVudFRhcmdldC5jaGVja2VkXHJcbiAgICAgICAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiRmlsdHJlIGRlIDI0aCBkdSBjYWJpbmV0XCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Z2VuZXJhbFNldHRpbmdzLnV0aWxpc2VyQWJicmV2aWF0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICB1dGlsaXNlckFiYnJldmlhdGlvbjogZS5jdXJyZW50VGFyZ2V0LmNoZWNrZWRcclxuICAgICAgICAgICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJVdGlsaXNlciBhYnLDqXZpYXRpb25cIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtnZW5lcmFsU2V0dGluZ3MudXRpbGlzZXJMaXN0ZVBhdGllbnRzfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICB1dGlsaXNlckxpc3RlUGF0aWVudHM6IGUuY3VycmVudFRhcmdldC5jaGVja2VkXHJcbiAgICAgICAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiVXRpbGlzZXIgbGEgbGlzdGUgZGVzIHBhdGllbnRzXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Z2VuZXJhbFNldHRpbmdzLmFmZmljaGVyU2V1bGVtZW50VmlzaXRlc1Rlcm1pbmVlc31cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEdlbmVyYWxTZXR0aW5ncyhwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgICAgICAgYWZmaWNoZXJTZXVsZW1lbnRWaXNpdGVzVGVybWluZWVzOiBlLmN1cnJlbnRUYXJnZXQuY2hlY2tlZFxyXG4gICAgICAgICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkFmZmljaGVyIHNldWxlbWVudCBsZXMgdmlzaXRlcyB0ZXJtaW7DqWVzIGRhbnMgJ0hpc3RvcmlxdWUgam91cm5hbGllcidcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9TdGFjaz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgey8qIFJhcHBlbCBkZSByZW5kZXotdm91cyAqL31cclxuICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgPFRpdGxlIG9yZGVyPXs0fSBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIG1iLW1kXCI+XHJcbiAgICAgICAgICAgICAgICAgIFJhcHBlbCBkZSByZW5kZXotdm91c1xyXG4gICAgICAgICAgICAgICAgPC9UaXRsZT5cclxuICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGZ3PXs1MDB9IG1iPVwieHNcIj5cclxuICAgICAgICAgICAgICAgICAgRHVyw6llIGF2YW50IFJEVlxyXG4gICAgICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgZGF0YT17W1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB7IHZhbHVlOiAnNScsIGxhYmVsOiAnTW9kw6hsZSBTTVMnIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsgdmFsdWU6ICcxMCcsIGxhYmVsOiAnMTAgbWludXRlcycgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgeyB2YWx1ZTogJzE1JywgbGFiZWw6ICcxNSBtaW51dGVzJyB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJNb2TDqGxlIEVtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGRhdGE9e1tcclxuICAgICAgICAgICAgICAgICAgICAgICAgeyB2YWx1ZTogJ2VtYWlsMScsIGxhYmVsOiAnTW9kw6hsZSBFbWFpbCcgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgeyB2YWx1ZTogJ2VtYWlsMicsIGxhYmVsOiAnRW1haWwgMicgfSxcclxuICAgICAgICAgICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9TdGFjaz5cclxuICAgICAgICAgIDwvUGFwZXI+XHJcbiAgICAgICAgPC9UYWJzLlBhbmVsPlxyXG5cclxuICAgICAgICB7LyogT25nbGV0IE1vdGlmcyAqL31cclxuICAgICAgICA8VGFicy5QYW5lbCB2YWx1ZT1cIm1vdGlmc1wiIHB0PVwibWRcIj5cclxuICAgICAgICAgIDxQYXBlciBwPVwibWRcIiB3aXRoQm9yZGVyPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi00XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlJlY2hlcmNoZXJcIlxyXG4gICAgICAgICAgICAgICAgICBsZWZ0U2VjdGlvbj17PEljb25TZWFyY2ggc2l6ZT17MTZ9IC8+fVxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hUZXJtKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy02NFwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIGxlZnRTZWN0aW9uPXs8SWNvblBsdXMgc2l6ZT17MTZ9IC8+fVxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3Blbk1vdGlmTW9kYWwoKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwIGhvdmVyOmJnLWJsdWUtNjAwXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBBam91dGVyIG5vdXZlYXUgbW90aWZcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8U2Nyb2xsQXJlYT5cclxuICAgICAgICAgICAgICA8VGFibGUgc3RyaXBlZCBoaWdobGlnaHRPbkhvdmVyIHdpdGhUYWJsZUJvcmRlcj5cclxuICAgICAgICAgICAgICAgIDxUYWJsZS5UaGVhZD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5Db2RlPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGg+TW90aWY8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5EdXLDqWUgKG1pbik8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5Db3VsZXVyPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGg+Q291bGV1ciByYXnDqWU8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaCBzdHlsZT17eyB3aWR0aDogMTAwIH19PjwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgIDwvVGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICA8L1RhYmxlLlRoZWFkPlxyXG4gICAgICAgICAgICAgICAgPFRhYmxlLlRib2R5PlxyXG4gICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRNb3RpZnMubWFwKChtb3RpZikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UciBrZXk9e21vdGlmLmlkfT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgZnc9ezUwMH0+e21vdGlmLmNvZGV9PC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD57bW90aWYuZGVzY3JpcHRpb259PC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD57bW90aWYuZHVyYXRpb259PC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDb2xvclN3YXRjaCBjb2xvcj17bW90aWYuY29sb3J9IHNpemU9ezIwfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiPnttb3RpZi5jb2xvcn08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDb2xvclN3YXRjaCBjb2xvcj17bW90aWYuY29sb3JSYXllZX0gc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+e21vdGlmLmNvbG9yUmF5ZWV9PC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxHcm91cCBnYXA9XCJ4c1wiIGp1c3RpZnk9XCJjZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBsYWJlbD1cIk1vZGlmaWVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWN0aW9uSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwic3VidGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3Blbk1vdGlmTW9kYWwobW90aWYpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkVkaXQgc2l6ZT17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjdGlvbkljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGxhYmVsPVwiU3VwcHJpbWVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWN0aW9uSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwic3VidGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJyZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVNb3RpZihtb3RpZi5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uVHJhc2ggc2l6ZT17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjdGlvbkljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0dyb3VwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgIDwvVGFibGUuVGJvZHk+XHJcbiAgICAgICAgICAgICAgPC9UYWJsZT5cclxuICAgICAgICAgICAgPC9TY3JvbGxBcmVhPlxyXG4gICAgICAgICAgPC9QYXBlcj5cclxuICAgICAgICA8L1RhYnMuUGFuZWw+XHJcblxyXG4gICAgICAgIHsvKiBPbmdsZXQgQ29kZSBjb3VsZXVycyBtw6lkZWNpbiAqL31cclxuICAgICAgICA8VGFicy5QYW5lbCB2YWx1ZT1cImNvZGUtY291bGV1cnNcIiBwdD1cIm1kXCI+XHJcbiAgICAgICAgICA8UGFwZXIgcD1cIm1kXCIgd2l0aEJvcmRlcj5cclxuICAgICAgICAgICAgPFRleHQ+Q29udGVudSBwb3VyIENvZGUgY291bGV1cnMgbcOpZGVjaW48L1RleHQ+XHJcbiAgICAgICAgICA8L1BhcGVyPlxyXG4gICAgICAgIDwvVGFicy5QYW5lbD5cclxuXHJcbiAgICAgICAgey8qIE9uZ2xldCBBZ2VuZGFzICovfVxyXG4gICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwiYWdlbmRhc1wiIHB0PVwibWRcIj5cclxuICAgICAgICAgIDxQYXBlciBwPVwibWRcIiB3aXRoQm9yZGVyPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi00XCI+XHJcbiAgICAgICAgICAgICAgPFRpdGxlIG9yZGVyPXs0fT5BZ2VuZGFzPC9UaXRsZT5cclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICBsZWZ0U2VjdGlvbj17PEljb25QbHVzIHNpemU9ezE2fSAvPn1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5BZ2VuZGFNb2RhbCgpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgaG92ZXI6YmctYmx1ZS02MDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIE5vdXZlbCBhZ2VuZGFcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8U2Nyb2xsQXJlYT5cclxuICAgICAgICAgICAgICA8VGFibGUgc3RyaXBlZCBoaWdobGlnaHRPbkhvdmVyIHdpdGhUYWJsZUJvcmRlcj5cclxuICAgICAgICAgICAgICAgIDxUYWJsZS5UaGVhZD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5Ob208L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5EZXNjcmlwdGlvbjwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPkNvdWxldXI8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5SZXNzb3VyY2U8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaCBzdHlsZT17eyB3aWR0aDogMTAwIH19PjwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgIDwvVGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICA8L1RhYmxlLlRoZWFkPlxyXG4gICAgICAgICAgICAgICAgPFRhYmxlLlRib2R5PlxyXG4gICAgICAgICAgICAgICAgICB7YWdlbmRhcy5sZW5ndGggPT09IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkIGNvbFNwYW49ezV9IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtZ3JheS01MDAgcHktOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBBdWN1biBhZ2VuZGEgdHJvdXbDqVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgIGFnZW5kYXMubWFwKChhZ2VuZGEpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UciBrZXk9e2FnZW5kYS5pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmdz17NTAwfT57YWdlbmRhLm5hbWV9PC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+e2FnZW5kYS5kZXNjcmlwdGlvbn08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoIGNvbG9yPXthZ2VuZGEuY29sb3J9IHNpemU9ezIwfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+e2FnZW5kYS5jb2xvcn08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgY29sb3I9e2FnZW5kYS5pc1Jlc291cmNlID8gJ2dyZWVuJyA6ICdncmF5J30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YWdlbmRhLmlzUmVzb3VyY2UgPyAnT3VpJyA6ICdOb24nfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8R3JvdXAgZ2FwPVwieHNcIiBqdXN0aWZ5PVwiY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBsYWJlbD1cIk1vZGlmaWVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBY3Rpb25JY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInN1YnRsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuQWdlbmRhTW9kYWwoYWdlbmRhKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uRWRpdCBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY3Rpb25JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvR3JvdXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgICAgICkpXHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L1RhYmxlLlRib2R5PlxyXG4gICAgICAgICAgICAgIDwvVGFibGU+XHJcbiAgICAgICAgICAgIDwvU2Nyb2xsQXJlYT5cclxuICAgICAgICAgIDwvUGFwZXI+XHJcbiAgICAgICAgPC9UYWJzLlBhbmVsPlxyXG5cclxuICAgICAgICB7LyogT25nbGV0IFNhbGxlcyAqL31cclxuICAgICAgICA8VGFicy5QYW5lbCB2YWx1ZT1cInNhbGxlc1wiIHB0PVwibWRcIj5cclxuICAgICAgICAgIDxQYXBlciBwPVwibWRcIiB3aXRoQm9yZGVyPlxyXG4gICAgICAgICAgICA8VGV4dD5Db250ZW51IHBvdXIgU2FsbGVzPC9UZXh0PlxyXG4gICAgICAgICAgPC9QYXBlcj5cclxuICAgICAgICA8L1RhYnMuUGFuZWw+XHJcblxyXG4gICAgICAgIHsvKiBPbmdsZXQgw4l2w6luZW1lbnRzICovfVxyXG4gICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwiZXZlbmVtZW50c1wiIHB0PVwibWRcIj5cclxuICAgICAgICAgIDxQYXBlciBwPVwibWRcIiB3aXRoQm9yZGVyPlxyXG4gICAgICAgICAgICA8VGV4dD5Db250ZW51IHBvdXIgw4l2w6luZW1lbnRzPC9UZXh0PlxyXG4gICAgICAgICAgPC9QYXBlcj5cclxuICAgICAgICA8L1RhYnMuUGFuZWw+XHJcbiAgICAgIDwvVGFicz5cclxuXHJcbiAgICAgIHsvKiBNb2RhbCBwb3VyIE1vdGlmICovfVxyXG4gICAgICA8TW9kYWxcclxuICAgICAgICBvcGVuZWQ9e21vdGlmTW9kYWxPcGVuZWR9XHJcbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0TW90aWZNb2RhbE9wZW5lZChmYWxzZSl9XHJcbiAgICAgICAgdGl0bGU9e1xyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiAtbS00IG1iLTRcIj5cclxuICAgICAgICAgICAgPEljb25DYWxlbmRhciBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgPFRleHQgZnc9ezYwMH0+QWN0ZXM8L1RleHQ+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICB9XHJcbiAgICAgICAgc2l6ZT1cImxnXCJcclxuICAgICAgICB3aXRoQ2xvc2VCdXR0b249e2ZhbHNlfVxyXG4gICAgICA+XHJcbiAgICAgICAgPFN0YWNrIGdhcD1cIm1kXCI+XHJcbiAgICAgICAgICA8R3JpZD5cclxuICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgICAgIGxhYmVsPXs8c3Bhbj5Db2RlIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+PC9zcGFuPn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXttb3RpZkZvcm0uY29kZX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TW90aWZGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgY29kZTogZS50YXJnZXQudmFsdWUgfSkpfVxyXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICAgICAgbGFiZWw9ezxzcGFuPkRlc2NyaXB0aW9uIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+PC9zcGFuPn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXttb3RpZkZvcm0uZGVzY3JpcHRpb259XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE1vdGlmRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KSl9XHJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICBsYWJlbD17PHNwYW4+RHVyw6llIChtaW4pIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+PC9zcGFuPn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXttb3RpZkZvcm0uZHVyYXRpb24udG9TdHJpbmcoKX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TW90aWZGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZHVyYXRpb246IHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB8fCAxNSB9KSl9XHJcbiAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcclxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezN9PlxyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBmdz17NTAwfSBtYj1cInhzXCI+Q291bGV1cjwvVGV4dD5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoXHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I9e21vdGlmRm9ybS5jb2xvcn1cclxuICAgICAgICAgICAgICAgICAgICBzaXplPXszMH1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBib3JkZXIgYm9yZGVyLWdyYXktMzAwXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17M30+XHJcbiAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGZ3PXs1MDB9IG1iPVwieHNcIj5Db3VsZXVyIHJhecOpZTwvVGV4dD5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoXHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I9e21vdGlmRm9ybS5jb2xvclJheWVlfVxyXG4gICAgICAgICAgICAgICAgICAgIHNpemU9ezMwfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGJvcmRlciBib3JkZXItZ3JheS0zMDBcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkFnZW5kYSBwYXIgZMOpZmF1dFwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17bW90aWZGb3JtLmFnZW5kYURlZmF1dH1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsodmFsdWUpID0+IHNldE1vdGlmRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIGFnZW5kYURlZmF1dDogdmFsdWUgfHwgJycgfSkpfVxyXG4gICAgICAgICAgICAgICAgZGF0YT17YWdlbmRhRGVmYXV0T3B0aW9uc31cclxuICAgICAgICAgICAgICAgIHJpZ2h0U2VjdGlvbj17PEljb25QbHVzIHNpemU9ezE2fSBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciB0ZXh0LWJsdWUtNTAwXCIgLz59XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiU2VydmljZXMgZMOpc2lnbsOpc1wiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17bW90aWZGb3JtLnNlcnZpY2VzRGVzaWduZXN9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRNb3RpZkZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBzZXJ2aWNlc0Rlc2lnbmVzOiB2YWx1ZSB8fCAnJyB9KSl9XHJcbiAgICAgICAgICAgICAgICBkYXRhPXtzZXJ2aWNlc0Rlc2lnbmVzT3B0aW9uc31cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB0LTQgYm9yZGVyLXRcIj5cclxuICAgICAgICAgICAgPFN3aXRjaFxyXG4gICAgICAgICAgICAgIGNoZWNrZWQ9e21vdGlmRm9ybS5jb3VsZXVyU29tYnJlfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TW90aWZGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgY291bGV1clNvbWJyZTogZS5jdXJyZW50VGFyZ2V0LmNoZWNrZWQgfSkpfVxyXG4gICAgICAgICAgICAgIGxhYmVsPVwiQ291bGV1ciBzb21icmVcIlxyXG4gICAgICAgICAgICAgIGxhYmVsUG9zaXRpb249XCJyaWdodFwiXHJcbiAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICA8R3JvdXA+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImZpbGxlZFwiXHJcbiAgICAgICAgICAgICAgICBjb2xvcj1cImdyYXlcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0TW90aWZNb2RhbE9wZW5lZChmYWxzZSl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgQW5udWxlclxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJmaWxsZWRcIlxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNhdmVNb3RpZn1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshbW90aWZGb3JtLmNvZGUgfHwgIW1vdGlmRm9ybS5kZXNjcmlwdGlvbn1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBFbnJlZ2lzdHJlclxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L0dyb3VwPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9TdGFjaz5cclxuICAgICAgPC9Nb2RhbD5cclxuXHJcbiAgICAgIHsvKiBNb2RhbCBwb3VyIEFnZW5kYSAqL31cclxuICAgICAgPE1vZGFsXHJcbiAgICAgICAgb3BlbmVkPXthZ2VuZGFNb2RhbE9wZW5lZH1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRBZ2VuZGFNb2RhbE9wZW5lZChmYWxzZSl9XHJcbiAgICAgICAgdGl0bGU9e1xyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiAtbS00IG1iLTRcIj5cclxuICAgICAgICAgICAgPEljb25DYWxlbmRhciBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgPFRleHQgZnc9ezYwMH0+QWdlbmRhPC9UZXh0PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgd2l0aENsb3NlQnV0dG9uPXtmYWxzZX1cclxuICAgICAgPlxyXG4gICAgICAgIDxTdGFjayBnYXA9XCJtZFwiPlxyXG4gICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs4fT5cclxuICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICBsYWJlbD17PHNwYW4+Tm9tIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+PC9zcGFuPn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXthZ2VuZGFGb3JtLm5hbWV9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEFnZW5kYUZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XHJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs0fT5cclxuICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0gbWI9XCJ4c1wiPkNvdWxldXI8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxDb2xvclN3YXRjaFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yPXthZ2VuZGFGb3JtLmNvbG9yfVxyXG4gICAgICAgICAgICAgICAgICAgIHNpemU9ezMwfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGJvcmRlciBib3JkZXItZ3JheS0zMDBcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkRlc2NyaXB0aW9uXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXthZ2VuZGFGb3JtLmRlc2NyaXB0aW9ufVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRBZ2VuZGFGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgbGFiZWw9XCJTZXJ2aWNlcyBkw6lzaWduw6lzXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXthZ2VuZGFGb3JtLnNlcnZpY2VzRGVzaWduZXN9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRBZ2VuZGFGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgc2VydmljZXNEZXNpZ25lczogdmFsdWUgfHwgJycgfSkpfVxyXG4gICAgICAgICAgICAgICAgZGF0YT17c2VydmljZXNEZXNpZ25lc09wdGlvbnN9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC00IGJvcmRlci10XCI+XHJcbiAgICAgICAgICAgIDxTd2l0Y2hcclxuICAgICAgICAgICAgICBjaGVja2VkPXthZ2VuZGFGb3JtLmlzUmVzb3VyY2V9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRBZ2VuZGFGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgaXNSZXNvdXJjZTogZS5jdXJyZW50VGFyZ2V0LmNoZWNrZWQgfSkpfVxyXG4gICAgICAgICAgICAgIGxhYmVsPVwiQWZmaWNoZXIgY29tbWUgcmVzc291cmNlXCJcclxuICAgICAgICAgICAgICBsYWJlbFBvc2l0aW9uPVwicmlnaHRcIlxyXG4gICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgPEdyb3VwPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJmaWxsZWRcIlxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJncmF5XCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFnZW5kYU1vZGFsT3BlbmVkKGZhbHNlKX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBBbm51bGVyXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImZpbGxlZFwiXHJcbiAgICAgICAgICAgICAgICBjb2xvcj1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2F2ZUFnZW5kYX1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshYWdlbmRhRm9ybS5uYW1lfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIEVucmVnaXN0cmVyXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvR3JvdXA+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L1N0YWNrPlxyXG4gICAgICA8L01vZGFsPlxyXG4gICAgPC9TdGFjaz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQWNjdWVpbEV0QWdlbmRhO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIlN0YWNrIiwiVGl0bGUiLCJUYWJzIiwiUGFwZXIiLCJHcmlkIiwiVGV4dCIsIlRleHRJbnB1dCIsIlNlbGVjdCIsIlN3aXRjaCIsIkJ1dHRvbiIsIk1vZGFsIiwiVGFibGUiLCJBY3Rpb25JY29uIiwiR3JvdXAiLCJDaGVja2JveCIsIlRvb2x0aXAiLCJTY3JvbGxBcmVhIiwiQmFkZ2UiLCJDb2xvclN3YXRjaCIsIkljb25QbHVzIiwiSWNvbkVkaXQiLCJJY29uVHJhc2giLCJJY29uU2VhcmNoIiwiSWNvbkNhbGVuZGFyIiwiSWNvblNldHRpbmdzIiwiSWNvbkhvbWUiLCJJY29uQ29sb3JQaWNrZXIiLCJBY2N1ZWlsRXRBZ2VuZGEiLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJtb3RpZk1vZGFsT3BlbmVkIiwic2V0TW90aWZNb2RhbE9wZW5lZCIsImFnZW5kYU1vZGFsT3BlbmVkIiwic2V0QWdlbmRhTW9kYWxPcGVuZWQiLCJzYWxsZU1vZGFsT3BlbmVkIiwic2V0U2FsbGVNb2RhbE9wZW5lZCIsImV2ZW5lbWVudE1vZGFsT3BlbmVkIiwic2V0RXZlbmVtZW50TW9kYWxPcGVuZWQiLCJlZGl0aW5nTW90aWYiLCJzZXRFZGl0aW5nTW90aWYiLCJlZGl0aW5nQWdlbmRhIiwic2V0RWRpdGluZ0FnZW5kYSIsImVkaXRpbmdTYWxsZSIsInNldEVkaXRpbmdTYWxsZSIsImVkaXRpbmdFdmVuZW1lbnQiLCJzZXRFZGl0aW5nRXZlbmVtZW50IiwibW90aWZGb3JtIiwic2V0TW90aWZGb3JtIiwiY29kZSIsImRlc2NyaXB0aW9uIiwiZHVyYXRpb24iLCJjb2xvciIsImNvbG9yUmF5ZWUiLCJhZ2VuZGFEZWZhdXQiLCJzZXJ2aWNlc0Rlc2lnbmVzIiwiY291bGV1clNvbWJyZSIsImFnZW5kYUZvcm0iLCJzZXRBZ2VuZGFGb3JtIiwibmFtZSIsImlzUmVzb3VyY2UiLCJzYWxsZUZvcm0iLCJzZXRTYWxsZUZvcm0iLCJ0eXBlIiwiY2FwYWNpdHkiLCJldmVuZW1lbnRGb3JtIiwic2V0RXZlbmVtZW50Rm9ybSIsInRpdGxlIiwiZGF0ZURlYnV0IiwiZGF0ZUZpbiIsImluZGlzcG9uaWJsZSIsInBlcm1hbmVudCIsInRvdXRlSm91cm5lZSIsInRvdXNMZXNKb3VycyIsImNhYmluZXQiLCJnZW5lcmFsU2V0dGluZ3MiLCJzZXRHZW5lcmFsU2V0dGluZ3MiLCJoZXVyZU91dmVydHVyZSIsImhldXJlRmVybWV0dXJlIiwicHJlbWllckpvdXJTZW1haW5lIiwidnVlRGVmYXV0Q2FsZW5kcmllciIsIm1pbmltdW1JbnRlcnZhbGxlIiwiZnVzZWF1SG9yYWlyZSIsImNhY2hlckpvdXJzRmVybWV0dXJlIiwiY3Vyc2V1ckFnZW5kYUhldXJlQ3VycmVudCIsImZpbHRyZTI0aENhYmluZXQiLCJ1dGlsaXNlckFiYnJldmlhdGlvbiIsInV0aWxpc2VyTGlzdGVQYXRpZW50cyIsImFmZmljaGVyU2V1bGVtZW50VmlzaXRlc1Rlcm1pbmVlcyIsIm1vdGlmcyIsInNldE1vdGlmcyIsImlkIiwiYWdlbmRhcyIsInNldEFnZW5kYXMiLCJkb2N0ZXVycyIsInNldERvY3RldXJzIiwic2FsbGVzIiwic2V0U2FsbGVzIiwiZXZlbmVtZW50cyIsInNldEV2ZW5lbWVudHMiLCJtZWRlY2lucyIsInNlYXJjaFRlcm0iLCJzZXRTZWFyY2hUZXJtIiwiZmlsdGVyZWRNb3RpZnMiLCJmaWx0ZXIiLCJtb3RpZiIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJqb3Vyc1NlbWFpbmUiLCJ2dWVDYWxlbmRyaWVyT3B0aW9ucyIsInZhbHVlIiwibGFiZWwiLCJpbnRlcnZhbGxlT3B0aW9ucyIsImZ1c2VhdUhvcmFpcmVPcHRpb25zIiwiYWdlbmRhRGVmYXV0T3B0aW9ucyIsInNlcnZpY2VzRGVzaWduZXNPcHRpb25zIiwib3Blbk1vdGlmTW9kYWwiLCJvcGVuQWdlbmRhTW9kYWwiLCJhZ2VuZGEiLCJvcGVuU2FsbGVNb2RhbCIsInNhbGxlIiwib3BlbkV2ZW5lbWVudE1vZGFsIiwiZXZlbmVtZW50IiwiaGFuZGxlU2F2ZU1vdGlmIiwicHJldiIsIm1hcCIsIm0iLCJuZXdNb3RpZiIsIkRhdGUiLCJub3ciLCJoYW5kbGVTYXZlQWdlbmRhIiwiYSIsIm5ld0FnZW5kYSIsImhhbmRsZURlbGV0ZU1vdGlmIiwiZ2FwIiwiY2xhc3NOYW1lIiwib3JkZXIiLCJzaXplIiwib25DaGFuZ2UiLCJ2YXJpYW50IiwiTGlzdCIsIlRhYiIsImxlZnRTZWN0aW9uIiwiUGFuZWwiLCJwdCIsInAiLCJ3aXRoQm9yZGVyIiwiZGl2IiwiQ29sIiwic3BhbiIsImZ3IiwibWIiLCJlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJqb3VyIiwiZGF0YSIsIm10IiwiY2hlY2tlZCIsImN1cnJlbnRUYXJnZXQiLCJvbkNsaWNrIiwic3RyaXBlZCIsImhpZ2hsaWdodE9uSG92ZXIiLCJ3aXRoVGFibGVCb3JkZXIiLCJUaGVhZCIsIlRyIiwiVGgiLCJzdHlsZSIsIndpZHRoIiwiVGJvZHkiLCJUZCIsImp1c3RpZnkiLCJsZW5ndGgiLCJjb2xTcGFuIiwib3BlbmVkIiwib25DbG9zZSIsIndpdGhDbG9zZUJ1dHRvbiIsInJlcXVpcmVkIiwidG9TdHJpbmciLCJwYXJzZUludCIsInJpZ2h0U2VjdGlvbiIsImxhYmVsUG9zaXRpb24iLCJkaXNhYmxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx\n"));

/***/ })

});