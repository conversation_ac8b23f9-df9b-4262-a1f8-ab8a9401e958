"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconChevronLeft.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconChevronLeft.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconChevronLeft)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconChevronLeft = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"chevron-left\", \"IconChevronLeft\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M15 6l-6 6l6 6\",\n            \"key\": \"svg-0\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconChevronLeft.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25DaGV2cm9uTGVmdC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDQSxDQUFlLDBGQUFxQixZQUFXLGNBQWdCLG9CQUFtQjtJQUFDO1FBQUM7UUFBTyxDQUFDO1lBQUEsSUFBSSxpQkFBaUI7WUFBQSxPQUFNLENBQU87UUFBQSxDQUFDO0tBQUM7Q0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcc3JjXFxpY29uc1xcSWNvbkNoZXZyb25MZWZ0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVSZWFjdENvbXBvbmVudCBmcm9tICcuLi9jcmVhdGVSZWFjdENvbXBvbmVudCc7XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVSZWFjdENvbXBvbmVudCgnb3V0bGluZScsICdjaGV2cm9uLWxlZnQnLCAnSWNvbkNoZXZyb25MZWZ0JywgW1tcInBhdGhcIix7XCJkXCI6XCJNMTUgNmwtNiA2bDYgNlwiLFwia2V5XCI6XCJzdmctMFwifV1dKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconChevronLeft.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconChevronRight.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconChevronRight.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconChevronRight)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconChevronRight = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"chevron-right\", \"IconChevronRight\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M9 6l6 6l-6 6\",\n            \"key\": \"svg-0\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconChevronRight.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25DaGV2cm9uUmlnaHQubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQ0EsQ0FBZSwyRkFBcUIsWUFBVyxlQUFpQixxQkFBb0I7SUFBQztRQUFDO1FBQU8sQ0FBQztZQUFBLElBQUksZ0JBQWdCO1lBQUEsT0FBTSxDQUFPO1FBQUEsQ0FBQztLQUFDO0NBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHNyY1xcaWNvbnNcXEljb25DaGV2cm9uUmlnaHQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZVJlYWN0Q29tcG9uZW50IGZyb20gJy4uL2NyZWF0ZVJlYWN0Q29tcG9uZW50JztcbmV4cG9ydCBkZWZhdWx0IGNyZWF0ZVJlYWN0Q29tcG9uZW50KCdvdXRsaW5lJywgJ2NoZXZyb24tcmlnaHQnLCAnSWNvbkNoZXZyb25SaWdodCcsIFtbXCJwYXRoXCIse1wiZFwiOlwiTTkgNmw2IDZsLTYgNlwiLFwia2V5XCI6XCJzdmctMFwifV1dKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconChevronRight.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconLungs.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tabler/icons-react/dist/esm/icons/IconLungs.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IconLungs)\n/* harmony export */ });\n/* harmony import */ var _createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createReactComponent.mjs */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/createReactComponent.mjs\");\n/**\n * @license @tabler/icons-react v3.33.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nvar IconLungs = (0,_createReactComponent_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"outline\", \"lungs\", \"IconLungs\", [\n    [\n        \"path\",\n        {\n            \"d\": \"M6.081 20c1.612 0 2.919 -1.335 2.919 -2.98v-9.763c0 -.694 -.552 -1.257 -1.232 -1.257c-.205 0 -.405 .052 -.584 .15l-.13 .083c-1.46 1.059 -2.432 2.647 -3.404 5.824c-.42 1.37 -.636 2.962 -.648 4.775c-.012 1.675 1.261 3.054 2.877 3.161l.203 .007z\",\n            \"key\": \"svg-0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M17.92 20c-1.613 0 -2.92 -1.335 -2.92 -2.98v-9.763c0 -.694 .552 -1.257 1.233 -1.257c.204 0 .405 .052 .584 .15l.13 .083c1.46 1.059 2.432 2.647 3.405 5.824c.42 1.37 .636 2.962 .648 4.775c.012 1.675 -1.261 3.054 -2.878 3.161l-.202 .007z\",\n            \"key\": \"svg-1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M9 12a3 3 0 0 0 3 -3a3 3 0 0 0 3 3\",\n            \"key\": \"svg-2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            \"d\": \"M12 4v5\",\n            \"key\": \"svg-3\"\n        }\n    ]\n]);\n //# sourceMappingURL=IconLungs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFibGVyL2ljb25zLXJlYWN0L2Rpc3QvZXNtL2ljb25zL0ljb25MdW5ncy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDQSxnQkFBZSxxRUFBb0IsQ0FBQyxDQUFXLG1CQUFTLGFBQWE7SUFBQztRQUFDO1FBQU87WUFBQyxLQUFJLENBQXFQO1lBQUEsT0FBTTtRQUFBLENBQVE7S0FBQTtJQUFFO1FBQUMsTUFBTztRQUFBO1lBQUMsS0FBSSwyT0FBNE87WUFBQSxNQUFNLFFBQU87UUFBQztLQUFBLENBQUU7SUFBQTtRQUFDO1FBQU8sQ0FBQztZQUFBLEdBQUksdUNBQXFDO1lBQUEsT0FBTSxDQUFPO1FBQUEsQ0FBQztLQUFBO0lBQUU7UUFBQyxPQUFPO1FBQUE7WUFBQyxHQUFJLFlBQVU7WUFBQSxNQUFNLFFBQU87UUFBQztLQUFDO0NBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29ycnlcXERlc2t0b3BcXHNyY1xcaWNvbnNcXEljb25MdW5ncy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlUmVhY3RDb21wb25lbnQgZnJvbSAnLi4vY3JlYXRlUmVhY3RDb21wb25lbnQnO1xuZXhwb3J0IGRlZmF1bHQgY3JlYXRlUmVhY3RDb21wb25lbnQoJ291dGxpbmUnLCAnbHVuZ3MnLCAnSWNvbkx1bmdzJywgW1tcInBhdGhcIix7XCJkXCI6XCJNNi4wODEgMjBjMS42MTIgMCAyLjkxOSAtMS4zMzUgMi45MTkgLTIuOTh2LTkuNzYzYzAgLS42OTQgLS41NTIgLTEuMjU3IC0xLjIzMiAtMS4yNTdjLS4yMDUgMCAtLjQwNSAuMDUyIC0uNTg0IC4xNWwtLjEzIC4wODNjLTEuNDYgMS4wNTkgLTIuNDMyIDIuNjQ3IC0zLjQwNCA1LjgyNGMtLjQyIDEuMzcgLS42MzYgMi45NjIgLS42NDggNC43NzVjLS4wMTIgMS42NzUgMS4yNjEgMy4wNTQgMi44NzcgMy4xNjFsLjIwMyAuMDA3elwiLFwia2V5XCI6XCJzdmctMFwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xNy45MiAyMGMtMS42MTMgMCAtMi45MiAtMS4zMzUgLTIuOTIgLTIuOTh2LTkuNzYzYzAgLS42OTQgLjU1MiAtMS4yNTcgMS4yMzMgLTEuMjU3Yy4yMDQgMCAuNDA1IC4wNTIgLjU4NCAuMTVsLjEzIC4wODNjMS40NiAxLjA1OSAyLjQzMiAyLjY0NyAzLjQwNSA1LjgyNGMuNDIgMS4zNyAuNjM2IDIuOTYyIC42NDggNC43NzVjLjAxMiAxLjY3NSAtMS4yNjEgMy4wNTQgLTIuODc4IDMuMTYxbC0uMjAyIC4wMDd6XCIsXCJrZXlcIjpcInN2Zy0xXCJ9XSxbXCJwYXRoXCIse1wiZFwiOlwiTTkgMTJhMyAzIDAgMCAwIDMgLTNhMyAzIDAgMCAwIDMgM1wiLFwia2V5XCI6XCJzdmctMlwifV0sW1wicGF0aFwiLHtcImRcIjpcIk0xMiA0djVcIixcImtleVwiOlwic3ZnLTNcIn1dXSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconLungs.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/Oxymetrie.tsx":
/*!******************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/Oxymetrie.tsx ***!
  \******************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=IconChevronLeft,IconChevronRight,IconLungs,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconLungs.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconChevronLeft,IconChevronRight,IconLungs,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=IconChevronLeft,IconChevronRight,IconLungs,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconChevronLeft.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=IconChevronLeft,IconChevronRight,IconLungs,IconSearch!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconChevronRight.mjs\");\n/* harmony import */ var mantine_datatable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mantine-datatable */ \"(app-pages-browser)/./node_modules/mantine-datatable/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Oxymetrie = ()=>{\n    _s();\n    // États pour les onglets\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    // États pour la pagination\n    const [currentPageProtocols, setCurrentPageProtocols] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPageProtocols, setItemsPerPageProtocols] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2);\n    const [currentPageReasons, setCurrentPageReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPageReasons, setItemsPerPageReasons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2);\n    // Données mockées\n    const chartColors = [];\n    const examProtocols = [];\n    const examReasons = [];\n    // Gestionnaire pour les onglets\n    const handleTabChange = (value)=>{\n        if (value) setActiveTab(value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-gray-50 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            size: 32,\n                            className: \"text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-800\",\n                            children: \"Oxym\\xe9trie\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                shadow: \"sm\",\n                padding: \"lg\",\n                radius: \"md\",\n                withBorder: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    value: activeTab,\n                    onChange: handleTabChange,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.List, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                    value: \"general\",\n                                    children: \"G\\xe9n\\xe9ral\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                    value: \"couleur-chart\",\n                                    children: \"Couleur du chart\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                    value: \"protocoles-examen\",\n                                    children: \"Protocoles d'examen\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                    value: \"motifs-examen\",\n                                    children: \"Motifs d'examen\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                            value: \"general\",\n                            pt: \"md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        size: \"lg\",\n                                        fw: 600,\n                                        className: \"text-gray-800 mb-4\",\n                                        children: \"Examen valeur par d\\xe9faut\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Type d'examen\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                placeholder: \"\",\n                                                                data: [],\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"M\\xe8tre/tour\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.TextInput, {\n                                                                placeholder: \"Raison\",\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 107,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Raison\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.TextInput, {\n                                                                placeholder: \"Raison\",\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Courte pause\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.TextInput, {\n                                                                placeholder: \"\",\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Dur\\xe9e de r\\xe9cup\\xe9ration\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.TextInput, {\n                                                                placeholder: \"\",\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Cause d'arr\\xeat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.TextInput, {\n                                                                placeholder: \"Cause d'arr\\xeat\",\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Checkbox, {\n                                            label: \"Enregistrement automatique \\xe0 la fin d'examen\",\n                                            className: \"text-gray-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                            value: \"couleur-chart\",\n                            pt: \"md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.TextInput, {\n                                            placeholder: \"Rechercher...\",\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 32\n                                            }, void 0),\n                                            className: \"max-w-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    chartColors.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-100 p-4 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            size: \"sm\",\n                                            className: \"text-yellow-800\",\n                                            children: \"⚠️ Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                        withTableBorder: true,\n                                        borderRadius: \"sm\",\n                                        withColumnBorders: true,\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        records: chartColors,\n                                        columns: [\n                                            {\n                                                accessor: 'name',\n                                                title: 'Name',\n                                                width: 300\n                                            },\n                                            {\n                                                accessor: 'color',\n                                                title: 'Couleur',\n                                                width: 200\n                                            }\n                                        ],\n                                        minHeight: 400,\n                                        noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                            value: \"protocoles-examen\",\n                            pt: \"md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.TextInput, {\n                                            placeholder: \"Rechercher...\",\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 32\n                                            }, void 0),\n                                            className: \"max-w-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    examProtocols.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-100 p-4 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            size: \"sm\",\n                                            className: \"text-yellow-800\",\n                                            children: \"⚠️ Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                        withTableBorder: true,\n                                        borderRadius: \"sm\",\n                                        withColumnBorders: true,\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        records: examProtocols,\n                                        columns: [\n                                            {\n                                                accessor: 'titre',\n                                                title: 'Titre',\n                                                width: 300\n                                            },\n                                            {\n                                                accessor: 'charge',\n                                                title: 'Charge',\n                                                width: 200\n                                            },\n                                            {\n                                                accessor: 'duree',\n                                                title: 'Durée',\n                                                width: 200\n                                            }\n                                        ],\n                                        minHeight: 400,\n                                        noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                        justify: \"space-between\",\n                                        mt: \"md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                            gap: \"sm\",\n                                            align: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    size: \"sm\",\n                                                    className: \"text-gray-600\",\n                                                    children: \"Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: currentPageProtocols.toString(),\n                                                    onChange: (value)=>setCurrentPageProtocols(Number(value) || 1),\n                                                    data: [\n                                                        '1'\n                                                    ],\n                                                    size: \"xs\",\n                                                    className: \"w-16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    size: \"sm\",\n                                                    className: \"text-gray-600\",\n                                                    children: \"Lignes par Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: itemsPerPageProtocols.toString(),\n                                                    onChange: (value)=>setItemsPerPageProtocols(Number(value) || 2),\n                                                    data: [\n                                                        '2',\n                                                        '5',\n                                                        '10',\n                                                        '20'\n                                                    ],\n                                                    size: \"xs\",\n                                                    className: \"w-16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    size: \"sm\",\n                                                    className: \"text-gray-600\",\n                                                    children: \"0 - de\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                                    gap: \"xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                            variant: \"subtle\",\n                                                            size: \"xs\",\n                                                            disabled: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                            variant: \"subtle\",\n                                                            size: \"xs\",\n                                                            disabled: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                            value: \"motifs-examen\",\n                            pt: \"md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.TextInput, {\n                                            placeholder: \"Rechercher...\",\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 32\n                                            }, void 0),\n                                            className: \"max-w-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    examReasons.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-100 p-4 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            size: \"sm\",\n                                            className: \"text-yellow-800\",\n                                            children: \"⚠️ Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                        withTableBorder: true,\n                                        borderRadius: \"sm\",\n                                        withColumnBorders: true,\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        records: examReasons,\n                                        columns: [\n                                            {\n                                                accessor: 'typeExamen',\n                                                title: 'Type d\\'examen',\n                                                width: 300\n                                            },\n                                            {\n                                                accessor: 'motif',\n                                                title: 'Motif',\n                                                width: 300\n                                            }\n                                        ],\n                                        minHeight: 400,\n                                        noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                        justify: \"space-between\",\n                                        mt: \"md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                            gap: \"sm\",\n                                            align: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    size: \"sm\",\n                                                    className: \"text-gray-600\",\n                                                    children: \"Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: currentPageReasons.toString(),\n                                                    onChange: (value)=>setCurrentPageReasons(Number(value) || 1),\n                                                    data: [\n                                                        '1'\n                                                    ],\n                                                    size: \"xs\",\n                                                    className: \"w-16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    size: \"sm\",\n                                                    className: \"text-gray-600\",\n                                                    children: \"Lignes par Page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: itemsPerPageReasons.toString(),\n                                                    onChange: (value)=>setItemsPerPageReasons(Number(value) || 2),\n                                                    data: [\n                                                        '2',\n                                                        '5',\n                                                        '10',\n                                                        '20'\n                                                    ],\n                                                    size: \"xs\",\n                                                    className: \"w-16\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    size: \"sm\",\n                                                    className: \"text-gray-600\",\n                                                    children: \"0 - de\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Group, {\n                                                    gap: \"xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                            variant: \"subtle\",\n                                                            size: \"xs\",\n                                                            disabled: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                                            variant: \"subtle\",\n                                                            size: \"xs\",\n                                                            disabled: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconChevronLeft_IconChevronRight_IconLungs_IconSearch_tabler_icons_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\Oxymetrie.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Oxymetrie, \"VGGBmr6dbNQrO4It6AXSb9MxHfM=\");\n_c = Oxymetrie;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Oxymetrie);\nvar _c;\n$RefreshReg$(_c, \"Oxymetrie\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvcGFyYW1ldHJhZ2VfZHVfbW9kdWxlX2Rlc19zcGVjaWFsaXRlcy9PeHltZXRyaWUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFjakI7QUFNTTtBQUNpQjtBQXNCOUMsTUFBTWUsWUFBWTs7SUFDaEIseUJBQXlCO0lBQ3pCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHaEIsK0NBQVFBLENBQUM7SUFFM0MsMkJBQTJCO0lBQzNCLE1BQU0sQ0FBQ2lCLHNCQUFzQkMsd0JBQXdCLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNtQix1QkFBdUJDLHlCQUF5QixHQUFHcEIsK0NBQVFBLENBQUM7SUFDbkUsTUFBTSxDQUFDcUIsb0JBQW9CQyxzQkFBc0IsR0FBR3RCLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ3VCLHFCQUFxQkMsdUJBQXVCLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUUvRCxrQkFBa0I7SUFDbEIsTUFBTXlCLGNBQTRCLEVBQUU7SUFDcEMsTUFBTUMsZ0JBQWdDLEVBQUU7SUFDeEMsTUFBTUMsY0FBNEIsRUFBRTtJQUVwQyxnQ0FBZ0M7SUFDaEMsTUFBTUMsa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUlBLE9BQU9iLGFBQWFhO0lBQzFCO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDdEIsdUlBQVNBOzRCQUFDdUIsTUFBTTs0QkFBSUQsV0FBVTs7Ozs7O3NDQUMvQiw4REFBQ0U7NEJBQUdGLFdBQVU7c0NBQW1DOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLckQsOERBQUN6QiwrQ0FBSUE7Z0JBQUM0QixRQUFPO2dCQUFLQyxTQUFRO2dCQUFLQyxRQUFPO2dCQUFLQyxVQUFVOzBCQUNuRCw0RUFBQ25DLCtDQUFJQTtvQkFBQzJCLE9BQU9kO29CQUFXdUIsVUFBVVY7O3NDQUNoQyw4REFBQzFCLCtDQUFJQSxDQUFDcUMsSUFBSTs7OENBQ1IsOERBQUNyQywrQ0FBSUEsQ0FBQ3NDLEdBQUc7b0NBQUNYLE9BQU07OENBQVU7Ozs7Ozs4Q0FDMUIsOERBQUMzQiwrQ0FBSUEsQ0FBQ3NDLEdBQUc7b0NBQUNYLE9BQU07OENBQWdCOzs7Ozs7OENBQ2hDLDhEQUFDM0IsK0NBQUlBLENBQUNzQyxHQUFHO29DQUFDWCxPQUFNOzhDQUFvQjs7Ozs7OzhDQUNwQyw4REFBQzNCLCtDQUFJQSxDQUFDc0MsR0FBRztvQ0FBQ1gsT0FBTTs4Q0FBZ0I7Ozs7Ozs7Ozs7OztzQ0FJbEMsOERBQUMzQiwrQ0FBSUEsQ0FBQ3VDLEtBQUs7NEJBQUNaLE9BQU07NEJBQVVhLElBQUc7c0NBQzdCLDRFQUFDWjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMxQiwrQ0FBSUE7d0NBQUMyQixNQUFLO3dDQUFLVyxJQUFJO3dDQUFLWixXQUFVO2tEQUFxQjs7Ozs7O2tEQUl4RCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUViLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEOzswRUFDQyw4REFBQ3pCLCtDQUFJQTtnRUFBQzJCLE1BQUs7Z0VBQUtXLElBQUk7Z0VBQUtDLElBQUc7MEVBQUs7Ozs7OzswRUFDakMsOERBQUNyQyxpREFBTUE7Z0VBQ0xzQyxhQUFZO2dFQUNaQyxNQUFNLEVBQUU7Z0VBQ1JmLFdBQVU7Ozs7Ozs7Ozs7OztrRUFJZCw4REFBQ0Q7OzBFQUNDLDhEQUFDekIsK0NBQUlBO2dFQUFDMkIsTUFBSztnRUFBS1csSUFBSTtnRUFBS0MsSUFBRzswRUFBSzs7Ozs7OzBFQUNqQyw4REFBQ3pDLG9EQUFTQTtnRUFDUjBDLGFBQVk7Z0VBQ1pkLFdBQVU7Ozs7Ozs7Ozs7OztrRUFJZCw4REFBQ0Q7OzBFQUNDLDhEQUFDekIsK0NBQUlBO2dFQUFDMkIsTUFBSztnRUFBS1csSUFBSTtnRUFBS0MsSUFBRzswRUFBSzs7Ozs7OzBFQUNqQyw4REFBQ3pDLG9EQUFTQTtnRUFDUjBDLGFBQVk7Z0VBQ1pkLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNaEIsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7OzBFQUNDLDhEQUFDekIsK0NBQUlBO2dFQUFDMkIsTUFBSztnRUFBS1csSUFBSTtnRUFBS0MsSUFBRzswRUFBSzs7Ozs7OzBFQUNqQyw4REFBQ3pDLG9EQUFTQTtnRUFDUjBDLGFBQVk7Z0VBQ1pkLFdBQVU7Ozs7Ozs7Ozs7OztrRUFJZCw4REFBQ0Q7OzBFQUNDLDhEQUFDekIsK0NBQUlBO2dFQUFDMkIsTUFBSztnRUFBS1csSUFBSTtnRUFBS0MsSUFBRzswRUFBSzs7Ozs7OzBFQUNqQyw4REFBQ3pDLG9EQUFTQTtnRUFDUjBDLGFBQVk7Z0VBQ1pkLFdBQVU7Ozs7Ozs7Ozs7OztrRUFJZCw4REFBQ0Q7OzBFQUNDLDhEQUFDekIsK0NBQUlBO2dFQUFDMkIsTUFBSztnRUFBS1csSUFBSTtnRUFBS0MsSUFBRzswRUFBSzs7Ozs7OzBFQUNqQyw4REFBQ3pDLG9EQUFTQTtnRUFDUjBDLGFBQVk7Z0VBQ1pkLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFPbEIsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDdkIsbURBQVFBOzRDQUNQdUMsT0FBTTs0Q0FDTmhCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT2xCLDhEQUFDN0IsK0NBQUlBLENBQUN1QyxLQUFLOzRCQUFDWixPQUFNOzRCQUFnQmEsSUFBRztzQ0FDbkMsNEVBQUNaO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUM1QixvREFBU0E7NENBQ1IwQyxhQUFZOzRDQUNaRywyQkFBYSw4REFBQ3RDLHdJQUFVQTtnREFBQ3NCLE1BQU07Ozs7Ozs0Q0FDL0JELFdBQVU7Ozs7Ozs7Ozs7O29DQUliTixZQUFZd0IsTUFBTSxLQUFLLGtCQUN0Qiw4REFBQ25CO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDMUIsK0NBQUlBOzRDQUFDMkIsTUFBSzs0Q0FBS0QsV0FBVTtzREFBa0I7Ozs7Ozs7Ozs7a0VBSzlDLDhEQUFDbEIsd0RBQVNBO3dDQUNScUMsZUFBZTt3Q0FDZkMsY0FBYTt3Q0FDYkMsaUJBQWlCO3dDQUNqQkMsT0FBTzt3Q0FDUEMsZ0JBQWdCO3dDQUNoQkMsU0FBUzlCO3dDQUNUK0IsU0FBUzs0Q0FDUDtnREFBRUMsVUFBVTtnREFBUUMsT0FBTztnREFBUUMsT0FBTzs0Q0FBSTs0Q0FDOUM7Z0RBQUVGLFVBQVU7Z0RBQVNDLE9BQU87Z0RBQVdDLE9BQU87NENBQUk7eUNBQ25EO3dDQUNEQyxXQUFXO3dDQUNYQyxlQUFjOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPdEIsOERBQUMzRCwrQ0FBSUEsQ0FBQ3VDLEtBQUs7NEJBQUNaLE9BQU07NEJBQW9CYSxJQUFHO3NDQUN2Qyw0RUFBQ1o7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQzVCLG9EQUFTQTs0Q0FDUjBDLGFBQVk7NENBQ1pHLDJCQUFhLDhEQUFDdEMsd0lBQVVBO2dEQUFDc0IsTUFBTTs7Ozs7OzRDQUMvQkQsV0FBVTs7Ozs7Ozs7Ozs7b0NBSWJMLGNBQWN1QixNQUFNLEtBQUssa0JBQ3hCLDhEQUFDbkI7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUMxQiwrQ0FBSUE7NENBQUMyQixNQUFLOzRDQUFLRCxXQUFVO3NEQUFrQjs7Ozs7Ozs7OztrRUFLOUMsOERBQUNsQix3REFBU0E7d0NBQ1JxQyxlQUFlO3dDQUNmQyxjQUFhO3dDQUNiQyxpQkFBaUI7d0NBQ2pCQyxPQUFPO3dDQUNQQyxnQkFBZ0I7d0NBQ2hCQyxTQUFTN0I7d0NBQ1Q4QixTQUFTOzRDQUNQO2dEQUFFQyxVQUFVO2dEQUFTQyxPQUFPO2dEQUFTQyxPQUFPOzRDQUFJOzRDQUNoRDtnREFBRUYsVUFBVTtnREFBVUMsT0FBTztnREFBVUMsT0FBTzs0Q0FBSTs0Q0FDbEQ7Z0RBQUVGLFVBQVU7Z0RBQVNDLE9BQU87Z0RBQVNDLE9BQU87NENBQUk7eUNBQ2pEO3dDQUNEQyxXQUFXO3dDQUNYQyxlQUFjOzs7Ozs7a0RBS2xCLDhEQUFDekQsaURBQUtBO3dDQUFDMEQsU0FBUTt3Q0FBZ0JDLElBQUc7a0RBQ2hDLDRFQUFDM0QsaURBQUtBOzRDQUFDNEQsS0FBSTs0Q0FBS0MsT0FBTTs7OERBQ3BCLDhEQUFDNUQsK0NBQUlBO29EQUFDMkIsTUFBSztvREFBS0QsV0FBVTs4REFBZ0I7Ozs7Ozs4REFDMUMsOERBQUN4QixpREFBTUE7b0RBQ0xzQixPQUFPWixxQkFBcUJpRCxRQUFRO29EQUNwQzVCLFVBQVUsQ0FBQ1QsUUFBVVgsd0JBQXdCaUQsT0FBT3RDLFVBQVU7b0RBQzlEaUIsTUFBTTt3REFBQztxREFBSTtvREFDWGQsTUFBSztvREFDTEQsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDMUIsK0NBQUlBO29EQUFDMkIsTUFBSztvREFBS0QsV0FBVTs4REFBZ0I7Ozs7Ozs4REFDMUMsOERBQUN4QixpREFBTUE7b0RBQ0xzQixPQUFPVixzQkFBc0IrQyxRQUFRO29EQUNyQzVCLFVBQVUsQ0FBQ1QsUUFBVVQseUJBQXlCK0MsT0FBT3RDLFVBQVU7b0RBQy9EaUIsTUFBTTt3REFBQzt3REFBSzt3REFBSzt3REFBTTtxREFBSztvREFDNUJkLE1BQUs7b0RBQ0xELFdBQVU7Ozs7Ozs4REFFWiw4REFBQzFCLCtDQUFJQTtvREFBQzJCLE1BQUs7b0RBQUtELFdBQVU7OERBQWdCOzs7Ozs7OERBQzFDLDhEQUFDM0IsaURBQUtBO29EQUFDNEQsS0FBSTs7c0VBQ1QsOERBQUMvRCxrREFBTUE7NERBQUNtRSxTQUFROzREQUFTcEMsTUFBSzs0REFBS3FDLFFBQVE7c0VBQ3pDLDRFQUFDMUQsd0lBQWVBO2dFQUFDcUIsTUFBTTs7Ozs7Ozs7Ozs7c0VBRXpCLDhEQUFDL0Isa0RBQU1BOzREQUFDbUUsU0FBUTs0REFBU3BDLE1BQUs7NERBQUtxQyxRQUFRO3NFQUN6Qyw0RUFBQ3pELHdJQUFnQkE7Z0VBQUNvQixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBU3BDLDhEQUFDOUIsK0NBQUlBLENBQUN1QyxLQUFLOzRCQUFDWixPQUFNOzRCQUFnQmEsSUFBRztzQ0FDbkMsNEVBQUNaO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUM1QixvREFBU0E7NENBQ1IwQyxhQUFZOzRDQUNaRywyQkFBYSw4REFBQ3RDLHdJQUFVQTtnREFBQ3NCLE1BQU07Ozs7Ozs0Q0FDL0JELFdBQVU7Ozs7Ozs7Ozs7O29DQUliSixZQUFZc0IsTUFBTSxLQUFLLGtCQUN0Qiw4REFBQ25CO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDMUIsK0NBQUlBOzRDQUFDMkIsTUFBSzs0Q0FBS0QsV0FBVTtzREFBa0I7Ozs7Ozs7Ozs7a0VBSzlDLDhEQUFDbEIsd0RBQVNBO3dDQUNScUMsZUFBZTt3Q0FDZkMsY0FBYTt3Q0FDYkMsaUJBQWlCO3dDQUNqQkMsT0FBTzt3Q0FDUEMsZ0JBQWdCO3dDQUNoQkMsU0FBUzVCO3dDQUNUNkIsU0FBUzs0Q0FDUDtnREFBRUMsVUFBVTtnREFBY0MsT0FBTztnREFBa0JDLE9BQU87NENBQUk7NENBQzlEO2dEQUFFRixVQUFVO2dEQUFTQyxPQUFPO2dEQUFTQyxPQUFPOzRDQUFJO3lDQUNqRDt3Q0FDREMsV0FBVzt3Q0FDWEMsZUFBYzs7Ozs7O2tEQUtsQiw4REFBQ3pELGlEQUFLQTt3Q0FBQzBELFNBQVE7d0NBQWdCQyxJQUFHO2tEQUNoQyw0RUFBQzNELGlEQUFLQTs0Q0FBQzRELEtBQUk7NENBQUtDLE9BQU07OzhEQUNwQiw4REFBQzVELCtDQUFJQTtvREFBQzJCLE1BQUs7b0RBQUtELFdBQVU7OERBQWdCOzs7Ozs7OERBQzFDLDhEQUFDeEIsaURBQU1BO29EQUNMc0IsT0FBT1IsbUJBQW1CNkMsUUFBUTtvREFDbEM1QixVQUFVLENBQUNULFFBQVVQLHNCQUFzQjZDLE9BQU90QyxVQUFVO29EQUM1RGlCLE1BQU07d0RBQUM7cURBQUk7b0RBQ1hkLE1BQUs7b0RBQ0xELFdBQVU7Ozs7Ozs4REFFWiw4REFBQzFCLCtDQUFJQTtvREFBQzJCLE1BQUs7b0RBQUtELFdBQVU7OERBQWdCOzs7Ozs7OERBQzFDLDhEQUFDeEIsaURBQU1BO29EQUNMc0IsT0FBT04sb0JBQW9CMkMsUUFBUTtvREFDbkM1QixVQUFVLENBQUNULFFBQVVMLHVCQUF1QjJDLE9BQU90QyxVQUFVO29EQUM3RGlCLE1BQU07d0RBQUM7d0RBQUs7d0RBQUs7d0RBQU07cURBQUs7b0RBQzVCZCxNQUFLO29EQUNMRCxXQUFVOzs7Ozs7OERBRVosOERBQUMxQiwrQ0FBSUE7b0RBQUMyQixNQUFLO29EQUFLRCxXQUFVOzhEQUFnQjs7Ozs7OzhEQUMxQyw4REFBQzNCLGlEQUFLQTtvREFBQzRELEtBQUk7O3NFQUNULDhEQUFDL0Qsa0RBQU1BOzREQUFDbUUsU0FBUTs0REFBU3BDLE1BQUs7NERBQUtxQyxRQUFRO3NFQUN6Qyw0RUFBQzFELHdJQUFlQTtnRUFBQ3FCLE1BQU07Ozs7Ozs7Ozs7O3NFQUV6Qiw4REFBQy9CLGtEQUFNQTs0REFBQ21FLFNBQVE7NERBQVNwQyxNQUFLOzREQUFLcUMsUUFBUTtzRUFDekMsNEVBQUN6RCx3SUFBZ0JBO2dFQUFDb0IsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXOUM7R0FoU01sQjtLQUFBQTtBQWtTTixpRUFBZUEsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb3JyeVxcRGVza3RvcFxcc3ZwXFxmcm9udGVuZFxcbWVkaWNpbmVcXHNyY1xcYXBwXFwoZGFzaGJvYXJkKVxcc2V0dGluZ3NcXHBhcmFtZXRyYWdlX2R1X21vZHVsZV9kZXNfc3BlY2lhbGl0ZXNcXE94eW1ldHJpZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7XHJcbiAgQnV0dG9uLFxyXG4gIFRhYnMsXHJcbiAgVGV4dElucHV0LFxyXG4gIEdyb3VwLFxyXG4gIFRleHQsXHJcbiAgQ2FyZCxcclxuICBTZWxlY3QsXHJcbiAgTnVtYmVySW5wdXQsXHJcbiAgQ2hlY2tib3gsXHJcbiAgUGFnaW5hdGlvbixcclxuICBUYWJsZSxcclxuICBCYWRnZVxyXG59IGZyb20gJ0BtYW50aW5lL2NvcmUnO1xyXG5pbXBvcnQge1xyXG4gIEljb25MdW5ncyxcclxuICBJY29uU2VhcmNoLFxyXG4gIEljb25DaGV2cm9uTGVmdCxcclxuICBJY29uQ2hldnJvblJpZ2h0XHJcbn0gZnJvbSAnQHRhYmxlci9pY29ucy1yZWFjdCc7XHJcbmltcG9ydCB7IERhdGFUYWJsZSB9IGZyb20gJ21hbnRpbmUtZGF0YXRhYmxlJztcclxuXHJcbi8vIFR5cGVzXHJcbmludGVyZmFjZSBDaGFydENvbG9yIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBjb2xvcjogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgRXhhbVByb3RvY29sIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIHRpdHJlOiBzdHJpbmc7XHJcbiAgY2hhcmdlOiBzdHJpbmc7XHJcbiAgZHVyZWU6IHN0cmluZztcclxufVxyXG5cclxuaW50ZXJmYWNlIEV4YW1SZWFzb24ge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgdHlwZUV4YW1lbjogc3RyaW5nO1xyXG4gIG1vdGlmOiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IE94eW1ldHJpZSA9ICgpID0+IHtcclxuICAvLyDDiXRhdHMgcG91ciBsZXMgb25nbGV0c1xyXG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZSgnZ2VuZXJhbCcpO1xyXG5cclxuICAvLyDDiXRhdHMgcG91ciBsYSBwYWdpbmF0aW9uXHJcbiAgY29uc3QgW2N1cnJlbnRQYWdlUHJvdG9jb2xzLCBzZXRDdXJyZW50UGFnZVByb3RvY29sc10gPSB1c2VTdGF0ZSgxKTtcclxuICBjb25zdCBbaXRlbXNQZXJQYWdlUHJvdG9jb2xzLCBzZXRJdGVtc1BlclBhZ2VQcm90b2NvbHNdID0gdXNlU3RhdGUoMik7XHJcbiAgY29uc3QgW2N1cnJlbnRQYWdlUmVhc29ucywgc2V0Q3VycmVudFBhZ2VSZWFzb25zXSA9IHVzZVN0YXRlKDEpO1xyXG4gIGNvbnN0IFtpdGVtc1BlclBhZ2VSZWFzb25zLCBzZXRJdGVtc1BlclBhZ2VSZWFzb25zXSA9IHVzZVN0YXRlKDIpO1xyXG5cclxuICAvLyBEb25uw6llcyBtb2Nrw6llc1xyXG4gIGNvbnN0IGNoYXJ0Q29sb3JzOiBDaGFydENvbG9yW10gPSBbXTtcclxuICBjb25zdCBleGFtUHJvdG9jb2xzOiBFeGFtUHJvdG9jb2xbXSA9IFtdO1xyXG4gIGNvbnN0IGV4YW1SZWFzb25zOiBFeGFtUmVhc29uW10gPSBbXTtcclxuXHJcbiAgLy8gR2VzdGlvbm5haXJlIHBvdXIgbGVzIG9uZ2xldHNcclxuICBjb25zdCBoYW5kbGVUYWJDaGFuZ2UgPSAodmFsdWU6IHN0cmluZyB8IG51bGwpID0+IHtcclxuICAgIGlmICh2YWx1ZSkgc2V0QWN0aXZlVGFiKHZhbHVlKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYmctZ3JheS01MCBtaW4taC1zY3JlZW5cIj5cclxuICAgICAgey8qIEhlYWRlciAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cclxuICAgICAgICAgIDxJY29uTHVuZ3Mgc2l6ZT17MzJ9IGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDBcIiAvPlxyXG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwXCI+T3h5bcOpdHJpZTwvaDE+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIE9uZ2xldHMgcHJpbmNpcGF1eCAqL31cclxuICAgICAgPENhcmQgc2hhZG93PVwic21cIiBwYWRkaW5nPVwibGdcIiByYWRpdXM9XCJtZFwiIHdpdGhCb3JkZXI+XHJcbiAgICAgICAgPFRhYnMgdmFsdWU9e2FjdGl2ZVRhYn0gb25DaGFuZ2U9e2hhbmRsZVRhYkNoYW5nZX0+XHJcbiAgICAgICAgICA8VGFicy5MaXN0PlxyXG4gICAgICAgICAgICA8VGFicy5UYWIgdmFsdWU9XCJnZW5lcmFsXCI+R8OpbsOpcmFsPC9UYWJzLlRhYj5cclxuICAgICAgICAgICAgPFRhYnMuVGFiIHZhbHVlPVwiY291bGV1ci1jaGFydFwiPkNvdWxldXIgZHUgY2hhcnQ8L1RhYnMuVGFiPlxyXG4gICAgICAgICAgICA8VGFicy5UYWIgdmFsdWU9XCJwcm90b2NvbGVzLWV4YW1lblwiPlByb3RvY29sZXMgZCZhcG9zO2V4YW1lbjwvVGFicy5UYWI+XHJcbiAgICAgICAgICAgIDxUYWJzLlRhYiB2YWx1ZT1cIm1vdGlmcy1leGFtZW5cIj5Nb3RpZnMgZCZhcG9zO2V4YW1lbjwvVGFicy5UYWI+XHJcbiAgICAgICAgICA8L1RhYnMuTGlzdD5cclxuXHJcbiAgICAgICAgICB7LyogT25nbGV0IEfDqW7DqXJhbCAqL31cclxuICAgICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwiZ2VuZXJhbFwiIHB0PVwibWRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgICA8VGV4dCBzaXplPVwibGdcIiBmdz17NjAwfSBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktODAwIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgIEV4YW1lbiB2YWxldXIgcGFyIGTDqWZhdXRcclxuICAgICAgICAgICAgICA8L1RleHQ+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxyXG4gICAgICAgICAgICAgICAgey8qIENvbG9ubmUgZ2F1Y2hlICovfVxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBmdz17NTAwfSBtYj1cInhzXCI+VHlwZSBkJmFwb3M7ZXhhbWVuPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGRhdGE9e1tdfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0gbWI9XCJ4c1wiPk3DqHRyZS90b3VyPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUmFpc29uXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGZ3PXs1MDB9IG1iPVwieHNcIj5SYWlzb248L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJSYWlzb25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgIHsvKiBDb2xvbm5lIGRyb2l0ZSAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0gbWI9XCJ4c1wiPkNvdXJ0ZSBwYXVzZTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBmdz17NTAwfSBtYj1cInhzXCI+RHVyw6llIGRlIHLDqWN1cMOpcmF0aW9uPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGZ3PXs1MDB9IG1iPVwieHNcIj5DYXVzZSBkJmFwb3M7YXJyw6p0PC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQ2F1c2UgZCdhcnLDqnRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICB7LyogQ2hlY2tib3ggKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02XCI+XHJcbiAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJFbnJlZ2lzdHJlbWVudCBhdXRvbWF0aXF1ZSDDoCBsYSBmaW4gZCdleGFtZW5cIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9UYWJzLlBhbmVsPlxyXG5cclxuICAgICAgICAgIHsvKiBPbmdsZXQgQ291bGV1ciBkdSBjaGFydCAqL31cclxuICAgICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwiY291bGV1ci1jaGFydFwiIHB0PVwibWRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cclxuICAgICAgICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJSZWNoZXJjaGVyLi4uXCJcclxuICAgICAgICAgICAgICAgICAgbGVmdFNlY3Rpb249ezxJY29uU2VhcmNoIHNpemU9ezE2fSAvPn1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF4LXctbWRcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAge2NoYXJ0Q29sb3JzLmxlbmd0aCA9PT0gMCA/IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTEwMCBwLTQgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy04MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICDimqDvuI8gQXVjdW4gw6lsw6ltZW50IHRyb3V2w6kuXHJcbiAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlXHJcbiAgICAgICAgICAgICAgICAgIHdpdGhUYWJsZUJvcmRlclxyXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgIHdpdGhDb2x1bW5Cb3JkZXJzXHJcbiAgICAgICAgICAgICAgICAgIHN0cmlwZWRcclxuICAgICAgICAgICAgICAgICAgaGlnaGxpZ2h0T25Ib3ZlclxyXG4gICAgICAgICAgICAgICAgICByZWNvcmRzPXtjaGFydENvbG9yc31cclxuICAgICAgICAgICAgICAgICAgY29sdW1ucz17W1xyXG4gICAgICAgICAgICAgICAgICAgIHsgYWNjZXNzb3I6ICduYW1lJywgdGl0bGU6ICdOYW1lJywgd2lkdGg6IDMwMCB9LFxyXG4gICAgICAgICAgICAgICAgICAgIHsgYWNjZXNzb3I6ICdjb2xvcicsIHRpdGxlOiAnQ291bGV1cicsIHdpZHRoOiAyMDAgfVxyXG4gICAgICAgICAgICAgICAgICBdfVxyXG4gICAgICAgICAgICAgICAgICBtaW5IZWlnaHQ9ezQwMH1cclxuICAgICAgICAgICAgICAgICAgbm9SZWNvcmRzVGV4dD1cIkF1Y3VuIMOpbMOpbWVudCB0cm91dsOpLlwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9UYWJzLlBhbmVsPlxyXG5cclxuICAgICAgICAgIHsvKiBPbmdsZXQgUHJvdG9jb2xlcyBkJ2V4YW1lbiAqL31cclxuICAgICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwicHJvdG9jb2xlcy1leGFtZW5cIiBwdD1cIm1kXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XHJcbiAgICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUmVjaGVyY2hlci4uLlwiXHJcbiAgICAgICAgICAgICAgICAgIGxlZnRTZWN0aW9uPXs8SWNvblNlYXJjaCBzaXplPXsxNn0gLz59XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LW1kXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIHtleGFtUHJvdG9jb2xzLmxlbmd0aCA9PT0gMCA/IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTEwMCBwLTQgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy04MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICDimqDvuI8gQXVjdW4gw6lsw6ltZW50IHRyb3V2w6kuXHJcbiAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlXHJcbiAgICAgICAgICAgICAgICAgIHdpdGhUYWJsZUJvcmRlclxyXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgIHdpdGhDb2x1bW5Cb3JkZXJzXHJcbiAgICAgICAgICAgICAgICAgIHN0cmlwZWRcclxuICAgICAgICAgICAgICAgICAgaGlnaGxpZ2h0T25Ib3ZlclxyXG4gICAgICAgICAgICAgICAgICByZWNvcmRzPXtleGFtUHJvdG9jb2xzfVxyXG4gICAgICAgICAgICAgICAgICBjb2x1bW5zPXtbXHJcbiAgICAgICAgICAgICAgICAgICAgeyBhY2Nlc3NvcjogJ3RpdHJlJywgdGl0bGU6ICdUaXRyZScsIHdpZHRoOiAzMDAgfSxcclxuICAgICAgICAgICAgICAgICAgICB7IGFjY2Vzc29yOiAnY2hhcmdlJywgdGl0bGU6ICdDaGFyZ2UnLCB3aWR0aDogMjAwIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgeyBhY2Nlc3NvcjogJ2R1cmVlJywgdGl0bGU6ICdEdXLDqWUnLCB3aWR0aDogMjAwIH1cclxuICAgICAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgICAgICAgbWluSGVpZ2h0PXs0MDB9XHJcbiAgICAgICAgICAgICAgICAgIG5vUmVjb3Jkc1RleHQ9XCJBdWN1biDDqWzDqW1lbnQgdHJvdXbDqS5cIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICB7LyogUGFnaW5hdGlvbiBwb3VyIHByb3RvY29sZXMgKi99XHJcbiAgICAgICAgICAgICAgPEdyb3VwIGp1c3RpZnk9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9XCJtZFwiPlxyXG4gICAgICAgICAgICAgICAgPEdyb3VwIGdhcD1cInNtXCIgYWxpZ249XCJjZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlBhZ2U8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y3VycmVudFBhZ2VQcm90b2NvbHMudG9TdHJpbmcoKX1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRDdXJyZW50UGFnZVByb3RvY29scyhOdW1iZXIodmFsdWUpIHx8IDEpfVxyXG4gICAgICAgICAgICAgICAgICAgIGRhdGE9e1snMSddfVxyXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJ4c1wiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0xNlwiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5MaWduZXMgcGFyIFBhZ2U8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aXRlbXNQZXJQYWdlUHJvdG9jb2xzLnRvU3RyaW5nKCl9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWx1ZSkgPT4gc2V0SXRlbXNQZXJQYWdlUHJvdG9jb2xzKE51bWJlcih2YWx1ZSkgfHwgMil9XHJcbiAgICAgICAgICAgICAgICAgICAgZGF0YT17WycyJywgJzUnLCAnMTAnLCAnMjAnXX1cclxuICAgICAgICAgICAgICAgICAgICBzaXplPVwieHNcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTZcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+MCAtIGRlPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICA8R3JvdXAgZ2FwPVwieHNcIj5cclxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJzdWJ0bGVcIiBzaXplPVwieHNcIiBkaXNhYmxlZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxJY29uQ2hldnJvbkxlZnQgc2l6ZT17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwic3VidGxlXCIgc2l6ZT1cInhzXCIgZGlzYWJsZWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8SWNvbkNoZXZyb25SaWdodCBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9Hcm91cD5cclxuICAgICAgICAgICAgICAgIDwvR3JvdXA+XHJcbiAgICAgICAgICAgICAgPC9Hcm91cD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L1RhYnMuUGFuZWw+XHJcblxyXG4gICAgICAgICAgey8qIE9uZ2xldCBNb3RpZnMgZCdleGFtZW4gKi99XHJcbiAgICAgICAgICA8VGFicy5QYW5lbCB2YWx1ZT1cIm1vdGlmcy1leGFtZW5cIiBwdD1cIm1kXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XHJcbiAgICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUmVjaGVyY2hlci4uLlwiXHJcbiAgICAgICAgICAgICAgICAgIGxlZnRTZWN0aW9uPXs8SWNvblNlYXJjaCBzaXplPXsxNn0gLz59XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LW1kXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIHtleGFtUmVhc29ucy5sZW5ndGggPT09IDAgPyAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy0xMDAgcC00IHJvdW5kZWQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctODAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAg4pqg77iPIEF1Y3VuIMOpbMOpbWVudCB0cm91dsOpLlxyXG4gICAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVxyXG4gICAgICAgICAgICAgICAgICB3aXRoVGFibGVCb3JkZXJcclxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICB3aXRoQ29sdW1uQm9yZGVyc1xyXG4gICAgICAgICAgICAgICAgICBzdHJpcGVkXHJcbiAgICAgICAgICAgICAgICAgIGhpZ2hsaWdodE9uSG92ZXJcclxuICAgICAgICAgICAgICAgICAgcmVjb3Jkcz17ZXhhbVJlYXNvbnN9XHJcbiAgICAgICAgICAgICAgICAgIGNvbHVtbnM9e1tcclxuICAgICAgICAgICAgICAgICAgICB7IGFjY2Vzc29yOiAndHlwZUV4YW1lbicsIHRpdGxlOiAnVHlwZSBkXFwnZXhhbWVuJywgd2lkdGg6IDMwMCB9LFxyXG4gICAgICAgICAgICAgICAgICAgIHsgYWNjZXNzb3I6ICdtb3RpZicsIHRpdGxlOiAnTW90aWYnLCB3aWR0aDogMzAwIH1cclxuICAgICAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgICAgICAgbWluSGVpZ2h0PXs0MDB9XHJcbiAgICAgICAgICAgICAgICAgIG5vUmVjb3Jkc1RleHQ9XCJBdWN1biDDqWzDqW1lbnQgdHJvdXbDqS5cIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICB7LyogUGFnaW5hdGlvbiBwb3VyIG1vdGlmcyAqL31cclxuICAgICAgICAgICAgICA8R3JvdXAganVzdGlmeT1cInNwYWNlLWJldHdlZW5cIiBtdD1cIm1kXCI+XHJcbiAgICAgICAgICAgICAgICA8R3JvdXAgZ2FwPVwic21cIiBhbGlnbj1cImNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+UGFnZTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjdXJyZW50UGFnZVJlYXNvbnMudG9TdHJpbmcoKX1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRDdXJyZW50UGFnZVJlYXNvbnMoTnVtYmVyKHZhbHVlKSB8fCAxKX1cclxuICAgICAgICAgICAgICAgICAgICBkYXRhPXtbJzEnXX1cclxuICAgICAgICAgICAgICAgICAgICBzaXplPVwieHNcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTZcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TGlnbmVzIHBhciBQYWdlPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2l0ZW1zUGVyUGFnZVJlYXNvbnMudG9TdHJpbmcoKX1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRJdGVtc1BlclBhZ2VSZWFzb25zKE51bWJlcih2YWx1ZSkgfHwgMil9XHJcbiAgICAgICAgICAgICAgICAgICAgZGF0YT17WycyJywgJzUnLCAnMTAnLCAnMjAnXX1cclxuICAgICAgICAgICAgICAgICAgICBzaXplPVwieHNcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTZcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+MCAtIGRlPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICA8R3JvdXAgZ2FwPVwieHNcIj5cclxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJzdWJ0bGVcIiBzaXplPVwieHNcIiBkaXNhYmxlZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxJY29uQ2hldnJvbkxlZnQgc2l6ZT17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwic3VidGxlXCIgc2l6ZT1cInhzXCIgZGlzYWJsZWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8SWNvbkNoZXZyb25SaWdodCBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9Hcm91cD5cclxuICAgICAgICAgICAgICAgIDwvR3JvdXA+XHJcbiAgICAgICAgICAgICAgPC9Hcm91cD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L1RhYnMuUGFuZWw+XHJcbiAgICAgICAgPC9UYWJzPlxyXG4gICAgICA8L0NhcmQ+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgT3h5bWV0cmllO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkJ1dHRvbiIsIlRhYnMiLCJUZXh0SW5wdXQiLCJHcm91cCIsIlRleHQiLCJDYXJkIiwiU2VsZWN0IiwiQ2hlY2tib3giLCJJY29uTHVuZ3MiLCJJY29uU2VhcmNoIiwiSWNvbkNoZXZyb25MZWZ0IiwiSWNvbkNoZXZyb25SaWdodCIsIkRhdGFUYWJsZSIsIk94eW1ldHJpZSIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsImN1cnJlbnRQYWdlUHJvdG9jb2xzIiwic2V0Q3VycmVudFBhZ2VQcm90b2NvbHMiLCJpdGVtc1BlclBhZ2VQcm90b2NvbHMiLCJzZXRJdGVtc1BlclBhZ2VQcm90b2NvbHMiLCJjdXJyZW50UGFnZVJlYXNvbnMiLCJzZXRDdXJyZW50UGFnZVJlYXNvbnMiLCJpdGVtc1BlclBhZ2VSZWFzb25zIiwic2V0SXRlbXNQZXJQYWdlUmVhc29ucyIsImNoYXJ0Q29sb3JzIiwiZXhhbVByb3RvY29scyIsImV4YW1SZWFzb25zIiwiaGFuZGxlVGFiQ2hhbmdlIiwidmFsdWUiLCJkaXYiLCJjbGFzc05hbWUiLCJzaXplIiwiaDEiLCJzaGFkb3ciLCJwYWRkaW5nIiwicmFkaXVzIiwid2l0aEJvcmRlciIsIm9uQ2hhbmdlIiwiTGlzdCIsIlRhYiIsIlBhbmVsIiwicHQiLCJmdyIsIm1iIiwicGxhY2Vob2xkZXIiLCJkYXRhIiwibGFiZWwiLCJsZWZ0U2VjdGlvbiIsImxlbmd0aCIsIndpdGhUYWJsZUJvcmRlciIsImJvcmRlclJhZGl1cyIsIndpdGhDb2x1bW5Cb3JkZXJzIiwic3RyaXBlZCIsImhpZ2hsaWdodE9uSG92ZXIiLCJyZWNvcmRzIiwiY29sdW1ucyIsImFjY2Vzc29yIiwidGl0bGUiLCJ3aWR0aCIsIm1pbkhlaWdodCIsIm5vUmVjb3Jkc1RleHQiLCJqdXN0aWZ5IiwibXQiLCJnYXAiLCJhbGlnbiIsInRvU3RyaW5nIiwiTnVtYmVyIiwidmFyaWFudCIsImRpc2FibGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/Oxymetrie.tsx\n"));

/***/ })

});