"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHome.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSettings.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCalendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconEdit.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst AccueilEtAgenda = ()=>{\n    _s();\n    // États pour les onglets\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    // États pour les modales\n    const [motifModalOpened, setMotifModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [agendaModalOpened, setAgendaModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [salleModalOpened, setSalleModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [evenementModalOpened, setEvenementModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMotif, setEditingMotif] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingAgenda, setEditingAgenda] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSalle, setEditingSalle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingEvenement, setEditingEvenement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour les formulaires\n    const [motifForm, setMotifForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: '',\n        description: '',\n        duration: 15,\n        color: '#9b4d93',\n        colorRayee: '#792485',\n        agendaDefaut: '',\n        servicesDesignes: '',\n        couleurSombre: false\n    });\n    const [agendaForm, setAgendaForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        servicesDesignes: '',\n        isResource: false\n    });\n    const [salleForm, setSalleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        type: 'attente',\n        capacity: 1,\n        servicesDesignes: ''\n    });\n    const [evenementForm, setEvenementForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        dateDebut: '',\n        dateFin: '',\n        color: '#9b4d93',\n        indisponible: false,\n        permanent: false,\n        touteJournee: false,\n        tousLesJours: false,\n        cabinet: false\n    });\n    // États pour les paramètres généraux\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        heureOuverture: '08:00',\n        heureFermeture: '20:00',\n        premierJourSemaine: 'Lundi',\n        vueDefautCalendrier: 'Semaine',\n        minimumIntervalle: '15 minutes',\n        fuseauHoraire: 'Africa/Casablanca',\n        cacherJoursFermeture: false,\n        curseurAgendaHeureCurrent: true,\n        filtre24hCabinet: true,\n        utiliserAbbreviation: false,\n        utiliserListePatients: false,\n        afficherSeulementVisitesTerminees: false\n    });\n    // Données de test pour les motifs\n    const [motifs, setMotifs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            code: 'CNS',\n            description: 'Consultation',\n            duration: 15,\n            color: '#9b4d93',\n            colorRayee: '#792485'\n        },\n        {\n            id: 2,\n            code: 'CTR',\n            description: 'Contrôle',\n            duration: 15,\n            color: '#c05889',\n            colorRayee: '#7d1868'\n        },\n        {\n            id: 3,\n            code: 'CHI',\n            description: 'Chirurgie/Paro',\n            duration: 30,\n            color: '#c14317',\n            colorRayee: '#922a12'\n        },\n        {\n            id: 4,\n            code: 'COM',\n            description: 'Composite',\n            duration: 15,\n            color: '#2836cd',\n            colorRayee: '#091486'\n        },\n        {\n            id: 5,\n            code: 'DS',\n            description: 'Dépose Sutures',\n            duration: 15,\n            color: '#2e7d32',\n            colorRayee: '#1b5e20'\n        },\n        {\n            id: 6,\n            code: 'CS',\n            description: '1er Consultation',\n            duration: 15,\n            color: '#e91e63',\n            colorRayee: '#c2185b'\n        },\n        {\n            id: 7,\n            code: 'DEV',\n            description: 'Devis',\n            duration: 15,\n            color: 'rgb(23, 160, 131)',\n            colorRayee: '#024f3f'\n        },\n        {\n            id: 8,\n            code: 'END',\n            description: 'Endodontie',\n            duration: 60,\n            color: '#a08523',\n            colorRayee: '#564507'\n        },\n        {\n            id: 9,\n            code: 'FOR',\n            description: 'Formation',\n            duration: 60,\n            color: '#982552',\n            colorRayee: '#72183c'\n        },\n        {\n            id: 10,\n            code: 'IMP',\n            description: 'Implantologie',\n            duration: 60,\n            color: '#388983',\n            colorRayee: '#26645e'\n        },\n        {\n            id: 11,\n            code: 'ORT',\n            description: 'Orthodontie',\n            duration: 30,\n            color: '#464098',\n            colorRayee: '#1a7c81'\n        },\n        {\n            id: 12,\n            code: 'CM',\n            description: 'FS Chassis/Monta',\n            duration: 15,\n            color: '#6c407a',\n            colorRayee: '#9c4168'\n        },\n        {\n            id: 13,\n            code: 'POS',\n            description: 'PA Pose',\n            duration: 15,\n            color: '#b25953',\n            colorRayee: '#9c3e3e'\n        },\n        {\n            id: 14,\n            code: 'EMP',\n            description: 'PC TP EMP',\n            duration: 30,\n            color: '#4c8d57',\n            colorRayee: '#366e42'\n        },\n        {\n            id: 15,\n            code: 'ARM',\n            description: 'PC ESS Armature',\n            duration: 30,\n            color: '#3291ef',\n            colorRayee: '#0d667b'\n        },\n        {\n            id: 16,\n            code: 'SCE',\n            description: 'PC Scellement',\n            duration: 15,\n            color: '#c2185b',\n            colorRayee: '#880e4f'\n        },\n        {\n            id: 17,\n            code: 'PRO',\n            description: 'Prophylaxie',\n            duration: 15,\n            color: '#009688',\n            colorRayee: '#00695c'\n        }\n    ]);\n    // Données de test pour les agendas\n    const [agendas, setAgendas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // États pour la recherche\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Filtrer les motifs selon la recherche\n    const filteredMotifs = motifs.filter((motif)=>motif.code.toLowerCase().includes(searchTerm.toLowerCase()) || motif.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    // Jours de la semaine\n    const joursSemaine = [\n        'Lundi',\n        'Mardi',\n        'Mercredi',\n        'Jeudi',\n        'Vendredi',\n        'Samedi',\n        'Dimanche'\n    ];\n    // Options pour les selects\n    const vueCalendrierOptions = [\n        {\n            value: 'Semaine',\n            label: 'Semaine'\n        },\n        {\n            value: 'Mois',\n            label: 'Mois'\n        },\n        {\n            value: 'Jour',\n            label: 'Jour'\n        }\n    ];\n    const intervalleOptions = [\n        {\n            value: '5 minutes',\n            label: '5 minutes'\n        },\n        {\n            value: '10 minutes',\n            label: '10 minutes'\n        },\n        {\n            value: '15 minutes',\n            label: '15 minutes'\n        },\n        {\n            value: '30 minutes',\n            label: '30 minutes'\n        }\n    ];\n    const fuseauHoraireOptions = [\n        {\n            value: 'Africa/Casablanca',\n            label: 'Africa/Casablanca'\n        },\n        {\n            value: 'Europe/Paris',\n            label: 'Europe/Paris'\n        },\n        {\n            value: 'UTC',\n            label: 'UTC'\n        }\n    ];\n    const agendaDefautOptions = [\n        {\n            value: 'agenda1',\n            label: 'Agenda par défaut'\n        },\n        {\n            value: 'agenda2',\n            label: 'Agenda 2'\n        }\n    ];\n    const servicesDesignesOptions = [\n        {\n            value: 'service1',\n            label: 'Services désignés'\n        },\n        {\n            value: 'service2',\n            label: 'Service 2'\n        }\n    ];\n    // Fonctions pour gérer les modales\n    const openMotifModal = (motif)=>{\n        if (motif) {\n            setEditingMotif(motif);\n            setMotifForm({\n                code: motif.code,\n                description: motif.description,\n                duration: motif.duration,\n                color: motif.color,\n                colorRayee: motif.colorRayee,\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        } else {\n            setEditingMotif(null);\n            setMotifForm({\n                code: '',\n                description: '',\n                duration: 15,\n                color: '#9b4d93',\n                colorRayee: '#792485',\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        }\n        setMotifModalOpened(true);\n    };\n    const openAgendaModal = (agenda)=>{\n        if (agenda) {\n            setEditingAgenda(agenda);\n            setAgendaForm({\n                name: agenda.name,\n                description: agenda.description,\n                color: agenda.color,\n                servicesDesignes: '',\n                isResource: agenda.isResource\n            });\n        } else {\n            setEditingAgenda(null);\n            setAgendaForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                servicesDesignes: '',\n                isResource: false\n            });\n        }\n        setAgendaModalOpened(true);\n    };\n    // Fonctions pour sauvegarder\n    const handleSaveMotif = ()=>{\n        if (editingMotif) {\n            // Modifier un motif existant\n            setMotifs((prev)=>prev.map((m)=>m.id === editingMotif.id ? {\n                        ...m,\n                        ...motifForm\n                    } : m));\n        } else {\n            // Ajouter un nouveau motif\n            const newMotif = {\n                id: Date.now(),\n                code: motifForm.code,\n                description: motifForm.description,\n                duration: motifForm.duration,\n                color: motifForm.color,\n                colorRayee: motifForm.colorRayee\n            };\n            setMotifs((prev)=>[\n                    ...prev,\n                    newMotif\n                ]);\n        }\n        setMotifModalOpened(false);\n    };\n    const handleSaveAgenda = ()=>{\n        if (editingAgenda) {\n            // Modifier un agenda existant\n            setAgendas((prev)=>prev.map((a)=>a.id === editingAgenda.id ? {\n                        ...a,\n                        ...agendaForm\n                    } : a));\n        } else {\n            // Ajouter un nouvel agenda\n            const newAgenda = {\n                id: Date.now(),\n                name: agendaForm.name,\n                description: agendaForm.description,\n                color: agendaForm.color,\n                isResource: agendaForm.isResource\n            };\n            setAgendas((prev)=>[\n                    ...prev,\n                    newAgenda\n                ]);\n        }\n        setAgendaModalOpened(false);\n    };\n    // Fonction pour supprimer un motif\n    const handleDeleteMotif = (id)=>{\n        setMotifs((prev)=>prev.filter((m)=>m.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        gap: \"lg\",\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                order: 2,\n                className: \"text-gray-800 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24,\n                        className: \"text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Accueil et Agenda\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                value: activeTab,\n                onChange: (value)=>setActiveTab(value || 'general'),\n                variant: \"outline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.List, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"general\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"G\\xe9n\\xe9ral\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"motifs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Motifs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"code-couleurs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 56\n                                }, void 0),\n                                children: \"Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"agendas\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"Agendas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"salles\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"evenements\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 53\n                                }, void 0),\n                                children: \"\\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"general\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                gap: \"lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Horaires du centre\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure d'ouverture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureOuverture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureOuverture: e.target.value\n                                                                        })),\n                                                                placeholder: \"08:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure de fermeture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureFermeture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureFermeture: e.target.value\n                                                                        })),\n                                                                placeholder: \"20:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                        size: \"sm\",\n                                                        fw: 500,\n                                                        mb: \"xs\",\n                                                        children: \"Jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-wrap\",\n                                                        children: joursSemaine.map((jour)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                variant: \"light\",\n                                                                color: \"blue\",\n                                                                className: \"cursor-pointer\",\n                                                                children: jour\n                                                            }, jour, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Param\\xe8tres d'affichage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Premier jour de la semaine\",\n                                                            value: generalSettings.premierJourSemaine,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        premierJourSemaine: value || 'Lundi'\n                                                                    })),\n                                                            data: joursSemaine.map((jour)=>({\n                                                                    value: jour,\n                                                                    label: jour\n                                                                }))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Vue par d\\xe9faut du calendrier\",\n                                                            value: generalSettings.vueDefautCalendrier,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        vueDefautCalendrier: value || 'Semaine'\n                                                                    })),\n                                                            data: vueCalendrierOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Minimum intervalle de temps\",\n                                                            value: generalSettings.minimumIntervalle,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        minimumIntervalle: value || '15 minutes'\n                                                                    })),\n                                                            data: intervalleOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Fuseau horaire\",\n                                                            value: generalSettings.fuseauHoraire,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        fuseauHoraire: value || 'Africa/Casablanca'\n                                                                    })),\n                                                            data: fuseauHoraireOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                                gap: \"xs\",\n                                                mt: \"md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.cacherJoursFermeture,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    cacherJoursFermeture: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Cacher les jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.curseurAgendaHeureCurrent,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    curseurAgendaHeureCurrent: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Curseur d'agenda \\xe0 l'heure current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.filtre24hCabinet,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    filtre24hCabinet: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Filtre de 24h du cabinet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserAbbreviation,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserAbbreviation: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser abr\\xe9viation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserListePatients,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserListePatients: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser la liste des patients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.afficherSeulementVisitesTerminees,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    afficherSeulementVisitesTerminees: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Afficher seulement les visites termin\\xe9es dans 'Historique journalier'\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Rappel de rendez-vous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Dur\\xe9e avant RDV\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"5\",\n                                                            data: [\n                                                                {\n                                                                    value: '5',\n                                                                    label: 'Modèle SMS'\n                                                                },\n                                                                {\n                                                                    value: '10',\n                                                                    label: '10 minutes'\n                                                                },\n                                                                {\n                                                                    value: '15',\n                                                                    label: '15 minutes'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"Mod\\xe8le Email\",\n                                                            data: [\n                                                                {\n                                                                    value: 'email1',\n                                                                    label: 'Modèle Email'\n                                                                },\n                                                                {\n                                                                    value: 'email2',\n                                                                    label: 'Email 2'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"motifs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                placeholder: \"Rechercher\",\n                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openMotifModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Ajouter nouveau motif\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Motif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Dur\\xe9e (min)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur ray\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: filteredMotifs.map((motif)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: motif.code\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.colorRayee,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.colorRayee\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 599,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openMotifModal(motif),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 610,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 605,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                onClick: ()=>handleDeleteMotif(motif.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 619,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, motif.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"code-couleurs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 635,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"agendas\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"Agendas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openAgendaModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Nouvel agenda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Ressource\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: agendas.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                        colSpan: 5,\n                                                        className: \"text-center text-gray-500 py-8\",\n                                                        children: \"Aucun agenda trouv\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 21\n                                                }, undefined) : agendas.map((agenda)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: agenda.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: agenda.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: agenda.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: agenda.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 681,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                    color: agenda.isResource ? 'green' : 'gray',\n                                                                    children: agenda.isResource ? 'Oui' : 'Non'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                        label: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"blue\",\n                                                                            onClick: ()=>openAgendaModal(agenda),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, agenda.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"salles\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"evenements\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                children: \"Contenu pour \\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.Modal, {\n                opened: motifModalOpened,\n                onClose: ()=>setMotifModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 733,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 731,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Code \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 35\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.code,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Description \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.description,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 749,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Dur\\xe9e (min) \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.duration.toString(),\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    duration: parseInt(e.target.value) || 15\n                                                })),\n                                        type: \"number\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur ray\\xe9e\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.colorRayee,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 784,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 759,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Agenda par d\\xe9faut\",\n                                        value: motifForm.agendaDefaut,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    agendaDefaut: value || ''\n                                                })),\n                                        data: agendaDefautOptions,\n                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 16,\n                                            className: \"cursor-pointer text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 31\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 797,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 796,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: motifForm.servicesDesignes,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Switch, {\n                                    checked: motifForm.couleurSombre,\n                                    onChange: (e)=>setMotifForm((prev)=>({\n                                                ...prev,\n                                                couleurSombre: e.currentTarget.checked\n                                            })),\n                                    label: \"Couleur sombre\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 816,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setMotifModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 824,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveMotif,\n                                            disabled: !motifForm.code || !motifForm.description,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 831,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 823,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 815,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 739,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 727,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_27__.Modal, {\n                opened: agendaModalOpened,\n                onClose: ()=>setAgendaModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 850,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Agenda\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 851,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 849,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Nom \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 34\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: agendaForm.name,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 859,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 869,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: agendaForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: \"Description\",\n                                        value: agendaForm.description,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: agendaForm.servicesDesignes,\n                                        onChange: (value)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 881,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Switch, {\n                                    checked: agendaForm.isResource,\n                                    onChange: (e)=>setAgendaForm((prev)=>({\n                                                ...prev,\n                                                isResource: e.currentTarget.checked\n                                            })),\n                                    label: \"Afficher comme ressource\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 900,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setAgendaModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 908,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveAgenda,\n                                            disabled: !agendaForm.name,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 899,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 857,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 845,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AccueilEtAgenda, \"adm53/mObZzMMKb1Z6+gCEOV7II=\");\n_c = AccueilEtAgenda;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AccueilEtAgenda);\nvar _c;\n$RefreshReg$(_c, \"AccueilEtAgenda\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx\n"));

/***/ })

});