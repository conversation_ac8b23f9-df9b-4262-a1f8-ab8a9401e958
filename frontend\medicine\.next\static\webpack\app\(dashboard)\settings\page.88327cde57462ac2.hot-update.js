"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHome.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSettings.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCalendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconEdit.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconX.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst AccueilEtAgenda = ()=>{\n    _s();\n    // États pour les onglets\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    // États pour les modales\n    const [motifModalOpened, setMotifModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [agendaModalOpened, setAgendaModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [salleModalOpened, setSalleModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [evenementModalOpened, setEvenementModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMotif, setEditingMotif] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingAgenda, setEditingAgenda] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSalle, setEditingSalle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingEvenement, setEditingEvenement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour les formulaires\n    const [motifForm, setMotifForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: '',\n        description: '',\n        duration: 15,\n        color: '#9b4d93',\n        colorRayee: '#792485',\n        agendaDefaut: '',\n        servicesDesignes: '',\n        couleurSombre: false\n    });\n    const [agendaForm, setAgendaForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        servicesDesignes: '',\n        isResource: false\n    });\n    const [salleForm, setSalleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        type: 'attente',\n        capacity: 1,\n        servicesDesignes: ''\n    });\n    const [evenementForm, setEvenementForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        dateDebut: '',\n        dateFin: '',\n        color: '#9b4d93',\n        indisponible: false,\n        permanent: false,\n        touteJournee: false,\n        tousLesJours: false,\n        cabinet: false\n    });\n    // États pour les paramètres généraux\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        heureOuverture: '08:00',\n        heureFermeture: '20:00',\n        premierJourSemaine: 'Lundi',\n        vueDefautCalendrier: 'Semaine',\n        minimumIntervalle: '15 minutes',\n        fuseauHoraire: 'Africa/Casablanca',\n        cacherJoursFermeture: false,\n        curseurAgendaHeureCurrent: true,\n        filtre24hCabinet: true,\n        utiliserAbbreviation: false,\n        utiliserListePatients: false,\n        afficherSeulementVisitesTerminees: false\n    });\n    // Données de test pour les motifs\n    const [motifs, setMotifs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            code: 'CNS',\n            description: 'Consultation',\n            duration: 15,\n            color: '#9b4d93',\n            colorRayee: '#792485'\n        },\n        {\n            id: 2,\n            code: 'CTR',\n            description: 'Contrôle',\n            duration: 15,\n            color: '#c05889',\n            colorRayee: '#7d1868'\n        },\n        {\n            id: 3,\n            code: 'CHI',\n            description: 'Chirurgie/Paro',\n            duration: 30,\n            color: '#c14317',\n            colorRayee: '#922a12'\n        },\n        {\n            id: 4,\n            code: 'COM',\n            description: 'Composite',\n            duration: 15,\n            color: '#2836cd',\n            colorRayee: '#091486'\n        },\n        {\n            id: 5,\n            code: 'DS',\n            description: 'Dépose Sutures',\n            duration: 15,\n            color: '#2e7d32',\n            colorRayee: '#1b5e20'\n        },\n        {\n            id: 6,\n            code: 'CS',\n            description: '1er Consultation',\n            duration: 15,\n            color: '#e91e63',\n            colorRayee: '#c2185b'\n        },\n        {\n            id: 7,\n            code: 'DEV',\n            description: 'Devis',\n            duration: 15,\n            color: 'rgb(23, 160, 131)',\n            colorRayee: '#024f3f'\n        },\n        {\n            id: 8,\n            code: 'END',\n            description: 'Endodontie',\n            duration: 60,\n            color: '#a08523',\n            colorRayee: '#564507'\n        },\n        {\n            id: 9,\n            code: 'FOR',\n            description: 'Formation',\n            duration: 60,\n            color: '#982552',\n            colorRayee: '#72183c'\n        },\n        {\n            id: 10,\n            code: 'IMP',\n            description: 'Implantologie',\n            duration: 60,\n            color: '#388983',\n            colorRayee: '#26645e'\n        },\n        {\n            id: 11,\n            code: 'ORT',\n            description: 'Orthodontie',\n            duration: 30,\n            color: '#464098',\n            colorRayee: '#1a7c81'\n        },\n        {\n            id: 12,\n            code: 'CM',\n            description: 'FS Chassis/Monta',\n            duration: 15,\n            color: '#6c407a',\n            colorRayee: '#9c4168'\n        },\n        {\n            id: 13,\n            code: 'POS',\n            description: 'PA Pose',\n            duration: 15,\n            color: '#b25953',\n            colorRayee: '#9c3e3e'\n        },\n        {\n            id: 14,\n            code: 'EMP',\n            description: 'PC TP EMP',\n            duration: 30,\n            color: '#4c8d57',\n            colorRayee: '#366e42'\n        },\n        {\n            id: 15,\n            code: 'ARM',\n            description: 'PC ESS Armature',\n            duration: 30,\n            color: '#3291ef',\n            colorRayee: '#0d667b'\n        },\n        {\n            id: 16,\n            code: 'SCE',\n            description: 'PC Scellement',\n            duration: 15,\n            color: '#c2185b',\n            colorRayee: '#880e4f'\n        },\n        {\n            id: 17,\n            code: 'PRO',\n            description: 'Prophylaxie',\n            duration: 15,\n            color: '#009688',\n            colorRayee: '#00695c'\n        }\n    ]);\n    // Données de test pour les agendas\n    const [agendas, setAgendas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'Domicile',\n            description: '',\n            color: '#f4511e',\n            isResource: false\n        },\n        {\n            id: 2,\n            name: 'Clinique',\n            description: '',\n            color: '#43a047',\n            isResource: false\n        },\n        {\n            id: 3,\n            name: 'Cabinet',\n            description: '',\n            color: '#0097a7',\n            isResource: false\n        }\n    ]);\n    // Données de test pour les docteurs (Code couleurs médecine)\n    const [docteurs, setDocteurs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'Docteur',\n            color: '#d50000'\n        }\n    ]);\n    // Données de test pour les salles\n    const [salles, setSalles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'SLT',\n            description: 'Salle d\\'attente',\n            color: '#039be5',\n            type: 'attente',\n            capacity: 30\n        },\n        {\n            id: 2,\n            name: 'FTL',\n            description: 'Fauteuil 1',\n            color: '#ff5722',\n            type: 'consultation',\n            capacity: 1\n        },\n        {\n            id: 3,\n            name: 'FT 1',\n            description: '',\n            color: '#000000',\n            type: 'consultation',\n            capacity: 1\n        },\n        {\n            id: 4,\n            name: 'SALLE DE CONSULTATION 2',\n            description: '',\n            color: '#000000',\n            type: 'consultation',\n            capacity: 1\n        }\n    ]);\n    // Données de test pour les événements\n    const [evenements, setEvenements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: 'CONGRES',\n            medecins: 'DEMO DEMO',\n            dateDebut: '21/04/2025',\n            dateFin: '23/04/2025',\n            color: '#f52dbe',\n            indisponible: true,\n            permanent: false,\n            touteJournee: true,\n            tousLesJours: false,\n            cabinet: false\n        }\n    ]);\n    // États pour la recherche\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Filtrer les motifs selon la recherche\n    const filteredMotifs = motifs.filter((motif)=>motif.code.toLowerCase().includes(searchTerm.toLowerCase()) || motif.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    // Jours de la semaine\n    const joursSemaine = [\n        'Lundi',\n        'Mardi',\n        'Mercredi',\n        'Jeudi',\n        'Vendredi',\n        'Samedi',\n        'Dimanche'\n    ];\n    // Options pour les selects\n    const vueCalendrierOptions = [\n        {\n            value: 'Semaine',\n            label: 'Semaine'\n        },\n        {\n            value: 'Mois',\n            label: 'Mois'\n        },\n        {\n            value: 'Jour',\n            label: 'Jour'\n        }\n    ];\n    const intervalleOptions = [\n        {\n            value: '5 minutes',\n            label: '5 minutes'\n        },\n        {\n            value: '10 minutes',\n            label: '10 minutes'\n        },\n        {\n            value: '15 minutes',\n            label: '15 minutes'\n        },\n        {\n            value: '30 minutes',\n            label: '30 minutes'\n        }\n    ];\n    const fuseauHoraireOptions = [\n        {\n            value: 'Africa/Casablanca',\n            label: 'Africa/Casablanca'\n        },\n        {\n            value: 'Europe/Paris',\n            label: 'Europe/Paris'\n        },\n        {\n            value: 'UTC',\n            label: 'UTC'\n        }\n    ];\n    const agendaDefautOptions = [\n        {\n            value: 'agenda1',\n            label: 'Agenda par défaut'\n        },\n        {\n            value: 'agenda2',\n            label: 'Agenda 2'\n        }\n    ];\n    const servicesDesignesOptions = [\n        {\n            value: 'service1',\n            label: 'Services désignés'\n        },\n        {\n            value: 'service2',\n            label: 'Service 2'\n        }\n    ];\n    // Fonctions pour gérer les modales\n    const openMotifModal = (motif)=>{\n        if (motif) {\n            setEditingMotif(motif);\n            setMotifForm({\n                code: motif.code,\n                description: motif.description,\n                duration: motif.duration,\n                color: motif.color,\n                colorRayee: motif.colorRayee,\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        } else {\n            setEditingMotif(null);\n            setMotifForm({\n                code: '',\n                description: '',\n                duration: 15,\n                color: '#9b4d93',\n                colorRayee: '#792485',\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        }\n        setMotifModalOpened(true);\n    };\n    const openAgendaModal = (agenda)=>{\n        if (agenda) {\n            setEditingAgenda(agenda);\n            setAgendaForm({\n                name: agenda.name,\n                description: agenda.description,\n                color: agenda.color,\n                servicesDesignes: '',\n                isResource: agenda.isResource\n            });\n        } else {\n            setEditingAgenda(null);\n            setAgendaForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                servicesDesignes: '',\n                isResource: false\n            });\n        }\n        setAgendaModalOpened(true);\n    };\n    const openSalleModal = (salle)=>{\n        if (salle) {\n            setEditingSalle(salle);\n            setSalleForm({\n                name: salle.name,\n                description: salle.description,\n                color: salle.color,\n                type: salle.type,\n                capacity: salle.capacity,\n                servicesDesignes: ''\n            });\n        } else {\n            setEditingSalle(null);\n            setSalleForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                type: 'attente',\n                capacity: 1,\n                servicesDesignes: ''\n            });\n        }\n        setSalleModalOpened(true);\n    };\n    const openEvenementModal = (evenement)=>{\n        if (evenement) {\n            setEditingEvenement(evenement);\n            setEvenementForm({\n                title: evenement.title,\n                dateDebut: evenement.dateDebut,\n                dateFin: evenement.dateFin,\n                color: evenement.color,\n                indisponible: evenement.indisponible,\n                permanent: evenement.permanent,\n                touteJournee: evenement.touteJournee,\n                tousLesJours: evenement.tousLesJours,\n                cabinet: evenement.cabinet\n            });\n        } else {\n            setEditingEvenement(null);\n            setEvenementForm({\n                title: '',\n                dateDebut: '',\n                dateFin: '',\n                color: '#9b4d93',\n                indisponible: false,\n                permanent: false,\n                touteJournee: false,\n                tousLesJours: false,\n                cabinet: false\n            });\n        }\n        setEvenementModalOpened(true);\n    };\n    // Fonctions pour sauvegarder\n    const handleSaveMotif = ()=>{\n        if (editingMotif) {\n            // Modifier un motif existant\n            setMotifs((prev)=>prev.map((m)=>m.id === editingMotif.id ? {\n                        ...m,\n                        ...motifForm\n                    } : m));\n        } else {\n            // Ajouter un nouveau motif\n            const newMotif = {\n                id: Date.now(),\n                code: motifForm.code,\n                description: motifForm.description,\n                duration: motifForm.duration,\n                color: motifForm.color,\n                colorRayee: motifForm.colorRayee\n            };\n            setMotifs((prev)=>[\n                    ...prev,\n                    newMotif\n                ]);\n        }\n        setMotifModalOpened(false);\n    };\n    const handleSaveAgenda = ()=>{\n        if (editingAgenda) {\n            // Modifier un agenda existant\n            setAgendas((prev)=>prev.map((a)=>a.id === editingAgenda.id ? {\n                        ...a,\n                        ...agendaForm\n                    } : a));\n        } else {\n            // Ajouter un nouvel agenda\n            const newAgenda = {\n                id: Date.now(),\n                name: agendaForm.name,\n                description: agendaForm.description,\n                color: agendaForm.color,\n                isResource: agendaForm.isResource\n            };\n            setAgendas((prev)=>[\n                    ...prev,\n                    newAgenda\n                ]);\n        }\n        setAgendaModalOpened(false);\n    };\n    // Fonction pour supprimer un motif\n    const handleDeleteMotif = (id)=>{\n        setMotifs((prev)=>prev.filter((m)=>m.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        gap: \"lg\",\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                order: 2,\n                className: \"text-gray-800 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24,\n                        className: \"text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Accueil et Agenda\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                value: activeTab,\n                onChange: (value)=>setActiveTab(value || 'general'),\n                variant: \"outline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.List, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"general\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"G\\xe9n\\xe9ral\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"motifs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Motifs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"code-couleurs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 56\n                                }, void 0),\n                                children: \"Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"agendas\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"Agendas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"salles\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"evenements\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 53\n                                }, void 0),\n                                children: \"\\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"general\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                gap: \"lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Horaires du centre\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure d'ouverture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureOuverture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureOuverture: e.target.value\n                                                                        })),\n                                                                placeholder: \"08:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure de fermeture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                                value: generalSettings.heureFermeture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureFermeture: e.target.value\n                                                                        })),\n                                                                placeholder: \"20:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                        size: \"sm\",\n                                                        fw: 500,\n                                                        mb: \"xs\",\n                                                        children: \"Jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-wrap\",\n                                                        children: joursSemaine.map((jour)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                variant: \"light\",\n                                                                color: \"blue\",\n                                                                className: \"cursor-pointer\",\n                                                                children: jour\n                                                            }, jour, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Param\\xe8tres d'affichage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Premier jour de la semaine\",\n                                                            value: generalSettings.premierJourSemaine,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        premierJourSemaine: value || 'Lundi'\n                                                                    })),\n                                                            data: joursSemaine.map((jour)=>({\n                                                                    value: jour,\n                                                                    label: jour\n                                                                }))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Vue par d\\xe9faut du calendrier\",\n                                                            value: generalSettings.vueDefautCalendrier,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        vueDefautCalendrier: value || 'Semaine'\n                                                                    })),\n                                                            data: vueCalendrierOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Minimum intervalle de temps\",\n                                                            value: generalSettings.minimumIntervalle,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        minimumIntervalle: value || '15 minutes'\n                                                                    })),\n                                                            data: intervalleOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            label: \"Fuseau horaire\",\n                                                            value: generalSettings.fuseauHoraire,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        fuseauHoraire: value || 'Africa/Casablanca'\n                                                                    })),\n                                                            data: fuseauHoraireOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                                gap: \"xs\",\n                                                mt: \"md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.cacherJoursFermeture,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    cacherJoursFermeture: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Cacher les jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.curseurAgendaHeureCurrent,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    curseurAgendaHeureCurrent: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Curseur d'agenda \\xe0 l'heure current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.filtre24hCabinet,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    filtre24hCabinet: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Filtre de 24h du cabinet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserAbbreviation,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserAbbreviation: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser abr\\xe9viation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.utiliserListePatients,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserListePatients: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser la liste des patients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Checkbox, {\n                                                        checked: generalSettings.afficherSeulementVisitesTerminees,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    afficherSeulementVisitesTerminees: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Afficher seulement les visites termin\\xe9es dans 'Historique journalier'\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Rappel de rendez-vous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Dur\\xe9e avant RDV\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"5\",\n                                                            data: [\n                                                                {\n                                                                    value: '5',\n                                                                    label: 'Modèle SMS'\n                                                                },\n                                                                {\n                                                                    value: '10',\n                                                                    label: '10 minutes'\n                                                                },\n                                                                {\n                                                                    value: '15',\n                                                                    label: '15 minutes'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                            placeholder: \"Mod\\xe8le Email\",\n                                                            data: [\n                                                                {\n                                                                    value: 'email1',\n                                                                    label: 'Modèle Email'\n                                                                },\n                                                                {\n                                                                    value: 'email2',\n                                                                    label: 'Email 2'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"motifs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                                placeholder: \"Rechercher\",\n                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openMotifModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Ajouter nouveau motif\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Motif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Dur\\xe9e (min)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur ray\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: filteredMotifs.map((motif)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: motif.code\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 680,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: motif.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: motif.colorRayee,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.colorRayee\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openMotifModal(motif),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 696,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                onClick: ()=>handleDeleteMotif(motif.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 711,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 706,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 705,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, motif.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"code-couleurs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                                    placeholder: \"Docteur\",\n                                                    data: docteurs.map((d)=>({\n                                                            value: d.id.toString(),\n                                                            label: d.name\n                                                        })),\n                                                    style: {\n                                                        width: 200\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                            size: \"sm\",\n                                                            fw: 500,\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                            color: \"#d50000\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Docteur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 752,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: docteurs.map((docteur)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: docteur.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: docteur.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 764,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: docteur.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 765,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 762,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                        label: \"Supprimer\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"red\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 775,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 771,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, docteur.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"agendas\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"Agendas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openAgendaModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Nouvel agenda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 806,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Ressource\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: agendas.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                        colSpan: 5,\n                                                        className: \"text-center text-gray-500 py-8\",\n                                                        children: \"Aucun agenda trouv\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 21\n                                                }, undefined) : agendas.map((agenda)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: agenda.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: agenda.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: agenda.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 829,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: agenda.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 830,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                    color: agenda.isResource ? 'green' : 'gray',\n                                                                    children: agenda.isResource ? 'Oui' : 'Non'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 833,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                        label: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"blue\",\n                                                                            onClick: ()=>openAgendaModal(agenda),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 841,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 840,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, agenda.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 802,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 789,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"salles\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"Salles\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 864,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openSalleModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Ajouter salle\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 880,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 881,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Capacit\\xe9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: salles.map((salle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: salle.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: salle.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 892,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                            color: salle.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 895,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: salle.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 896,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                                    color: salle.type === 'attente' ? 'blue' : 'pink',\n                                                                    variant: \"light\",\n                                                                    children: salle.type === 'attente' ? 'Salle d\\'attente' : 'Salle de consultation'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 900,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    children: salle.capacity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 908,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openSalleModal(salle),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 918,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 913,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 912,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 926,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 922,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 921,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 911,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, salle.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 886,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 874,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 862,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 861,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"evenements\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"\\xc9v\\xe9nements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openEvenementModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Nouvel \\xe9v\\xe9nement\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 942,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Titre\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 957,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"M\\xe9decins\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Disponibilit\\xe9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 959,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"D\\xe9but\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 960,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"Fin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"de\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            children: \"\\xe0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 955,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tbody, {\n                                                children: evenements.map((evenement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                                    fw: 500,\n                                                                    children: evenement.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 971,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 970,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: evenement.medecins\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 973,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                                        color: evenement.indisponible ? '#f52dbe' : '#4caf50',\n                                                                        size: 20\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 976,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: evenement.dateDebut\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: evenement.dateFin\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: \"de\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: \"\\xe0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openEvenementModal(evenement),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 994,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 989,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 988,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 1002,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 998,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 997,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 986,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, evenement.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 969,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 967,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 954,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 941,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 940,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Modal, {\n                opened: motifModalOpened,\n                onClose: ()=>setMotifModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1022,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1023,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1021,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Code \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1033,\n                                                    columnNumber: 35\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.code,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Description \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1041,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.description,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1040,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1039,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1030,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Dur\\xe9e (min) \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1052,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1052,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.duration.toString(),\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    duration: parseInt(e.target.value) || 15\n                                                })),\n                                        type: \"number\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1051,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1050,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1060,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1059,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur ray\\xe9e\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1073,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: motifForm.colorRayee,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1075,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1074,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1072,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1049,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Agenda par d\\xe9faut\",\n                                        value: motifForm.agendaDefaut,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    agendaDefaut: value || ''\n                                                })),\n                                        data: agendaDefautOptions,\n                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 16,\n                                            className: \"cursor-pointer text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 31\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1087,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1086,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: motifForm.servicesDesignes,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1096,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1095,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1085,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_29__.Switch, {\n                                    checked: motifForm.couleurSombre,\n                                    onChange: (e)=>setMotifForm((prev)=>({\n                                                ...prev,\n                                                couleurSombre: e.currentTarget.checked\n                                            })),\n                                    label: \"Couleur sombre\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1106,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setMotifModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1114,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveMotif,\n                                            disabled: !motifForm.code || !motifForm.description,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1121,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1113,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1105,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1029,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 1017,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_28__.Modal, {\n                opened: agendaModalOpened,\n                onClose: ()=>setAgendaModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1140,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                            fw: 600,\n                            children: \"Agenda\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1141,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1139,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Nom \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1151,\n                                                    columnNumber: 34\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1151,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: agendaForm.name,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1159,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorSwatch, {\n                                                    color: agendaForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1161,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1160,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1158,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1157,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.TextInput, {\n                                        label: \"Description\",\n                                        value: agendaForm.description,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1173,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1172,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: agendaForm.servicesDesignes,\n                                        onChange: (value)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1179,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1171,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_29__.Switch, {\n                                    checked: agendaForm.isResource,\n                                    onChange: (e)=>setAgendaForm((prev)=>({\n                                                ...prev,\n                                                isResource: e.currentTarget.checked\n                                            })),\n                                    label: \"Afficher comme ressource\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setAgendaModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveAgenda,\n                                            disabled: !agendaForm.name,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1197,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1189,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1147,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 1135,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AccueilEtAgenda, \"9TmtZyRHMHLof8M+DHEVpq/AogA=\");\n_c = AccueilEtAgenda;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AccueilEtAgenda);\nvar _c;\n$RefreshReg$(_c, \"AccueilEtAgenda\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx\n"));

/***/ })

});