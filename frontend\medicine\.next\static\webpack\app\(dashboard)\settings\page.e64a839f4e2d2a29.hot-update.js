"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx":
/*!*****************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx ***!
  \*****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Paper/Paper.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Badge/Badge.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ScrollArea/ScrollArea.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Table/Table.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorSwatch/ColorSwatch.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tooltip/Tooltip.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHome.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSettings.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconCalendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconBuilding.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconEdit.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconTrash.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=IconBuilding,IconCalendar,IconColorPicker,IconEdit,IconHome,IconPlus,IconSearch,IconSettings,IconTrash,IconX!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconX.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst AccueilEtAgenda = ()=>{\n    _s();\n    // États pour les onglets\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    // États pour les modales\n    const [motifModalOpened, setMotifModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [agendaModalOpened, setAgendaModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [salleModalOpened, setSalleModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [evenementModalOpened, setEvenementModalOpened] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMotif, setEditingMotif] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingAgenda, setEditingAgenda] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSalle, setEditingSalle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingEvenement, setEditingEvenement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // États pour les formulaires\n    const [motifForm, setMotifForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: '',\n        description: '',\n        duration: 15,\n        color: '#9b4d93',\n        colorRayee: '#792485',\n        agendaDefaut: '',\n        servicesDesignes: '',\n        couleurSombre: false\n    });\n    const [agendaForm, setAgendaForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        servicesDesignes: '',\n        isResource: false\n    });\n    const [salleForm, setSalleForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#9b4d93',\n        type: 'attente',\n        capacity: 1,\n        servicesDesignes: ''\n    });\n    const [evenementForm, setEvenementForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        dateDebut: '',\n        dateFin: '',\n        color: '#9b4d93',\n        indisponible: false,\n        permanent: false,\n        touteJournee: false,\n        tousLesJours: false,\n        cabinet: false\n    });\n    // États pour les paramètres généraux\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        heureOuverture: '08:00',\n        heureFermeture: '20:00',\n        premierJourSemaine: 'Lundi',\n        vueDefautCalendrier: 'Semaine',\n        minimumIntervalle: '15 minutes',\n        fuseauHoraire: 'Africa/Casablanca',\n        cacherJoursFermeture: false,\n        curseurAgendaHeureCurrent: true,\n        filtre24hCabinet: true,\n        utiliserAbbreviation: false,\n        utiliserListePatients: false,\n        afficherSeulementVisitesTerminees: false\n    });\n    // Données de test pour les motifs\n    const [motifs, setMotifs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            code: 'CNS',\n            description: 'Consultation',\n            duration: 15,\n            color: '#9b4d93',\n            colorRayee: '#792485'\n        },\n        {\n            id: 2,\n            code: 'CTR',\n            description: 'Contrôle',\n            duration: 15,\n            color: '#c05889',\n            colorRayee: '#7d1868'\n        },\n        {\n            id: 3,\n            code: 'CHI',\n            description: 'Chirurgie/Paro',\n            duration: 30,\n            color: '#c14317',\n            colorRayee: '#922a12'\n        },\n        {\n            id: 4,\n            code: 'COM',\n            description: 'Composite',\n            duration: 15,\n            color: '#2836cd',\n            colorRayee: '#091486'\n        },\n        {\n            id: 5,\n            code: 'DS',\n            description: 'Dépose Sutures',\n            duration: 15,\n            color: '#2e7d32',\n            colorRayee: '#1b5e20'\n        },\n        {\n            id: 6,\n            code: 'CS',\n            description: '1er Consultation',\n            duration: 15,\n            color: '#e91e63',\n            colorRayee: '#c2185b'\n        },\n        {\n            id: 7,\n            code: 'DEV',\n            description: 'Devis',\n            duration: 15,\n            color: 'rgb(23, 160, 131)',\n            colorRayee: '#024f3f'\n        },\n        {\n            id: 8,\n            code: 'END',\n            description: 'Endodontie',\n            duration: 60,\n            color: '#a08523',\n            colorRayee: '#564507'\n        },\n        {\n            id: 9,\n            code: 'FOR',\n            description: 'Formation',\n            duration: 60,\n            color: '#982552',\n            colorRayee: '#72183c'\n        },\n        {\n            id: 10,\n            code: 'IMP',\n            description: 'Implantologie',\n            duration: 60,\n            color: '#388983',\n            colorRayee: '#26645e'\n        },\n        {\n            id: 11,\n            code: 'ORT',\n            description: 'Orthodontie',\n            duration: 30,\n            color: '#464098',\n            colorRayee: '#1a7c81'\n        },\n        {\n            id: 12,\n            code: 'CM',\n            description: 'FS Chassis/Monta',\n            duration: 15,\n            color: '#6c407a',\n            colorRayee: '#9c4168'\n        },\n        {\n            id: 13,\n            code: 'POS',\n            description: 'PA Pose',\n            duration: 15,\n            color: '#b25953',\n            colorRayee: '#9c3e3e'\n        },\n        {\n            id: 14,\n            code: 'EMP',\n            description: 'PC TP EMP',\n            duration: 30,\n            color: '#4c8d57',\n            colorRayee: '#366e42'\n        },\n        {\n            id: 15,\n            code: 'ARM',\n            description: 'PC ESS Armature',\n            duration: 30,\n            color: '#3291ef',\n            colorRayee: '#0d667b'\n        },\n        {\n            id: 16,\n            code: 'SCE',\n            description: 'PC Scellement',\n            duration: 15,\n            color: '#c2185b',\n            colorRayee: '#880e4f'\n        },\n        {\n            id: 17,\n            code: 'PRO',\n            description: 'Prophylaxie',\n            duration: 15,\n            color: '#009688',\n            colorRayee: '#00695c'\n        }\n    ]);\n    // Données de test pour les agendas\n    const [agendas, setAgendas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'Domicile',\n            description: '',\n            color: '#f4511e',\n            isResource: false\n        },\n        {\n            id: 2,\n            name: 'Clinique',\n            description: '',\n            color: '#43a047',\n            isResource: false\n        },\n        {\n            id: 3,\n            name: 'Cabinet',\n            description: '',\n            color: '#0097a7',\n            isResource: false\n        }\n    ]);\n    // Données de test pour les docteurs (Code couleurs médecine)\n    const [docteurs, setDocteurs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'Docteur',\n            color: '#d50000'\n        }\n    ]);\n    // Données de test pour les salles\n    const [salles, setSalles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: 'SLT',\n            description: 'Salle d\\'attente',\n            color: '#039be5',\n            type: 'attente',\n            capacity: 30\n        },\n        {\n            id: 2,\n            name: 'FTL',\n            description: 'Fauteuil 1',\n            color: '#ff5722',\n            type: 'consultation',\n            capacity: 1\n        },\n        {\n            id: 3,\n            name: 'FT 1',\n            description: '',\n            color: '#000000',\n            type: 'consultation',\n            capacity: 1\n        },\n        {\n            id: 4,\n            name: 'SALLE DE CONSULTATION 2',\n            description: '',\n            color: '#000000',\n            type: 'consultation',\n            capacity: 1\n        }\n    ]);\n    // Données de test pour les événements\n    const [evenements, setEvenements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: 'CONGRES',\n            medecins: 'DEMO DEMO',\n            dateDebut: '21/04/2025',\n            dateFin: '23/04/2025',\n            color: '#f52dbe',\n            indisponible: true,\n            permanent: false,\n            touteJournee: true,\n            tousLesJours: false,\n            cabinet: false\n        }\n    ]);\n    // États pour la recherche\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Filtrer les motifs selon la recherche\n    const filteredMotifs = motifs.filter((motif)=>motif.code.toLowerCase().includes(searchTerm.toLowerCase()) || motif.description.toLowerCase().includes(searchTerm.toLowerCase()));\n    // Jours de la semaine\n    const joursSemaine = [\n        'Lundi',\n        'Mardi',\n        'Mercredi',\n        'Jeudi',\n        'Vendredi',\n        'Samedi',\n        'Dimanche'\n    ];\n    // Options pour les selects\n    const vueCalendrierOptions = [\n        {\n            value: 'Semaine',\n            label: 'Semaine'\n        },\n        {\n            value: 'Mois',\n            label: 'Mois'\n        },\n        {\n            value: 'Jour',\n            label: 'Jour'\n        }\n    ];\n    const intervalleOptions = [\n        {\n            value: '5 minutes',\n            label: '5 minutes'\n        },\n        {\n            value: '10 minutes',\n            label: '10 minutes'\n        },\n        {\n            value: '15 minutes',\n            label: '15 minutes'\n        },\n        {\n            value: '30 minutes',\n            label: '30 minutes'\n        }\n    ];\n    const fuseauHoraireOptions = [\n        {\n            value: 'Africa/Casablanca',\n            label: 'Africa/Casablanca'\n        },\n        {\n            value: 'Europe/Paris',\n            label: 'Europe/Paris'\n        },\n        {\n            value: 'UTC',\n            label: 'UTC'\n        }\n    ];\n    const agendaDefautOptions = [\n        {\n            value: 'agenda1',\n            label: 'Agenda par défaut'\n        },\n        {\n            value: 'agenda2',\n            label: 'Agenda 2'\n        }\n    ];\n    const servicesDesignesOptions = [\n        {\n            value: 'service1',\n            label: 'Services désignés'\n        },\n        {\n            value: 'service2',\n            label: 'Service 2'\n        }\n    ];\n    // Fonctions pour gérer les modales\n    const openMotifModal = (motif)=>{\n        if (motif) {\n            setEditingMotif(motif);\n            setMotifForm({\n                code: motif.code,\n                description: motif.description,\n                duration: motif.duration,\n                color: motif.color,\n                colorRayee: motif.colorRayee,\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        } else {\n            setEditingMotif(null);\n            setMotifForm({\n                code: '',\n                description: '',\n                duration: 15,\n                color: '#9b4d93',\n                colorRayee: '#792485',\n                agendaDefaut: '',\n                servicesDesignes: '',\n                couleurSombre: false\n            });\n        }\n        setMotifModalOpened(true);\n    };\n    const openAgendaModal = (agenda)=>{\n        if (agenda) {\n            setEditingAgenda(agenda);\n            setAgendaForm({\n                name: agenda.name,\n                description: agenda.description,\n                color: agenda.color,\n                servicesDesignes: '',\n                isResource: agenda.isResource\n            });\n        } else {\n            setEditingAgenda(null);\n            setAgendaForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                servicesDesignes: '',\n                isResource: false\n            });\n        }\n        setAgendaModalOpened(true);\n    };\n    const openSalleModal = (salle)=>{\n        if (salle) {\n            setEditingSalle(salle);\n            setSalleForm({\n                name: salle.name,\n                description: salle.description,\n                color: salle.color,\n                type: salle.type,\n                capacity: salle.capacity,\n                servicesDesignes: ''\n            });\n        } else {\n            setEditingSalle(null);\n            setSalleForm({\n                name: '',\n                description: '',\n                color: '#9b4d93',\n                type: 'attente',\n                capacity: 1,\n                servicesDesignes: ''\n            });\n        }\n        setSalleModalOpened(true);\n    };\n    const openEvenementModal = (evenement)=>{\n        if (evenement) {\n            setEditingEvenement(evenement);\n            setEvenementForm({\n                title: evenement.title,\n                dateDebut: evenement.dateDebut,\n                dateFin: evenement.dateFin,\n                color: evenement.color,\n                indisponible: evenement.indisponible,\n                permanent: evenement.permanent,\n                touteJournee: evenement.touteJournee,\n                tousLesJours: evenement.tousLesJours,\n                cabinet: evenement.cabinet\n            });\n        } else {\n            setEditingEvenement(null);\n            setEvenementForm({\n                title: '',\n                dateDebut: '',\n                dateFin: '',\n                color: '#9b4d93',\n                indisponible: false,\n                permanent: false,\n                touteJournee: false,\n                tousLesJours: false,\n                cabinet: false\n            });\n        }\n        setEvenementModalOpened(true);\n    };\n    // Fonctions pour sauvegarder\n    const handleSaveMotif = ()=>{\n        if (editingMotif) {\n            // Modifier un motif existant\n            setMotifs((prev)=>prev.map((m)=>m.id === editingMotif.id ? {\n                        ...m,\n                        ...motifForm\n                    } : m));\n        } else {\n            // Ajouter un nouveau motif\n            const newMotif = {\n                id: Date.now(),\n                code: motifForm.code,\n                description: motifForm.description,\n                duration: motifForm.duration,\n                color: motifForm.color,\n                colorRayee: motifForm.colorRayee\n            };\n            setMotifs((prev)=>[\n                    ...prev,\n                    newMotif\n                ]);\n        }\n        setMotifModalOpened(false);\n    };\n    const handleSaveAgenda = ()=>{\n        if (editingAgenda) {\n            // Modifier un agenda existant\n            setAgendas((prev)=>prev.map((a)=>a.id === editingAgenda.id ? {\n                        ...a,\n                        ...agendaForm\n                    } : a));\n        } else {\n            // Ajouter un nouvel agenda\n            const newAgenda = {\n                id: Date.now(),\n                name: agendaForm.name,\n                description: agendaForm.description,\n                color: agendaForm.color,\n                isResource: agendaForm.isResource\n            };\n            setAgendas((prev)=>[\n                    ...prev,\n                    newAgenda\n                ]);\n        }\n        setAgendaModalOpened(false);\n    };\n    // Fonction pour supprimer un motif\n    const handleDeleteMotif = (id)=>{\n        setMotifs((prev)=>prev.filter((m)=>m.id !== id));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n        gap: \"lg\",\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                order: 2,\n                className: \"text-gray-800 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 24,\n                        className: \"text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Accueil et Agenda\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 417,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                value: activeTab,\n                onChange: (value)=>setActiveTab(value || 'general'),\n                variant: \"outline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.List, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"general\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"G\\xe9n\\xe9ral\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"motifs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Motifs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"code-couleurs\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 56\n                                }, void 0),\n                                children: \"Code couleurs m\\xe9decin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"agendas\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 50\n                                }, void 0),\n                                children: \"Agendas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"salles\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 49\n                                }, void 0),\n                                children: \"Salles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Tab, {\n                                value: \"evenements\",\n                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 53\n                                }, void 0),\n                                children: \"\\xc9v\\xe9nements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"general\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                gap: \"lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Horaires du centre\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure d'ouverture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                                                value: generalSettings.heureOuverture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureOuverture: e.target.value\n                                                                        })),\n                                                                placeholder: \"08:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                                        span: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                size: \"sm\",\n                                                                fw: 500,\n                                                                mb: \"xs\",\n                                                                children: \"Heure de fermeture(s)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                                                value: generalSettings.heureFermeture,\n                                                                onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                            ...prev,\n                                                                            heureFermeture: e.target.value\n                                                                        })),\n                                                                placeholder: \"20:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                        size: \"sm\",\n                                                        fw: 500,\n                                                        mb: \"xs\",\n                                                        children: \"Jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-wrap\",\n                                                        children: joursSemaine.map((jour)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                                                variant: \"light\",\n                                                                color: \"blue\",\n                                                                className: \"cursor-pointer\",\n                                                                children: jour\n                                                            }, jour, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Param\\xe8tres d'affichage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                            label: \"Premier jour de la semaine\",\n                                                            value: generalSettings.premierJourSemaine,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        premierJourSemaine: value || 'Lundi'\n                                                                    })),\n                                                            data: joursSemaine.map((jour)=>({\n                                                                    value: jour,\n                                                                    label: jour\n                                                                }))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                            label: \"Vue par d\\xe9faut du calendrier\",\n                                                            value: generalSettings.vueDefautCalendrier,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        vueDefautCalendrier: value || 'Semaine'\n                                                                    })),\n                                                            data: vueCalendrierOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                            label: \"Minimum intervalle de temps\",\n                                                            value: generalSettings.minimumIntervalle,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        minimumIntervalle: value || '15 minutes'\n                                                                    })),\n                                                            data: intervalleOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                            label: \"Fuseau horaire\",\n                                                            value: generalSettings.fuseauHoraire,\n                                                            onChange: (value)=>setGeneralSettings((prev)=>({\n                                                                        ...prev,\n                                                                        fuseauHoraire: value || 'Africa/Casablanca'\n                                                                    })),\n                                                            data: fuseauHoraireOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                                                gap: \"xs\",\n                                                mt: \"md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                                                        checked: generalSettings.cacherJoursFermeture,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    cacherJoursFermeture: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Cacher les jours de fermeture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                                                        checked: generalSettings.curseurAgendaHeureCurrent,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    curseurAgendaHeureCurrent: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Curseur d'agenda \\xe0 l'heure current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                                                        checked: generalSettings.filtre24hCabinet,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    filtre24hCabinet: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Filtre de 24h du cabinet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                                                        checked: generalSettings.utiliserAbbreviation,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserAbbreviation: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser abr\\xe9viation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                                                        checked: generalSettings.utiliserListePatients,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    utiliserListePatients: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Utiliser la liste des patients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                                                        checked: generalSettings.afficherSeulementVisitesTerminees,\n                                                        onChange: (e)=>setGeneralSettings((prev)=>({\n                                                                    ...prev,\n                                                                    afficherSeulementVisitesTerminees: e.currentTarget.checked\n                                                                })),\n                                                        label: \"Afficher seulement les visites termin\\xe9es dans 'Historique journalier'\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                                order: 4,\n                                                className: \"text-gray-700 mb-md\",\n                                                children: \"Rappel de rendez-vous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Dur\\xe9e avant RDV\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                            placeholder: \"5\",\n                                                            data: [\n                                                                {\n                                                                    value: '5',\n                                                                    label: 'Modèle SMS'\n                                                                },\n                                                                {\n                                                                    value: '10',\n                                                                    label: '10 minutes'\n                                                                },\n                                                                {\n                                                                    value: '15',\n                                                                    label: '15 minutes'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                                        span: 6,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                            placeholder: \"Mod\\xe8le Email\",\n                                                            data: [\n                                                                {\n                                                                    value: 'email1',\n                                                                    label: 'Modèle Email'\n                                                                },\n                                                                {\n                                                                    value: 'email2',\n                                                                    label: 'Email 2'\n                                                                }\n                                                            ]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"motifs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                                placeholder: \"Rechercher\",\n                                                leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 32\n                                                }, void 0),\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openMotifModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Ajouter nouveau motif\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Motif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Dur\\xe9e (min)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Couleur ray\\xe9e\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tbody, {\n                                                children: filteredMotifs.map((motif)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                    fw: 500,\n                                                                    children: motif.code\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: motif.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: motif.duration\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                                            color: motif.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 686,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                                            color: motif.colorRayee,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: motif.colorRayee\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openMotifModal(motif),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 703,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 698,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                onClick: ()=>handleDeleteMotif(motif.id),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 712,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 707,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 706,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, motif.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 643,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"code-couleurs\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                                    placeholder: \"Docteur\",\n                                                    data: docteurs.map((d)=>({\n                                                            value: d.id.toString(),\n                                                            label: d.name\n                                                        })),\n                                                    style: {\n                                                        width: 200\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                            size: \"sm\",\n                                                            fw: 500,\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                            color: \"#d50000\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Docteur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 752,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tbody, {\n                                                children: docteurs.map((docteur)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                    fw: 500,\n                                                                    children: docteur.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                                            color: docteur.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 765,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: docteur.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 766,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 764,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                                        label: \"Supprimer\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"red\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 776,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 771,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, docteur.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 748,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"agendas\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"Agendas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openAgendaModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Nouvel agenda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 792,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Ressource\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tbody, {\n                                                children: agendas.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                        colSpan: 5,\n                                                        className: \"text-center text-gray-500 py-8\",\n                                                        children: \"Aucun agenda trouv\\xe9\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 21\n                                                }, undefined) : agendas.map((agenda)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                    fw: 500,\n                                                                    children: agenda.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: agenda.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                                            color: agenda.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 830,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: agenda.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                                                    color: agenda.isResource ? 'green' : 'gray',\n                                                                    children: agenda.isResource ? 'Oui' : 'Non'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                                        label: \"Modifier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.ActionIcon, {\n                                                                            variant: \"subtle\",\n                                                                            color: \"blue\",\n                                                                            onClick: ()=>openAgendaModal(agenda),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 847,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 842,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 841,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, agenda.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 814,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 790,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"salles\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"Salles\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openSalleModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Ajouter salle\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Nom\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 880,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 881,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Capacit\\xe9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 884,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 878,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tbody, {\n                                                children: salles.map((salle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                    fw: 500,\n                                                                    children: salle.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 891,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: salle.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                                            color: salle.color,\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 896,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                            size: \"sm\",\n                                                                            children: salle.color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 897,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Badge, {\n                                                                    color: salle.type === 'attente' ? 'blue' : 'pink',\n                                                                    variant: \"light\",\n                                                                    children: salle.type === 'attente' ? 'Salle d\\'attente' : 'Salle de consultation'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                    children: salle.capacity\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 909,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 908,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openSalleModal(salle),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 919,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 914,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 913,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 927,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 923,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 922,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 912,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, salle.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Tabs.Panel, {\n                        value: \"evenements\",\n                        pt: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Paper, {\n                            p: \"md\",\n                            withBorder: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_3__.Title, {\n                                            order: 4,\n                                            children: \"\\xc9v\\xe9nements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 30\n                                            }, void 0),\n                                            onClick: ()=>openEvenementModal(),\n                                            className: \"bg-blue-500 hover:bg-blue-600\",\n                                            children: \"Nouvel \\xe9v\\xe9nement\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 945,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_20__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table, {\n                                        striped: true,\n                                        highlightOnHover: true,\n                                        withTableBorder: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Thead, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Titre\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"M\\xe9decins\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 959,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Disponibilit\\xe9\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 960,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"D\\xe9but\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"Fin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"de\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            children: \"\\xe0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Th, {\n                                                            style: {\n                                                                width: 100\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                            lineNumber: 965,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 956,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tbody, {\n                                                children: evenements.map((evenement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Tr, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                                    fw: 500,\n                                                                    children: evenement.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 972,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 971,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: evenement.medecins\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                                        color: evenement.indisponible ? '#f52dbe' : '#4caf50',\n                                                                        size: 20\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                        lineNumber: 977,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 975,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: evenement.dateDebut\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: evenement.dateFin\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: \"de\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: \"\\xe0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 986,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.Table.Td, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Group, {\n                                                                    gap: \"xs\",\n                                                                    justify: \"center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                                            label: \"Modifier\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"blue\",\n                                                                                onClick: ()=>openEvenementModal(evenement),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 995,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 990,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 989,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                                            label: \"Supprimer\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.ActionIcon, {\n                                                                                variant: \"subtle\",\n                                                                                color: \"red\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                    lineNumber: 1003,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                                lineNumber: 999,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                            lineNumber: 998,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                    lineNumber: 988,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                                lineNumber: 987,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, evenement.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 955,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 942,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                        lineNumber: 941,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_29__.Modal, {\n                opened: motifModalOpened,\n                onClose: ()=>setMotifModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1023,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                            fw: 600,\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1024,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1022,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Code \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1034,\n                                                    columnNumber: 35\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.code,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    code: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1033,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1032,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Description \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1042,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1042,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.description,\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1040,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1031,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Dur\\xe9e (min) \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1053,\n                                                    columnNumber: 42\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1053,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: motifForm.duration.toString(),\n                                        onChange: (e)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    duration: parseInt(e.target.value) || 15\n                                                })),\n                                        type: \"number\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1052,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1051,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                    color: motifForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1064,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1063,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1061,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1060,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur ray\\xe9e\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1074,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                    color: motifForm.colorRayee,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1076,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1075,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1072,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1050,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                        label: \"Agenda par d\\xe9faut\",\n                                        value: motifForm.agendaDefaut,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    agendaDefaut: value || ''\n                                                })),\n                                        data: agendaDefautOptions,\n                                        rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: 16,\n                                            className: \"cursor-pointer text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 31\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1088,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1087,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: motifForm.servicesDesignes,\n                                        onChange: (value)=>setMotifForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1097,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1096,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1086,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Switch, {\n                                    checked: motifForm.couleurSombre,\n                                    onChange: (e)=>setMotifForm((prev)=>({\n                                                ...prev,\n                                                couleurSombre: e.currentTarget.checked\n                                            })),\n                                    label: \"Couleur sombre\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setMotifModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1115,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveMotif,\n                                            disabled: !motifForm.code || !motifForm.description,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1122,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1114,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1106,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1030,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 1018,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_29__.Modal, {\n                opened: agendaModalOpened,\n                onClose: ()=>setAgendaModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1141,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                            fw: 600,\n                            children: \"Agenda\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1142,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1140,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Nom \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1152,\n                                                    columnNumber: 34\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1152,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: agendaForm.name,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1150,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1160,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                    color: agendaForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1162,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1161,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1159,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1158,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1149,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: \"Description\",\n                                        value: agendaForm.description,\n                                        onChange: (e)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1174,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1173,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: agendaForm.servicesDesignes,\n                                        onChange: (value)=>setAgendaForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1181,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1180,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1172,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_30__.Switch, {\n                                    checked: agendaForm.isResource,\n                                    onChange: (e)=>setAgendaForm((prev)=>({\n                                                ...prev,\n                                                isResource: e.currentTarget.checked\n                                            })),\n                                    label: \"Afficher comme ressource\",\n                                    labelPosition: \"right\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1191,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Group, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            variant: \"filled\",\n                                            color: \"gray\",\n                                            onClick: ()=>setAgendaModalOpened(false),\n                                            children: \"Annuler\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                            variant: \"filled\",\n                                            color: \"blue\",\n                                            onClick: handleSaveAgenda,\n                                            disabled: !agendaForm.name,\n                                            children: \"Enregistrer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1206,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1198,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1190,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1148,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 1136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_29__.Modal, {\n                opened: salleModalOpened,\n                onClose: ()=>setSalleModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1225,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                            fw: 600,\n                            children: \"Ajout du salle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1226,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1224,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Nom \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1236,\n                                                    columnNumber: 34\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1236,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: salleForm.name,\n                                        onChange: (e)=>setSalleForm((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1235,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1234,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Capacit\\xe9 \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 39\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1244,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: salleForm.capacity.toString(),\n                                        onChange: (e)=>setSalleForm((prev)=>({\n                                                    ...prev,\n                                                    capacity: parseInt(e.target.value) || 1\n                                                })),\n                                        type: \"number\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1243,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1242,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                size: \"sm\",\n                                                fw: 500,\n                                                mb: \"xs\",\n                                                children: \"Couleur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1253,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                                    color: salleForm.color,\n                                                    size: 30,\n                                                    className: \"cursor-pointer border border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1255,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                lineNumber: 1254,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1252,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1251,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1233,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: \"Description\",\n                                        value: salleForm.description,\n                                        onChange: (e)=>setSalleForm((prev)=>({\n                                                    ...prev,\n                                                    description: e.target.value\n                                                }))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1267,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_15__.Select, {\n                                        label: \"Services d\\xe9sign\\xe9s\",\n                                        value: salleForm.servicesDesignes,\n                                        onChange: (value)=>setSalleForm((prev)=>({\n                                                    ...prev,\n                                                    servicesDesignes: value || ''\n                                                })),\n                                        data: servicesDesignesOptions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1274,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1273,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1265,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                    size: \"sm\",\n                                    fw: 500,\n                                    mb: \"xs\",\n                                    children: \"Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1284,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    id: \"salle-attente\",\n                                                    name: \"salleType\",\n                                                    checked: salleForm.type === 'attente',\n                                                    onChange: ()=>setSalleForm((prev)=>({\n                                                                ...prev,\n                                                                type: 'attente'\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1287,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"salle-attente\",\n                                                    children: \"Salle d'attente\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1294,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1286,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    id: \"salle-consultation\",\n                                                    name: \"salleType\",\n                                                    checked: salleForm.type === 'consultation',\n                                                    onChange: ()=>setSalleForm((prev)=>({\n                                                                ...prev,\n                                                                type: 'consultation'\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1297,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"salle-consultation\",\n                                                    children: \"Salle de consultation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1304,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1296,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1285,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1283,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end pt-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Group, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        variant: \"filled\",\n                                        color: \"red\",\n                                        onClick: ()=>setSalleModalOpened(false),\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1311,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        variant: \"filled\",\n                                        color: \"blue\",\n                                        disabled: !salleForm.name,\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1318,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 1310,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1309,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1232,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 1220,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_29__.Modal, {\n                opened: evenementModalOpened,\n                onClose: ()=>setEvenementModalOpened(false),\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 bg-blue-500 text-white px-4 py-2 -m-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconBuilding_IconCalendar_IconColorPicker_IconEdit_IconHome_IconPlus_IconSearch_IconSettings_IconTrash_IconX_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1336,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                            fw: 600,\n                            children: \"G\\xe9rer \\xe9v\\xe9nement\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1337,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1335,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                withCloseButton: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Stack, {\n                    gap: \"md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Titre \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-500\",\n                                        children: \"*\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1345,\n                                        columnNumber: 32\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 1345,\n                                columnNumber: 20\n                            }, void 0),\n                            value: evenementForm.title,\n                            onChange: (e)=>setEvenementForm((prev)=>({\n                                        ...prev,\n                                        title: e.target.value\n                                    })),\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1344,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Date d\\xe9but \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1354,\n                                                    columnNumber: 41\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1354,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: evenementForm.dateDebut,\n                                        onChange: (e)=>setEvenementForm((prev)=>({\n                                                    ...prev,\n                                                    dateDebut: e.target.value\n                                                })),\n                                        placeholder: \"DD/MM/YYYY\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1353,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1352,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Grid.Col, {\n                                    span: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.TextInput, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Date fin \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-500\",\n                                                    children: \"*\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                                    lineNumber: 1363,\n                                                    columnNumber: 39\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1363,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        value: evenementForm.dateFin,\n                                        onChange: (e)=>setEvenementForm((prev)=>({\n                                                    ...prev,\n                                                    dateFin: e.target.value\n                                                })),\n                                        placeholder: \"DD/MM/YYYY\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1362,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1361,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1351,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                    size: \"sm\",\n                                    fw: 500,\n                                    mb: \"xs\",\n                                    children: \"Couleur\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1373,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.ColorSwatch, {\n                                        color: evenementForm.color,\n                                        size: 30,\n                                        className: \"cursor-pointer border border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1375,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1374,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1372,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 rounded-full cursor-pointer \".concat(evenementForm.indisponible ? 'bg-red-500' : 'bg-gray-300'),\n                                            onClick: ()=>setEvenementForm((prev)=>({\n                                                        ...prev,\n                                                        indisponible: !prev.indisponible\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1385,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                            size: \"sm\",\n                                            children: \"Indisponible\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1389,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1384,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 rounded-full cursor-pointer \".concat(evenementForm.permanent ? 'bg-gray-500' : 'bg-gray-300'),\n                                            onClick: ()=>setEvenementForm((prev)=>({\n                                                        ...prev,\n                                                        permanent: !prev.permanent\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1392,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                            size: \"sm\",\n                                            children: \"Permanent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1396,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1391,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 rounded-full cursor-pointer \".concat(evenementForm.touteJournee ? 'bg-blue-500' : 'bg-gray-300'),\n                                            onClick: ()=>setEvenementForm((prev)=>({\n                                                        ...prev,\n                                                        touteJournee: !prev.touteJournee\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1399,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                            size: \"sm\",\n                                            children: \"Toute la journ\\xe9e\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1403,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1398,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 rounded-full cursor-pointer \".concat(evenementForm.tousLesJours ? 'bg-blue-500' : 'bg-gray-300'),\n                                            onClick: ()=>setEvenementForm((prev)=>({\n                                                        ...prev,\n                                                        tousLesJours: !prev.tousLesJours\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1406,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                            size: \"sm\",\n                                            children: \"Tous les jours\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1410,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1405,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 rounded-full cursor-pointer \".concat(evenementForm.cabinet ? 'bg-blue-500' : 'bg-gray-300'),\n                                            onClick: ()=>setEvenementForm((prev)=>({\n                                                        ...prev,\n                                                        cabinet: !prev.cabinet\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1413,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                            size: \"sm\",\n                                            children: \"Cabinet\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                            lineNumber: 1417,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                    lineNumber: 1412,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1383,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end pt-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.Group, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        variant: \"filled\",\n                                        color: \"red\",\n                                        onClick: ()=>setEvenementModalOpened(false),\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1423,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        variant: \"filled\",\n                                        color: \"blue\",\n                                        disabled: !evenementForm.title,\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                        lineNumber: 1430,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                                lineNumber: 1422,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                            lineNumber: 1421,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                    lineNumber: 1343,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n                lineNumber: 1331,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parameters_de_base\\\\AccueilEtAgenda.tsx\",\n        lineNumber: 416,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AccueilEtAgenda, \"9TmtZyRHMHLof8M+DHEVpq/AogA=\");\n_c = AccueilEtAgenda;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AccueilEtAgenda);\nvar _c;\n$RefreshReg$(_c, \"AccueilEtAgenda\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGRhc2hib2FyZCkvc2V0dGluZ3MvcGFyYW1ldGVyc19kZV9iYXNlL0FjY3VlaWxFdEFnZW5kYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBd0M7QUFxQmpCO0FBYU07QUFpRDdCLE1BQU0rQixrQkFBa0I7O0lBQ3RCLHlCQUF5QjtJQUN6QixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR2hDLCtDQUFRQSxDQUFTO0lBRW5ELHlCQUF5QjtJQUN6QixNQUFNLENBQUNpQyxrQkFBa0JDLG9CQUFvQixHQUFHbEMsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDbUMsbUJBQW1CQyxxQkFBcUIsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQ3FDLGtCQUFrQkMsb0JBQW9CLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUN1QyxzQkFBc0JDLHdCQUF3QixHQUFHeEMsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDeUMsY0FBY0MsZ0JBQWdCLEdBQUcxQywrQ0FBUUEsQ0FBZTtJQUMvRCxNQUFNLENBQUMyQyxlQUFlQyxpQkFBaUIsR0FBRzVDLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUM2QyxjQUFjQyxnQkFBZ0IsR0FBRzlDLCtDQUFRQSxDQUFlO0lBQy9ELE1BQU0sQ0FBQytDLGtCQUFrQkMsb0JBQW9CLEdBQUdoRCwrQ0FBUUEsQ0FBbUI7SUFFM0UsNkJBQTZCO0lBQzdCLE1BQU0sQ0FBQ2lELFdBQVdDLGFBQWEsR0FBR2xELCtDQUFRQSxDQUFDO1FBQ3pDbUQsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxZQUFZO1FBQ1pDLGNBQWM7UUFDZEMsa0JBQWtCO1FBQ2xCQyxlQUFlO0lBQ2pCO0lBRUEsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUc1RCwrQ0FBUUEsQ0FBQztRQUMzQzZELE1BQU07UUFDTlQsYUFBYTtRQUNiRSxPQUFPO1FBQ1BHLGtCQUFrQjtRQUNsQkssWUFBWTtJQUNkO0lBRUEsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdoRSwrQ0FBUUEsQ0FBQztRQUN6QzZELE1BQU07UUFDTlQsYUFBYTtRQUNiRSxPQUFPO1FBQ1BXLE1BQU07UUFDTkMsVUFBVTtRQUNWVCxrQkFBa0I7SUFDcEI7SUFFQSxNQUFNLENBQUNVLGVBQWVDLGlCQUFpQixHQUFHcEUsK0NBQVFBLENBQUM7UUFDakRxRSxPQUFPO1FBQ1BDLFdBQVc7UUFDWEMsU0FBUztRQUNUakIsT0FBTztRQUNQa0IsY0FBYztRQUNkQyxXQUFXO1FBQ1hDLGNBQWM7UUFDZEMsY0FBYztRQUNkQyxTQUFTO0lBQ1g7SUFFQSxxQ0FBcUM7SUFDckMsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHOUUsK0NBQVFBLENBQUM7UUFDckQrRSxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUNoQkMsb0JBQW9CO1FBQ3BCQyxxQkFBcUI7UUFDckJDLG1CQUFtQjtRQUNuQkMsZUFBZTtRQUNmQyxzQkFBc0I7UUFDdEJDLDJCQUEyQjtRQUMzQkMsa0JBQWtCO1FBQ2xCQyxzQkFBc0I7UUFDdEJDLHVCQUF1QjtRQUN2QkMsbUNBQW1DO0lBQ3JDO0lBRUEsa0NBQWtDO0lBQ2xDLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHNUYsK0NBQVFBLENBQVU7UUFDNUM7WUFBRTZGLElBQUk7WUFBRzFDLE1BQU07WUFBT0MsYUFBYTtZQUFnQkMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUN6RztZQUFFc0MsSUFBSTtZQUFHMUMsTUFBTTtZQUFPQyxhQUFhO1lBQVlDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDckc7WUFBRXNDLElBQUk7WUFBRzFDLE1BQU07WUFBT0MsYUFBYTtZQUFrQkMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUMzRztZQUFFc0MsSUFBSTtZQUFHMUMsTUFBTTtZQUFPQyxhQUFhO1lBQWFDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDdEc7WUFBRXNDLElBQUk7WUFBRzFDLE1BQU07WUFBTUMsYUFBYTtZQUFrQkMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUMxRztZQUFFc0MsSUFBSTtZQUFHMUMsTUFBTTtZQUFNQyxhQUFhO1lBQW9CQyxVQUFVO1lBQUlDLE9BQU87WUFBV0MsWUFBWTtRQUFVO1FBQzVHO1lBQUVzQyxJQUFJO1lBQUcxQyxNQUFNO1lBQU9DLGFBQWE7WUFBU0MsVUFBVTtZQUFJQyxPQUFPO1lBQXFCQyxZQUFZO1FBQVU7UUFDNUc7WUFBRXNDLElBQUk7WUFBRzFDLE1BQU07WUFBT0MsYUFBYTtZQUFjQyxVQUFVO1lBQUlDLE9BQU87WUFBV0MsWUFBWTtRQUFVO1FBQ3ZHO1lBQUVzQyxJQUFJO1lBQUcxQyxNQUFNO1lBQU9DLGFBQWE7WUFBYUMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUN0RztZQUFFc0MsSUFBSTtZQUFJMUMsTUFBTTtZQUFPQyxhQUFhO1lBQWlCQyxVQUFVO1lBQUlDLE9BQU87WUFBV0MsWUFBWTtRQUFVO1FBQzNHO1lBQUVzQyxJQUFJO1lBQUkxQyxNQUFNO1lBQU9DLGFBQWE7WUFBZUMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUN6RztZQUFFc0MsSUFBSTtZQUFJMUMsTUFBTTtZQUFNQyxhQUFhO1lBQW9CQyxVQUFVO1lBQUlDLE9BQU87WUFBV0MsWUFBWTtRQUFVO1FBQzdHO1lBQUVzQyxJQUFJO1lBQUkxQyxNQUFNO1lBQU9DLGFBQWE7WUFBV0MsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUNyRztZQUFFc0MsSUFBSTtZQUFJMUMsTUFBTTtZQUFPQyxhQUFhO1lBQWFDLFVBQVU7WUFBSUMsT0FBTztZQUFXQyxZQUFZO1FBQVU7UUFDdkc7WUFBRXNDLElBQUk7WUFBSTFDLE1BQU07WUFBT0MsYUFBYTtZQUFtQkMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtRQUM3RztZQUFFc0MsSUFBSTtZQUFJMUMsTUFBTTtZQUFPQyxhQUFhO1lBQWlCQyxVQUFVO1lBQUlDLE9BQU87WUFBV0MsWUFBWTtRQUFVO1FBQzNHO1lBQUVzQyxJQUFJO1lBQUkxQyxNQUFNO1lBQU9DLGFBQWE7WUFBZUMsVUFBVTtZQUFJQyxPQUFPO1lBQVdDLFlBQVk7UUFBVTtLQUMxRztJQUVELG1DQUFtQztJQUNuQyxNQUFNLENBQUN1QyxTQUFTQyxXQUFXLEdBQUcvRiwrQ0FBUUEsQ0FBVztRQUMvQztZQUFFNkYsSUFBSTtZQUFHaEMsTUFBTTtZQUFZVCxhQUFhO1lBQUlFLE9BQU87WUFBV1EsWUFBWTtRQUFNO1FBQ2hGO1lBQUUrQixJQUFJO1lBQUdoQyxNQUFNO1lBQVlULGFBQWE7WUFBSUUsT0FBTztZQUFXUSxZQUFZO1FBQU07UUFDaEY7WUFBRStCLElBQUk7WUFBR2hDLE1BQU07WUFBV1QsYUFBYTtZQUFJRSxPQUFPO1lBQVdRLFlBQVk7UUFBTTtLQUNoRjtJQUVELDZEQUE2RDtJQUM3RCxNQUFNLENBQUNrQyxVQUFVQyxZQUFZLEdBQUdqRywrQ0FBUUEsQ0FBWTtRQUNsRDtZQUFFNkYsSUFBSTtZQUFHaEMsTUFBTTtZQUFXUCxPQUFPO1FBQVU7S0FDNUM7SUFFRCxrQ0FBa0M7SUFDbEMsTUFBTSxDQUFDNEMsUUFBUUMsVUFBVSxHQUFHbkcsK0NBQVFBLENBQVU7UUFDNUM7WUFBRTZGLElBQUk7WUFBR2hDLE1BQU07WUFBT1QsYUFBYTtZQUFvQkUsT0FBTztZQUFXVyxNQUFNO1lBQVdDLFVBQVU7UUFBRztRQUN2RztZQUFFMkIsSUFBSTtZQUFHaEMsTUFBTTtZQUFPVCxhQUFhO1lBQWNFLE9BQU87WUFBV1csTUFBTTtZQUFnQkMsVUFBVTtRQUFFO1FBQ3JHO1lBQUUyQixJQUFJO1lBQUdoQyxNQUFNO1lBQVFULGFBQWE7WUFBSUUsT0FBTztZQUFXVyxNQUFNO1lBQWdCQyxVQUFVO1FBQUU7UUFDNUY7WUFBRTJCLElBQUk7WUFBR2hDLE1BQU07WUFBMkJULGFBQWE7WUFBSUUsT0FBTztZQUFXVyxNQUFNO1lBQWdCQyxVQUFVO1FBQUU7S0FDaEg7SUFFRCxzQ0FBc0M7SUFDdEMsTUFBTSxDQUFDa0MsWUFBWUMsY0FBYyxHQUFHckcsK0NBQVFBLENBQWM7UUFDeEQ7WUFDRTZGLElBQUk7WUFDSnhCLE9BQU87WUFDUGlDLFVBQVU7WUFDVmhDLFdBQVc7WUFDWEMsU0FBUztZQUNUakIsT0FBTztZQUNQa0IsY0FBYztZQUNkQyxXQUFXO1lBQ1hDLGNBQWM7WUFDZEMsY0FBYztZQUNkQyxTQUFTO1FBQ1g7S0FDRDtJQUVELDBCQUEwQjtJQUMxQixNQUFNLENBQUMyQixZQUFZQyxjQUFjLEdBQUd4RywrQ0FBUUEsQ0FBQztJQUU3Qyx3Q0FBd0M7SUFDeEMsTUFBTXlHLGlCQUFpQmQsT0FBT2UsTUFBTSxDQUFDQyxDQUFBQSxRQUNuQ0EsTUFBTXhELElBQUksQ0FBQ3lELFdBQVcsR0FBR0MsUUFBUSxDQUFDTixXQUFXSyxXQUFXLE9BQ3hERCxNQUFNdkQsV0FBVyxDQUFDd0QsV0FBVyxHQUFHQyxRQUFRLENBQUNOLFdBQVdLLFdBQVc7SUFHakUsc0JBQXNCO0lBQ3RCLE1BQU1FLGVBQWU7UUFBQztRQUFTO1FBQVM7UUFBWTtRQUFTO1FBQVk7UUFBVTtLQUFXO0lBRTlGLDJCQUEyQjtJQUMzQixNQUFNQyx1QkFBdUI7UUFDM0I7WUFBRUMsT0FBTztZQUFXQyxPQUFPO1FBQVU7UUFDckM7WUFBRUQsT0FBTztZQUFRQyxPQUFPO1FBQU87UUFDL0I7WUFBRUQsT0FBTztZQUFRQyxPQUFPO1FBQU87S0FDaEM7SUFFRCxNQUFNQyxvQkFBb0I7UUFDeEI7WUFBRUYsT0FBTztZQUFhQyxPQUFPO1FBQVk7UUFDekM7WUFBRUQsT0FBTztZQUFjQyxPQUFPO1FBQWE7UUFDM0M7WUFBRUQsT0FBTztZQUFjQyxPQUFPO1FBQWE7UUFDM0M7WUFBRUQsT0FBTztZQUFjQyxPQUFPO1FBQWE7S0FDNUM7SUFFRCxNQUFNRSx1QkFBdUI7UUFDM0I7WUFBRUgsT0FBTztZQUFxQkMsT0FBTztRQUFvQjtRQUN6RDtZQUFFRCxPQUFPO1lBQWdCQyxPQUFPO1FBQWU7UUFDL0M7WUFBRUQsT0FBTztZQUFPQyxPQUFPO1FBQU07S0FDOUI7SUFFRCxNQUFNRyxzQkFBc0I7UUFDMUI7WUFBRUosT0FBTztZQUFXQyxPQUFPO1FBQW9CO1FBQy9DO1lBQUVELE9BQU87WUFBV0MsT0FBTztRQUFXO0tBQ3ZDO0lBRUQsTUFBTUksMEJBQTBCO1FBQzlCO1lBQUVMLE9BQU87WUFBWUMsT0FBTztRQUFvQjtRQUNoRDtZQUFFRCxPQUFPO1lBQVlDLE9BQU87UUFBWTtLQUN6QztJQUVELG1DQUFtQztJQUNuQyxNQUFNSyxpQkFBaUIsQ0FBQ1g7UUFDdEIsSUFBSUEsT0FBTztZQUNUakUsZ0JBQWdCaUU7WUFDaEJ6RCxhQUFhO2dCQUNYQyxNQUFNd0QsTUFBTXhELElBQUk7Z0JBQ2hCQyxhQUFhdUQsTUFBTXZELFdBQVc7Z0JBQzlCQyxVQUFVc0QsTUFBTXRELFFBQVE7Z0JBQ3hCQyxPQUFPcUQsTUFBTXJELEtBQUs7Z0JBQ2xCQyxZQUFZb0QsTUFBTXBELFVBQVU7Z0JBQzVCQyxjQUFjO2dCQUNkQyxrQkFBa0I7Z0JBQ2xCQyxlQUFlO1lBQ2pCO1FBQ0YsT0FBTztZQUNMaEIsZ0JBQWdCO1lBQ2hCUSxhQUFhO2dCQUNYQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxVQUFVO2dCQUNWQyxPQUFPO2dCQUNQQyxZQUFZO2dCQUNaQyxjQUFjO2dCQUNkQyxrQkFBa0I7Z0JBQ2xCQyxlQUFlO1lBQ2pCO1FBQ0Y7UUFDQXhCLG9CQUFvQjtJQUN0QjtJQUVBLE1BQU1xRixrQkFBa0IsQ0FBQ0M7UUFDdkIsSUFBSUEsUUFBUTtZQUNWNUUsaUJBQWlCNEU7WUFDakI1RCxjQUFjO2dCQUNaQyxNQUFNMkQsT0FBTzNELElBQUk7Z0JBQ2pCVCxhQUFhb0UsT0FBT3BFLFdBQVc7Z0JBQy9CRSxPQUFPa0UsT0FBT2xFLEtBQUs7Z0JBQ25CRyxrQkFBa0I7Z0JBQ2xCSyxZQUFZMEQsT0FBTzFELFVBQVU7WUFDL0I7UUFDRixPQUFPO1lBQ0xsQixpQkFBaUI7WUFDakJnQixjQUFjO2dCQUNaQyxNQUFNO2dCQUNOVCxhQUFhO2dCQUNiRSxPQUFPO2dCQUNQRyxrQkFBa0I7Z0JBQ2xCSyxZQUFZO1lBQ2Q7UUFDRjtRQUNBMUIscUJBQXFCO0lBQ3ZCO0lBRUEsTUFBTXFGLGlCQUFpQixDQUFDQztRQUN0QixJQUFJQSxPQUFPO1lBQ1Q1RSxnQkFBZ0I0RTtZQUNoQjFELGFBQWE7Z0JBQ1hILE1BQU02RCxNQUFNN0QsSUFBSTtnQkFDaEJULGFBQWFzRSxNQUFNdEUsV0FBVztnQkFDOUJFLE9BQU9vRSxNQUFNcEUsS0FBSztnQkFDbEJXLE1BQU15RCxNQUFNekQsSUFBSTtnQkFDaEJDLFVBQVV3RCxNQUFNeEQsUUFBUTtnQkFDeEJULGtCQUFrQjtZQUNwQjtRQUNGLE9BQU87WUFDTFgsZ0JBQWdCO1lBQ2hCa0IsYUFBYTtnQkFDWEgsTUFBTTtnQkFDTlQsYUFBYTtnQkFDYkUsT0FBTztnQkFDUFcsTUFBTTtnQkFDTkMsVUFBVTtnQkFDVlQsa0JBQWtCO1lBQ3BCO1FBQ0Y7UUFDQW5CLG9CQUFvQjtJQUN0QjtJQUVBLE1BQU1xRixxQkFBcUIsQ0FBQ0M7UUFDMUIsSUFBSUEsV0FBVztZQUNiNUUsb0JBQW9CNEU7WUFDcEJ4RCxpQkFBaUI7Z0JBQ2ZDLE9BQU91RCxVQUFVdkQsS0FBSztnQkFDdEJDLFdBQVdzRCxVQUFVdEQsU0FBUztnQkFDOUJDLFNBQVNxRCxVQUFVckQsT0FBTztnQkFDMUJqQixPQUFPc0UsVUFBVXRFLEtBQUs7Z0JBQ3RCa0IsY0FBY29ELFVBQVVwRCxZQUFZO2dCQUNwQ0MsV0FBV21ELFVBQVVuRCxTQUFTO2dCQUM5QkMsY0FBY2tELFVBQVVsRCxZQUFZO2dCQUNwQ0MsY0FBY2lELFVBQVVqRCxZQUFZO2dCQUNwQ0MsU0FBU2dELFVBQVVoRCxPQUFPO1lBQzVCO1FBQ0YsT0FBTztZQUNMNUIsb0JBQW9CO1lBQ3BCb0IsaUJBQWlCO2dCQUNmQyxPQUFPO2dCQUNQQyxXQUFXO2dCQUNYQyxTQUFTO2dCQUNUakIsT0FBTztnQkFDUGtCLGNBQWM7Z0JBQ2RDLFdBQVc7Z0JBQ1hDLGNBQWM7Z0JBQ2RDLGNBQWM7Z0JBQ2RDLFNBQVM7WUFDWDtRQUNGO1FBQ0FwQyx3QkFBd0I7SUFDMUI7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTXFGLGtCQUFrQjtRQUN0QixJQUFJcEYsY0FBYztZQUNoQiw2QkFBNkI7WUFDN0JtRCxVQUFVa0MsQ0FBQUEsT0FBUUEsS0FBS0MsR0FBRyxDQUFDQyxDQUFBQSxJQUN6QkEsRUFBRW5DLEVBQUUsS0FBS3BELGFBQWFvRCxFQUFFLEdBQ3BCO3dCQUFFLEdBQUdtQyxDQUFDO3dCQUFFLEdBQUcvRSxTQUFTO29CQUFDLElBQ3JCK0U7UUFFUixPQUFPO1lBQ0wsMkJBQTJCO1lBQzNCLE1BQU1DLFdBQWtCO2dCQUN0QnBDLElBQUlxQyxLQUFLQyxHQUFHO2dCQUNaaEYsTUFBTUYsVUFBVUUsSUFBSTtnQkFDcEJDLGFBQWFILFVBQVVHLFdBQVc7Z0JBQ2xDQyxVQUFVSixVQUFVSSxRQUFRO2dCQUM1QkMsT0FBT0wsVUFBVUssS0FBSztnQkFDdEJDLFlBQVlOLFVBQVVNLFVBQVU7WUFDbEM7WUFDQXFDLFVBQVVrQyxDQUFBQSxPQUFRO3VCQUFJQTtvQkFBTUc7aUJBQVM7UUFDdkM7UUFDQS9GLG9CQUFvQjtJQUN0QjtJQUVBLE1BQU1rRyxtQkFBbUI7UUFDdkIsSUFBSXpGLGVBQWU7WUFDakIsOEJBQThCO1lBQzlCb0QsV0FBVytCLENBQUFBLE9BQVFBLEtBQUtDLEdBQUcsQ0FBQ00sQ0FBQUEsSUFDMUJBLEVBQUV4QyxFQUFFLEtBQUtsRCxjQUFja0QsRUFBRSxHQUNyQjt3QkFBRSxHQUFHd0MsQ0FBQzt3QkFBRSxHQUFHMUUsVUFBVTtvQkFBQyxJQUN0QjBFO1FBRVIsT0FBTztZQUNMLDJCQUEyQjtZQUMzQixNQUFNQyxZQUFvQjtnQkFDeEJ6QyxJQUFJcUMsS0FBS0MsR0FBRztnQkFDWnRFLE1BQU1GLFdBQVdFLElBQUk7Z0JBQ3JCVCxhQUFhTyxXQUFXUCxXQUFXO2dCQUNuQ0UsT0FBT0ssV0FBV0wsS0FBSztnQkFDdkJRLFlBQVlILFdBQVdHLFVBQVU7WUFDbkM7WUFDQWlDLFdBQVcrQixDQUFBQSxPQUFRO3VCQUFJQTtvQkFBTVE7aUJBQVU7UUFDekM7UUFDQWxHLHFCQUFxQjtJQUN2QjtJQUVBLG1DQUFtQztJQUNuQyxNQUFNbUcsb0JBQW9CLENBQUMxQztRQUN6QkQsVUFBVWtDLENBQUFBLE9BQVFBLEtBQUtwQixNQUFNLENBQUNzQixDQUFBQSxJQUFLQSxFQUFFbkMsRUFBRSxLQUFLQTtJQUM5QztJQUVBLHFCQUNFLDhEQUFDNUYsZ0RBQUtBO1FBQUN1SSxLQUFJO1FBQUtDLFdBQVU7OzBCQUN4Qiw4REFBQ3ZJLGdEQUFLQTtnQkFBQ3dJLE9BQU87Z0JBQUdELFdBQVU7O2tDQUN6Qiw4REFBQy9HLDhMQUFRQTt3QkFBQ2lILE1BQU07d0JBQUlGLFdBQVU7Ozs7OztvQkFBa0I7Ozs7Ozs7MEJBSWxELDhEQUFDdEksK0NBQUlBO2dCQUFDNkcsT0FBT2pGO2dCQUFXNkcsVUFBVSxDQUFDNUIsUUFBVWhGLGFBQWFnRixTQUFTO2dCQUFZNkIsU0FBUTs7a0NBQ3JGLDhEQUFDMUksK0NBQUlBLENBQUMySSxJQUFJOzswQ0FDUiw4REFBQzNJLCtDQUFJQSxDQUFDNEksR0FBRztnQ0FBQy9CLE9BQU07Z0NBQVVnQywyQkFBYSw4REFBQ3ZILDhMQUFZQTtvQ0FBQ2tILE1BQU07Ozs7OzswQ0FBUTs7Ozs7OzBDQUduRSw4REFBQ3hJLCtDQUFJQSxDQUFDNEksR0FBRztnQ0FBQy9CLE9BQU07Z0NBQVNnQywyQkFBYSw4REFBQ3hILDhMQUFZQTtvQ0FBQ21ILE1BQU07Ozs7OzswQ0FBUTs7Ozs7OzBDQUdsRSw4REFBQ3hJLCtDQUFJQSxDQUFDNEksR0FBRztnQ0FBQy9CLE9BQU07Z0NBQWdCZ0MsMkJBQWEsOERBQUNySCw4TEFBZUE7b0NBQUNnSCxNQUFNOzs7Ozs7MENBQVE7Ozs7OzswQ0FHNUUsOERBQUN4SSwrQ0FBSUEsQ0FBQzRJLEdBQUc7Z0NBQUMvQixPQUFNO2dDQUFVZ0MsMkJBQWEsOERBQUN4SCw4TEFBWUE7b0NBQUNtSCxNQUFNOzs7Ozs7MENBQVE7Ozs7OzswQ0FHbkUsOERBQUN4SSwrQ0FBSUEsQ0FBQzRJLEdBQUc7Z0NBQUMvQixPQUFNO2dDQUFTZ0MsMkJBQWEsOERBQUNuSCw4TEFBWUE7b0NBQUM4RyxNQUFNOzs7Ozs7MENBQVE7Ozs7OzswQ0FHbEUsOERBQUN4SSwrQ0FBSUEsQ0FBQzRJLEdBQUc7Z0NBQUMvQixPQUFNO2dDQUFhZ0MsMkJBQWEsOERBQUN2SCw4TEFBWUE7b0NBQUNrSCxNQUFNOzs7Ozs7MENBQVE7Ozs7Ozs7Ozs7OztrQ0FNeEUsOERBQUN4SSwrQ0FBSUEsQ0FBQzhJLEtBQUs7d0JBQUNqQyxPQUFNO3dCQUFVa0MsSUFBRztrQ0FDN0IsNEVBQUM5SSxpREFBS0E7NEJBQUMrSSxHQUFFOzRCQUFLQyxVQUFVO3NDQUN0Qiw0RUFBQ25KLGdEQUFLQTtnQ0FBQ3VJLEtBQUk7O2tEQUVULDhEQUFDYTs7MERBQ0MsOERBQUNuSixnREFBS0E7Z0RBQUN3SSxPQUFPO2dEQUFHRCxXQUFVOzBEQUFzQjs7Ozs7OzBEQUdqRCw4REFBQ3BJLGdEQUFJQTs7a0VBQ0gsOERBQUNBLGdEQUFJQSxDQUFDaUosR0FBRzt3REFBQ0MsTUFBTTs7MEVBQ2QsOERBQUNqSixnREFBSUE7Z0VBQUNxSSxNQUFLO2dFQUFLYSxJQUFJO2dFQUFLQyxJQUFHOzBFQUFLOzs7Ozs7MEVBR2pDLDhEQUFDbEoscURBQVNBO2dFQUNSeUcsT0FBT25DLGdCQUFnQkUsY0FBYztnRUFDckM2RCxVQUFVLENBQUNjLElBQU01RSxtQkFBbUJnRCxDQUFBQSxPQUFTOzRFQUMzQyxHQUFHQSxJQUFJOzRFQUNQL0MsZ0JBQWdCMkUsRUFBRUMsTUFBTSxDQUFDM0MsS0FBSzt3RUFDaEM7Z0VBQ0E0QyxhQUFZOzs7Ozs7Ozs7Ozs7a0VBR2hCLDhEQUFDdkosZ0RBQUlBLENBQUNpSixHQUFHO3dEQUFDQyxNQUFNOzswRUFDZCw4REFBQ2pKLGdEQUFJQTtnRUFBQ3FJLE1BQUs7Z0VBQUthLElBQUk7Z0VBQUtDLElBQUc7MEVBQUs7Ozs7OzswRUFHakMsOERBQUNsSixxREFBU0E7Z0VBQ1J5RyxPQUFPbkMsZ0JBQWdCRyxjQUFjO2dFQUNyQzRELFVBQVUsQ0FBQ2MsSUFBTTVFLG1CQUFtQmdELENBQUFBLE9BQVM7NEVBQzNDLEdBQUdBLElBQUk7NEVBQ1A5QyxnQkFBZ0IwRSxFQUFFQyxNQUFNLENBQUMzQyxLQUFLO3dFQUNoQztnRUFDQTRDLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNbEIsOERBQUNQO2dEQUFJWixXQUFVOztrRUFDYiw4REFBQ25JLGdEQUFJQTt3REFBQ3FJLE1BQUs7d0RBQUthLElBQUk7d0RBQUtDLElBQUc7a0VBQUs7Ozs7OztrRUFHakMsOERBQUNKO3dEQUFJWixXQUFVO2tFQUNaM0IsYUFBYWlCLEdBQUcsQ0FBQyxDQUFDOEIscUJBQ2pCLDhEQUFDM0ksaURBQUtBO2dFQUVKMkgsU0FBUTtnRUFDUnZGLE9BQU07Z0VBQ05tRixXQUFVOzBFQUVUb0I7K0RBTElBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQWFmLDhEQUFDUjs7MERBQ0MsOERBQUNuSixnREFBS0E7Z0RBQUN3SSxPQUFPO2dEQUFHRCxXQUFVOzBEQUFzQjs7Ozs7OzBEQUdqRCw4REFBQ3BJLGdEQUFJQTs7a0VBQ0gsOERBQUNBLGdEQUFJQSxDQUFDaUosR0FBRzt3REFBQ0MsTUFBTTtrRUFDZCw0RUFBQy9JLGtEQUFNQTs0REFDTHlHLE9BQU07NERBQ05ELE9BQU9uQyxnQkFBZ0JJLGtCQUFrQjs0REFDekMyRCxVQUFVLENBQUM1QixRQUFVbEMsbUJBQW1CZ0QsQ0FBQUEsT0FBUzt3RUFDL0MsR0FBR0EsSUFBSTt3RUFDUDdDLG9CQUFvQitCLFNBQVM7b0VBQy9COzREQUNBOEMsTUFBTWhELGFBQWFpQixHQUFHLENBQUM4QixDQUFBQSxPQUFTO29FQUFFN0MsT0FBTzZDO29FQUFNNUMsT0FBTzRDO2dFQUFLOzs7Ozs7Ozs7OztrRUFHL0QsOERBQUN4SixnREFBSUEsQ0FBQ2lKLEdBQUc7d0RBQUNDLE1BQU07a0VBQ2QsNEVBQUMvSSxrREFBTUE7NERBQ0x5RyxPQUFNOzREQUNORCxPQUFPbkMsZ0JBQWdCSyxtQkFBbUI7NERBQzFDMEQsVUFBVSxDQUFDNUIsUUFBVWxDLG1CQUFtQmdELENBQUFBLE9BQVM7d0VBQy9DLEdBQUdBLElBQUk7d0VBQ1A1QyxxQkFBcUI4QixTQUFTO29FQUNoQzs0REFDQThDLE1BQU0vQzs7Ozs7Ozs7Ozs7a0VBR1YsOERBQUMxRyxnREFBSUEsQ0FBQ2lKLEdBQUc7d0RBQUNDLE1BQU07a0VBQ2QsNEVBQUMvSSxrREFBTUE7NERBQ0x5RyxPQUFNOzREQUNORCxPQUFPbkMsZ0JBQWdCTSxpQkFBaUI7NERBQ3hDeUQsVUFBVSxDQUFDNUIsUUFBVWxDLG1CQUFtQmdELENBQUFBLE9BQVM7d0VBQy9DLEdBQUdBLElBQUk7d0VBQ1AzQyxtQkFBbUI2QixTQUFTO29FQUM5Qjs0REFDQThDLE1BQU01Qzs7Ozs7Ozs7Ozs7a0VBR1YsOERBQUM3RyxnREFBSUEsQ0FBQ2lKLEdBQUc7d0RBQUNDLE1BQU07a0VBQ2QsNEVBQUMvSSxrREFBTUE7NERBQ0x5RyxPQUFNOzREQUNORCxPQUFPbkMsZ0JBQWdCTyxhQUFhOzREQUNwQ3dELFVBQVUsQ0FBQzVCLFFBQVVsQyxtQkFBbUJnRCxDQUFBQSxPQUFTO3dFQUMvQyxHQUFHQSxJQUFJO3dFQUNQMUMsZUFBZTRCLFNBQVM7b0VBQzFCOzREQUNBOEMsTUFBTTNDOzs7Ozs7Ozs7Ozs7Ozs7OzswREFNWiw4REFBQ2xILGdEQUFLQTtnREFBQ3VJLEtBQUk7Z0RBQUt1QixJQUFHOztrRUFDakIsOERBQUNoSixvREFBUUE7d0RBQ1BpSixTQUFTbkYsZ0JBQWdCUSxvQkFBb0I7d0RBQzdDdUQsVUFBVSxDQUFDYyxJQUFNNUUsbUJBQW1CZ0QsQ0FBQUEsT0FBUztvRUFDM0MsR0FBR0EsSUFBSTtvRUFDUHpDLHNCQUFzQnFFLEVBQUVPLGFBQWEsQ0FBQ0QsT0FBTztnRUFDL0M7d0RBQ0EvQyxPQUFNOzs7Ozs7a0VBRVIsOERBQUNsRyxvREFBUUE7d0RBQ1BpSixTQUFTbkYsZ0JBQWdCUyx5QkFBeUI7d0RBQ2xEc0QsVUFBVSxDQUFDYyxJQUFNNUUsbUJBQW1CZ0QsQ0FBQUEsT0FBUztvRUFDM0MsR0FBR0EsSUFBSTtvRUFDUHhDLDJCQUEyQm9FLEVBQUVPLGFBQWEsQ0FBQ0QsT0FBTztnRUFDcEQ7d0RBQ0EvQyxPQUFNOzs7Ozs7a0VBRVIsOERBQUNsRyxvREFBUUE7d0RBQ1BpSixTQUFTbkYsZ0JBQWdCVSxnQkFBZ0I7d0RBQ3pDcUQsVUFBVSxDQUFDYyxJQUFNNUUsbUJBQW1CZ0QsQ0FBQUEsT0FBUztvRUFDM0MsR0FBR0EsSUFBSTtvRUFDUHZDLGtCQUFrQm1FLEVBQUVPLGFBQWEsQ0FBQ0QsT0FBTztnRUFDM0M7d0RBQ0EvQyxPQUFNOzs7Ozs7a0VBRVIsOERBQUNsRyxvREFBUUE7d0RBQ1BpSixTQUFTbkYsZ0JBQWdCVyxvQkFBb0I7d0RBQzdDb0QsVUFBVSxDQUFDYyxJQUFNNUUsbUJBQW1CZ0QsQ0FBQUEsT0FBUztvRUFDM0MsR0FBR0EsSUFBSTtvRUFDUHRDLHNCQUFzQmtFLEVBQUVPLGFBQWEsQ0FBQ0QsT0FBTztnRUFDL0M7d0RBQ0EvQyxPQUFNOzs7Ozs7a0VBRVIsOERBQUNsRyxvREFBUUE7d0RBQ1BpSixTQUFTbkYsZ0JBQWdCWSxxQkFBcUI7d0RBQzlDbUQsVUFBVSxDQUFDYyxJQUFNNUUsbUJBQW1CZ0QsQ0FBQUEsT0FBUztvRUFDM0MsR0FBR0EsSUFBSTtvRUFDUHJDLHVCQUF1QmlFLEVBQUVPLGFBQWEsQ0FBQ0QsT0FBTztnRUFDaEQ7d0RBQ0EvQyxPQUFNOzs7Ozs7a0VBRVIsOERBQUNsRyxvREFBUUE7d0RBQ1BpSixTQUFTbkYsZ0JBQWdCYSxpQ0FBaUM7d0RBQzFEa0QsVUFBVSxDQUFDYyxJQUFNNUUsbUJBQW1CZ0QsQ0FBQUEsT0FBUztvRUFDM0MsR0FBR0EsSUFBSTtvRUFDUHBDLG1DQUFtQ2dFLEVBQUVPLGFBQWEsQ0FBQ0QsT0FBTztnRUFDNUQ7d0RBQ0EvQyxPQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTVosOERBQUNvQzs7MERBQ0MsOERBQUNuSixnREFBS0E7Z0RBQUN3SSxPQUFPO2dEQUFHRCxXQUFVOzBEQUFzQjs7Ozs7OzBEQUdqRCw4REFBQ25JLGdEQUFJQTtnREFBQ3FJLE1BQUs7Z0RBQUthLElBQUk7Z0RBQUtDLElBQUc7MERBQUs7Ozs7OzswREFHakMsOERBQUNwSixnREFBSUE7O2tFQUNILDhEQUFDQSxnREFBSUEsQ0FBQ2lKLEdBQUc7d0RBQUNDLE1BQU07a0VBQ2QsNEVBQUMvSSxrREFBTUE7NERBQ0xvSixhQUFZOzREQUNaRSxNQUFNO2dFQUNKO29FQUFFOUMsT0FBTztvRUFBS0MsT0FBTztnRUFBYTtnRUFDbEM7b0VBQUVELE9BQU87b0VBQU1DLE9BQU87Z0VBQWE7Z0VBQ25DO29FQUFFRCxPQUFPO29FQUFNQyxPQUFPO2dFQUFhOzZEQUNwQzs7Ozs7Ozs7Ozs7a0VBR0wsOERBQUM1RyxnREFBSUEsQ0FBQ2lKLEdBQUc7d0RBQUNDLE1BQU07a0VBQ2QsNEVBQUMvSSxrREFBTUE7NERBQ0xvSixhQUFZOzREQUNaRSxNQUFNO2dFQUNKO29FQUFFOUMsT0FBTztvRUFBVUMsT0FBTztnRUFBZTtnRUFDekM7b0VBQUVELE9BQU87b0VBQVVDLE9BQU87Z0VBQVU7NkRBQ3JDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBVWYsOERBQUM5RywrQ0FBSUEsQ0FBQzhJLEtBQUs7d0JBQUNqQyxPQUFNO3dCQUFTa0MsSUFBRztrQ0FDNUIsNEVBQUM5SSxpREFBS0E7NEJBQUMrSSxHQUFFOzRCQUFLQyxVQUFVOzs4Q0FDdEIsOERBQUNDO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQUlaLFdBQVU7c0RBQ2IsNEVBQUNsSSxxREFBU0E7Z0RBQ1JxSixhQUFZO2dEQUNaWiwyQkFBYSw4REFBQ3pILCtMQUFVQTtvREFBQ29ILE1BQU07Ozs7OztnREFDL0IzQixPQUFPVDtnREFDUHFDLFVBQVUsQ0FBQ2MsSUFBTWxELGNBQWNrRCxFQUFFQyxNQUFNLENBQUMzQyxLQUFLO2dEQUM3Q3lCLFdBQVU7Ozs7Ozs7Ozs7O3NEQUdkLDhEQUFDL0gsa0RBQU1BOzRDQUNMc0ksMkJBQWEsOERBQUM1SCwrTEFBUUE7Z0RBQUN1SCxNQUFNOzs7Ozs7NENBQzdCdUIsU0FBUyxJQUFNNUM7NENBQ2ZtQixXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7OENBS0gsOERBQUN4SCxzREFBVUE7OENBQ1QsNEVBQUNMLGlEQUFLQTt3Q0FBQ3VKLE9BQU87d0NBQUNDLGdCQUFnQjt3Q0FBQ0MsZUFBZTs7MERBQzdDLDhEQUFDekosaURBQUtBLENBQUMwSixLQUFLOzBEQUNWLDRFQUFDMUosaURBQUtBLENBQUMySixFQUFFOztzRUFDUCw4REFBQzNKLGlEQUFLQSxDQUFDNEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDNUosaURBQUtBLENBQUM0SixFQUFFO3NFQUFDOzs7Ozs7c0VBQ1YsOERBQUM1SixpREFBS0EsQ0FBQzRKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzVKLGlEQUFLQSxDQUFDNEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDNUosaURBQUtBLENBQUM0SixFQUFFO3NFQUFDOzs7Ozs7c0VBQ1YsOERBQUM1SixpREFBS0EsQ0FBQzRKLEVBQUU7NERBQUNDLE9BQU87Z0VBQUVDLE9BQU87NERBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUdsQyw4REFBQzlKLGlEQUFLQSxDQUFDK0osS0FBSzswREFDVGxFLGVBQWVzQixHQUFHLENBQUMsQ0FBQ3BCLHNCQUNuQiw4REFBQy9GLGlEQUFLQSxDQUFDMkosRUFBRTs7MEVBQ1AsOERBQUMzSixpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUN0SyxnREFBSUE7b0VBQUNrSixJQUFJOzhFQUFNN0MsTUFBTXhELElBQUk7Ozs7Ozs7Ozs7OzBFQUU1Qiw4REFBQ3ZDLGlEQUFLQSxDQUFDZ0ssRUFBRTswRUFBRWpFLE1BQU12RCxXQUFXOzs7Ozs7MEVBQzVCLDhEQUFDeEMsaURBQUtBLENBQUNnSyxFQUFFOzBFQUFFakUsTUFBTXRELFFBQVE7Ozs7OzswRUFDekIsOERBQUN6QyxpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUN2QjtvRUFBSVosV0FBVTs7c0ZBQ2IsOERBQUN0SCx1REFBV0E7NEVBQUNtQyxPQUFPcUQsTUFBTXJELEtBQUs7NEVBQUVxRixNQUFNOzs7Ozs7c0ZBQ3ZDLDhEQUFDckksZ0RBQUlBOzRFQUFDcUksTUFBSztzRkFBTWhDLE1BQU1yRCxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHaEMsOERBQUMxQyxpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUN2QjtvRUFBSVosV0FBVTs7c0ZBQ2IsOERBQUN0SCx1REFBV0E7NEVBQUNtQyxPQUFPcUQsTUFBTXBELFVBQVU7NEVBQUVvRixNQUFNOzs7Ozs7c0ZBQzVDLDhEQUFDckksZ0RBQUlBOzRFQUFDcUksTUFBSztzRkFBTWhDLE1BQU1wRCxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHckMsOERBQUMzQyxpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUM5SixpREFBS0E7b0VBQUMwSCxLQUFJO29FQUFLcUMsU0FBUTs7c0ZBQ3RCLDhEQUFDN0osbURBQU9BOzRFQUFDaUcsT0FBTTtzRkFDYiw0RUFBQ3BHLHNEQUFVQTtnRkFDVGdJLFNBQVE7Z0ZBQ1J2RixPQUFNO2dGQUNONEcsU0FBUyxJQUFNNUMsZUFBZVg7MEZBRTlCLDRFQUFDdEYsK0xBQVFBO29GQUFDc0gsTUFBTTs7Ozs7Ozs7Ozs7Ozs7OztzRkFHcEIsOERBQUMzSCxtREFBT0E7NEVBQUNpRyxPQUFNO3NGQUNiLDRFQUFDcEcsc0RBQVVBO2dGQUNUZ0ksU0FBUTtnRkFDUnZGLE9BQU07Z0ZBQ040RyxTQUFTLElBQU0zQixrQkFBa0I1QixNQUFNZCxFQUFFOzBGQUV6Qyw0RUFBQ3ZFLCtMQUFTQTtvRkFBQ3FILE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dURBbkNaaEMsTUFBTWQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBaURuQyw4REFBQzFGLCtDQUFJQSxDQUFDOEksS0FBSzt3QkFBQ2pDLE9BQU07d0JBQWdCa0MsSUFBRztrQ0FDbkMsNEVBQUM5SSxpREFBS0E7NEJBQUMrSSxHQUFFOzRCQUFLQyxVQUFVOzs4Q0FDdEIsOERBQUNDO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQUlaLFdBQVU7OzhEQUNiLDhEQUFDakksa0RBQU1BO29EQUNMb0osYUFBWTtvREFDWkUsTUFBTTlELFNBQVMrQixHQUFHLENBQUMrQyxDQUFBQSxJQUFNOzREQUFFOUQsT0FBTzhELEVBQUVqRixFQUFFLENBQUNrRixRQUFROzREQUFJOUQsT0FBTzZELEVBQUVqSCxJQUFJO3dEQUFDO29EQUNqRTRHLE9BQU87d0RBQUVDLE9BQU87b0RBQUk7Ozs7Ozs4REFFdEIsOERBQUNyQjtvREFBSVosV0FBVTs7c0VBQ2IsOERBQUNuSSxnREFBSUE7NERBQUNxSSxNQUFLOzREQUFLYSxJQUFJO3NFQUFLOzs7Ozs7c0VBQ3pCLDhEQUFDckksdURBQVdBOzREQUFDbUMsT0FBTTs0REFBVXFGLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFHdkMsOERBQUNqSSxrREFBTUE7NENBQ0xtSSxTQUFROzRDQUNSdkYsT0FBTTtzREFDUDs7Ozs7Ozs7Ozs7OzhDQUtILDhEQUFDckMsc0RBQVVBOzhDQUNULDRFQUFDTCxpREFBS0E7d0NBQUN1SixPQUFPO3dDQUFDQyxnQkFBZ0I7d0NBQUNDLGVBQWU7OzBEQUM3Qyw4REFBQ3pKLGlEQUFLQSxDQUFDMEosS0FBSzswREFDViw0RUFBQzFKLGlEQUFLQSxDQUFDMkosRUFBRTs7c0VBQ1AsOERBQUMzSixpREFBS0EsQ0FBQzRKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzVKLGlEQUFLQSxDQUFDNEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDNUosaURBQUtBLENBQUM0SixFQUFFOzREQUFDQyxPQUFPO2dFQUFFQyxPQUFPOzREQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzswREFHbEMsOERBQUM5SixpREFBS0EsQ0FBQytKLEtBQUs7MERBQ1QzRSxTQUFTK0IsR0FBRyxDQUFDLENBQUNpRCx3QkFDYiw4REFBQ3BLLGlEQUFLQSxDQUFDMkosRUFBRTs7MEVBQ1AsOERBQUMzSixpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUN0SyxnREFBSUE7b0VBQUNrSixJQUFJOzhFQUFNd0IsUUFBUW5ILElBQUk7Ozs7Ozs7Ozs7OzBFQUU5Qiw4REFBQ2pELGlEQUFLQSxDQUFDZ0ssRUFBRTswRUFDUCw0RUFBQ3ZCO29FQUFJWixXQUFVOztzRkFDYiw4REFBQ3RILHVEQUFXQTs0RUFBQ21DLE9BQU8wSCxRQUFRMUgsS0FBSzs0RUFBRXFGLE1BQU07Ozs7OztzRkFDekMsOERBQUNySSxnREFBSUE7NEVBQUNxSSxNQUFLO3NGQUFNcUMsUUFBUTFILEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUdsQyw4REFBQzFDLGlEQUFLQSxDQUFDZ0ssRUFBRTswRUFDUCw0RUFBQzlKLGlEQUFLQTtvRUFBQzBILEtBQUk7b0VBQUtxQyxTQUFROzhFQUN0Qiw0RUFBQzdKLG1EQUFPQTt3RUFBQ2lHLE9BQU07a0ZBQ2IsNEVBQUNwRyxzREFBVUE7NEVBQ1RnSSxTQUFROzRFQUNSdkYsT0FBTTtzRkFFTiw0RUFBQzFCLCtMQUFLQTtnRkFBQytHLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1REFqQlJxQyxRQUFRbkYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBK0JyQyw4REFBQzFGLCtDQUFJQSxDQUFDOEksS0FBSzt3QkFBQ2pDLE9BQU07d0JBQVVrQyxJQUFHO2tDQUM3Qiw0RUFBQzlJLGlEQUFLQTs0QkFBQytJLEdBQUU7NEJBQUtDLFVBQVU7OzhDQUN0Qiw4REFBQ0M7b0NBQUlaLFdBQVU7O3NEQUNiLDhEQUFDdkksZ0RBQUtBOzRDQUFDd0ksT0FBTztzREFBRzs7Ozs7O3NEQUNqQiw4REFBQ2hJLGtEQUFNQTs0Q0FDTHNJLDJCQUFhLDhEQUFDNUgsK0xBQVFBO2dEQUFDdUgsTUFBTTs7Ozs7OzRDQUM3QnVCLFNBQVMsSUFBTTNDOzRDQUNma0IsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7OzhDQUtILDhEQUFDeEgsc0RBQVVBOzhDQUNULDRFQUFDTCxpREFBS0E7d0NBQUN1SixPQUFPO3dDQUFDQyxnQkFBZ0I7d0NBQUNDLGVBQWU7OzBEQUM3Qyw4REFBQ3pKLGlEQUFLQSxDQUFDMEosS0FBSzswREFDViw0RUFBQzFKLGlEQUFLQSxDQUFDMkosRUFBRTs7c0VBQ1AsOERBQUMzSixpREFBS0EsQ0FBQzRKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzVKLGlEQUFLQSxDQUFDNEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDNUosaURBQUtBLENBQUM0SixFQUFFO3NFQUFDOzs7Ozs7c0VBQ1YsOERBQUM1SixpREFBS0EsQ0FBQzRKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzVKLGlEQUFLQSxDQUFDNEosRUFBRTs0REFBQ0MsT0FBTztnRUFBRUMsT0FBTzs0REFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR2xDLDhEQUFDOUosaURBQUtBLENBQUMrSixLQUFLOzBEQUNUN0UsUUFBUW1GLE1BQU0sS0FBSyxrQkFDbEIsOERBQUNySyxpREFBS0EsQ0FBQzJKLEVBQUU7OERBQ1AsNEVBQUMzSixpREFBS0EsQ0FBQ2dLLEVBQUU7d0RBQUNNLFNBQVM7d0RBQUd6QyxXQUFVO2tFQUFpQzs7Ozs7Ozs7OztnRUFLbkUzQyxRQUFRaUMsR0FBRyxDQUFDLENBQUNQLHVCQUNYLDhEQUFDNUcsaURBQUtBLENBQUMySixFQUFFOzswRUFDUCw4REFBQzNKLGlEQUFLQSxDQUFDZ0ssRUFBRTswRUFDUCw0RUFBQ3RLLGdEQUFJQTtvRUFBQ2tKLElBQUk7OEVBQU1oQyxPQUFPM0QsSUFBSTs7Ozs7Ozs7Ozs7MEVBRTdCLDhEQUFDakQsaURBQUtBLENBQUNnSyxFQUFFOzBFQUFFcEQsT0FBT3BFLFdBQVc7Ozs7OzswRUFDN0IsOERBQUN4QyxpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUN2QjtvRUFBSVosV0FBVTs7c0ZBQ2IsOERBQUN0SCx1REFBV0E7NEVBQUNtQyxPQUFPa0UsT0FBT2xFLEtBQUs7NEVBQUVxRixNQUFNOzs7Ozs7c0ZBQ3hDLDhEQUFDckksZ0RBQUlBOzRFQUFDcUksTUFBSztzRkFBTW5CLE9BQU9sRSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzswRUFHakMsOERBQUMxQyxpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUMxSixpREFBS0E7b0VBQUNvQyxPQUFPa0UsT0FBTzFELFVBQVUsR0FBRyxVQUFVOzhFQUN6QzBELE9BQU8xRCxVQUFVLEdBQUcsUUFBUTs7Ozs7Ozs7Ozs7MEVBR2pDLDhEQUFDbEQsaURBQUtBLENBQUNnSyxFQUFFOzBFQUNQLDRFQUFDOUosaURBQUtBO29FQUFDMEgsS0FBSTtvRUFBS3FDLFNBQVE7OEVBQ3RCLDRFQUFDN0osbURBQU9BO3dFQUFDaUcsT0FBTTtrRkFDYiw0RUFBQ3BHLHNEQUFVQTs0RUFDVGdJLFNBQVE7NEVBQ1J2RixPQUFNOzRFQUNONEcsU0FBUyxJQUFNM0MsZ0JBQWdCQztzRkFFL0IsNEVBQUNuRywrTEFBUUE7Z0ZBQUNzSCxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dURBeEJYbkIsT0FBTzNCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQXVDdEMsOERBQUMxRiwrQ0FBSUEsQ0FBQzhJLEtBQUs7d0JBQUNqQyxPQUFNO3dCQUFTa0MsSUFBRztrQ0FDNUIsNEVBQUM5SSxpREFBS0E7NEJBQUMrSSxHQUFFOzRCQUFLQyxVQUFVOzs4Q0FDdEIsOERBQUNDO29DQUFJWixXQUFVOztzREFDYiw4REFBQ3ZJLGdEQUFLQTs0Q0FBQ3dJLE9BQU87c0RBQUc7Ozs7OztzREFDakIsOERBQUNoSSxrREFBTUE7NENBQ0xzSSwyQkFBYSw4REFBQzVILCtMQUFRQTtnREFBQ3VILE1BQU07Ozs7Ozs0Q0FDN0J1QixTQUFTLElBQU16Qzs0Q0FDZmdCLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs4Q0FLSCw4REFBQ3hILHNEQUFVQTs4Q0FDVCw0RUFBQ0wsaURBQUtBO3dDQUFDdUosT0FBTzt3Q0FBQ0MsZ0JBQWdCO3dDQUFDQyxlQUFlOzswREFDN0MsOERBQUN6SixpREFBS0EsQ0FBQzBKLEtBQUs7MERBQ1YsNEVBQUMxSixpREFBS0EsQ0FBQzJKLEVBQUU7O3NFQUNQLDhEQUFDM0osaURBQUtBLENBQUM0SixFQUFFO3NFQUFDOzs7Ozs7c0VBQ1YsOERBQUM1SixpREFBS0EsQ0FBQzRKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzVKLGlEQUFLQSxDQUFDNEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDNUosaURBQUtBLENBQUM0SixFQUFFO3NFQUFDOzs7Ozs7c0VBQ1YsOERBQUM1SixpREFBS0EsQ0FBQzRKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzVKLGlEQUFLQSxDQUFDNEosRUFBRTs0REFBQ0MsT0FBTztnRUFBRUMsT0FBTzs0REFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR2xDLDhEQUFDOUosaURBQUtBLENBQUMrSixLQUFLOzBEQUNUekUsT0FBTzZCLEdBQUcsQ0FBQyxDQUFDTCxzQkFDWCw4REFBQzlHLGlEQUFLQSxDQUFDMkosRUFBRTs7MEVBQ1AsOERBQUMzSixpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUN0SyxnREFBSUE7b0VBQUNrSixJQUFJOzhFQUFNOUIsTUFBTTdELElBQUk7Ozs7Ozs7Ozs7OzBFQUU1Qiw4REFBQ2pELGlEQUFLQSxDQUFDZ0ssRUFBRTswRUFBRWxELE1BQU10RSxXQUFXOzs7Ozs7MEVBQzVCLDhEQUFDeEMsaURBQUtBLENBQUNnSyxFQUFFOzBFQUNQLDRFQUFDdkI7b0VBQUlaLFdBQVU7O3NGQUNiLDhEQUFDdEgsdURBQVdBOzRFQUFDbUMsT0FBT29FLE1BQU1wRSxLQUFLOzRFQUFFcUYsTUFBTTs7Ozs7O3NGQUN2Qyw4REFBQ3JJLGdEQUFJQTs0RUFBQ3FJLE1BQUs7c0ZBQU1qQixNQUFNcEUsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBR2hDLDhEQUFDMUMsaURBQUtBLENBQUNnSyxFQUFFOzBFQUNQLDRFQUFDMUosaURBQUtBO29FQUNKb0MsT0FBT29FLE1BQU16RCxJQUFJLEtBQUssWUFBWSxTQUFTO29FQUMzQzRFLFNBQVE7OEVBRVBuQixNQUFNekQsSUFBSSxLQUFLLFlBQVkscUJBQXFCOzs7Ozs7Ozs7OzswRUFHckQsOERBQUNyRCxpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUN0SyxnREFBSUE7OEVBQUVvSCxNQUFNeEQsUUFBUTs7Ozs7Ozs7Ozs7MEVBRXZCLDhEQUFDdEQsaURBQUtBLENBQUNnSyxFQUFFOzBFQUNQLDRFQUFDOUosaURBQUtBO29FQUFDMEgsS0FBSTtvRUFBS3FDLFNBQVE7O3NGQUN0Qiw4REFBQzdKLG1EQUFPQTs0RUFBQ2lHLE9BQU07c0ZBQ2IsNEVBQUNwRyxzREFBVUE7Z0ZBQ1RnSSxTQUFRO2dGQUNSdkYsT0FBTTtnRkFDTjRHLFNBQVMsSUFBTXpDLGVBQWVDOzBGQUU5Qiw0RUFBQ3JHLCtMQUFRQTtvRkFBQ3NILE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBR3BCLDhEQUFDM0gsbURBQU9BOzRFQUFDaUcsT0FBTTtzRkFDYiw0RUFBQ3BHLHNEQUFVQTtnRkFDVGdJLFNBQVE7Z0ZBQ1J2RixPQUFNOzBGQUVOLDRFQUFDaEMsK0xBQVNBO29GQUFDcUgsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1REF0Q1pqQixNQUFNN0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBb0RuQyw4REFBQzFGLCtDQUFJQSxDQUFDOEksS0FBSzt3QkFBQ2pDLE9BQU07d0JBQWFrQyxJQUFHO2tDQUNoQyw0RUFBQzlJLGlEQUFLQTs0QkFBQytJLEdBQUU7NEJBQUtDLFVBQVU7OzhDQUN0Qiw4REFBQ0M7b0NBQUlaLFdBQVU7O3NEQUNiLDhEQUFDdkksZ0RBQUtBOzRDQUFDd0ksT0FBTztzREFBRzs7Ozs7O3NEQUNqQiw4REFBQ2hJLGtEQUFNQTs0Q0FDTHNJLDJCQUFhLDhEQUFDNUgsK0xBQVFBO2dEQUFDdUgsTUFBTTs7Ozs7OzRDQUM3QnVCLFNBQVMsSUFBTXZDOzRDQUNmYyxXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7OENBS0gsOERBQUN4SCxzREFBVUE7OENBQ1QsNEVBQUNMLGlEQUFLQTt3Q0FBQ3VKLE9BQU87d0NBQUNDLGdCQUFnQjt3Q0FBQ0MsZUFBZTs7MERBQzdDLDhEQUFDekosaURBQUtBLENBQUMwSixLQUFLOzBEQUNWLDRFQUFDMUosaURBQUtBLENBQUMySixFQUFFOztzRUFDUCw4REFBQzNKLGlEQUFLQSxDQUFDNEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDNUosaURBQUtBLENBQUM0SixFQUFFO3NFQUFDOzs7Ozs7c0VBQ1YsOERBQUM1SixpREFBS0EsQ0FBQzRKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzVKLGlEQUFLQSxDQUFDNEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDNUosaURBQUtBLENBQUM0SixFQUFFO3NFQUFDOzs7Ozs7c0VBQ1YsOERBQUM1SixpREFBS0EsQ0FBQzRKLEVBQUU7c0VBQUM7Ozs7OztzRUFDViw4REFBQzVKLGlEQUFLQSxDQUFDNEosRUFBRTtzRUFBQzs7Ozs7O3NFQUNWLDhEQUFDNUosaURBQUtBLENBQUM0SixFQUFFOzREQUFDQyxPQUFPO2dFQUFFQyxPQUFPOzREQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzswREFHbEMsOERBQUM5SixpREFBS0EsQ0FBQytKLEtBQUs7MERBQ1R2RSxXQUFXMkIsR0FBRyxDQUFDLENBQUNILDBCQUNmLDhEQUFDaEgsaURBQUtBLENBQUMySixFQUFFOzswRUFDUCw4REFBQzNKLGlEQUFLQSxDQUFDZ0ssRUFBRTswRUFDUCw0RUFBQ3RLLGdEQUFJQTtvRUFBQ2tKLElBQUk7OEVBQU01QixVQUFVdkQsS0FBSzs7Ozs7Ozs7Ozs7MEVBRWpDLDhEQUFDekQsaURBQUtBLENBQUNnSyxFQUFFOzBFQUFFaEQsVUFBVXRCLFFBQVE7Ozs7OzswRUFDN0IsOERBQUMxRixpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQ1AsNEVBQUN2QjtvRUFBSVosV0FBVTs4RUFDYiw0RUFBQ3RILHVEQUFXQTt3RUFDVm1DLE9BQU9zRSxVQUFVcEQsWUFBWSxHQUFHLFlBQVk7d0VBQzVDbUUsTUFBTTs7Ozs7Ozs7Ozs7Ozs7OzswRUFJWiw4REFBQy9ILGlEQUFLQSxDQUFDZ0ssRUFBRTswRUFBRWhELFVBQVV0RCxTQUFTOzs7Ozs7MEVBQzlCLDhEQUFDMUQsaURBQUtBLENBQUNnSyxFQUFFOzBFQUFFaEQsVUFBVXJELE9BQU87Ozs7OzswRUFDNUIsOERBQUMzRCxpREFBS0EsQ0FBQ2dLLEVBQUU7MEVBQUM7Ozs7OzswRUFDViw4REFBQ2hLLGlEQUFLQSxDQUFDZ0ssRUFBRTswRUFBQzs7Ozs7OzBFQUNWLDhEQUFDaEssaURBQUtBLENBQUNnSyxFQUFFOzBFQUNQLDRFQUFDOUosaURBQUtBO29FQUFDMEgsS0FBSTtvRUFBS3FDLFNBQVE7O3NGQUN0Qiw4REFBQzdKLG1EQUFPQTs0RUFBQ2lHLE9BQU07c0ZBQ2IsNEVBQUNwRyxzREFBVUE7Z0ZBQ1RnSSxTQUFRO2dGQUNSdkYsT0FBTTtnRkFDTjRHLFNBQVMsSUFBTXZDLG1CQUFtQkM7MEZBRWxDLDRFQUFDdkcsK0xBQVFBO29GQUFDc0gsTUFBTTs7Ozs7Ozs7Ozs7Ozs7OztzRkFHcEIsOERBQUMzSCxtREFBT0E7NEVBQUNpRyxPQUFNO3NGQUNiLDRFQUFDcEcsc0RBQVVBO2dGQUNUZ0ksU0FBUTtnRkFDUnZGLE9BQU07MEZBRU4sNEVBQUNoQywrTEFBU0E7b0ZBQUNxSCxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VEQWpDWmYsVUFBVS9CLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQWdEekMsOERBQUNsRixpREFBS0E7Z0JBQ0p3SyxRQUFRbEo7Z0JBQ1JtSixTQUFTLElBQU1sSixvQkFBb0I7Z0JBQ25DbUMscUJBQ0UsOERBQUNnRjtvQkFBSVosV0FBVTs7c0NBQ2IsOERBQUNqSCw4TEFBWUE7NEJBQUNtSCxNQUFNOzs7Ozs7c0NBQ3BCLDhEQUFDckksZ0RBQUlBOzRCQUFDa0osSUFBSTtzQ0FBSzs7Ozs7Ozs7Ozs7O2dCQUduQmIsTUFBSztnQkFDTDBDLGlCQUFpQjswQkFFakIsNEVBQUNwTCxnREFBS0E7b0JBQUN1SSxLQUFJOztzQ0FDVCw4REFBQ25JLGdEQUFJQTs7OENBQ0gsOERBQUNBLGdEQUFJQSxDQUFDaUosR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQ2hKLHFEQUFTQTt3Q0FDUjBHLHFCQUFPLDhEQUFDc0M7O2dEQUFLOzhEQUFLLDhEQUFDQTtvREFBS2QsV0FBVTs4REFBZTs7Ozs7Ozs7Ozs7O3dDQUNqRHpCLE9BQU8vRCxVQUFVRSxJQUFJO3dDQUNyQnlGLFVBQVUsQ0FBQ2MsSUFBTXhHLGFBQWE0RSxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUUzRSxNQUFNdUcsRUFBRUMsTUFBTSxDQUFDM0MsS0FBSztnREFBQzt3Q0FDdkVzRSxRQUFROzs7Ozs7Ozs7Ozs4Q0FHWiw4REFBQ2pMLGdEQUFJQSxDQUFDaUosR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQ2hKLHFEQUFTQTt3Q0FDUjBHLHFCQUFPLDhEQUFDc0M7O2dEQUFLOzhEQUFZLDhEQUFDQTtvREFBS2QsV0FBVTs4REFBZTs7Ozs7Ozs7Ozs7O3dDQUN4RHpCLE9BQU8vRCxVQUFVRyxXQUFXO3dDQUM1QndGLFVBQVUsQ0FBQ2MsSUFBTXhHLGFBQWE0RSxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUUxRSxhQUFhc0csRUFBRUMsTUFBTSxDQUFDM0MsS0FBSztnREFBQzt3Q0FDOUVzRSxRQUFROzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLZCw4REFBQ2pMLGdEQUFJQTs7OENBQ0gsOERBQUNBLGdEQUFJQSxDQUFDaUosR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQ2hKLHFEQUFTQTt3Q0FDUjBHLHFCQUFPLDhEQUFDc0M7O2dEQUFLOzhEQUFZLDhEQUFDQTtvREFBS2QsV0FBVTs4REFBZTs7Ozs7Ozs7Ozs7O3dDQUN4RHpCLE9BQU8vRCxVQUFVSSxRQUFRLENBQUMwSCxRQUFRO3dDQUNsQ25DLFVBQVUsQ0FBQ2MsSUFBTXhHLGFBQWE0RSxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUV6RSxVQUFVa0ksU0FBUzdCLEVBQUVDLE1BQU0sQ0FBQzNDLEtBQUssS0FBSztnREFBRzt3Q0FDM0YvQyxNQUFLO3dDQUNMcUgsUUFBUTs7Ozs7Ozs7Ozs7OENBR1osOERBQUNqTCxnREFBSUEsQ0FBQ2lKLEdBQUc7b0NBQUNDLE1BQU07OENBQ2QsNEVBQUNGOzswREFDQyw4REFBQy9JLGdEQUFJQTtnREFBQ3FJLE1BQUs7Z0RBQUthLElBQUk7Z0RBQUtDLElBQUc7MERBQUs7Ozs7OzswREFDakMsOERBQUNKO2dEQUFJWixXQUFVOzBEQUNiLDRFQUFDdEgsdURBQVdBO29EQUNWbUMsT0FBT0wsVUFBVUssS0FBSztvREFDdEJxRixNQUFNO29EQUNORixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtsQiw4REFBQ3BJLGdEQUFJQSxDQUFDaUosR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQ0Y7OzBEQUNDLDhEQUFDL0ksZ0RBQUlBO2dEQUFDcUksTUFBSztnREFBS2EsSUFBSTtnREFBS0MsSUFBRzswREFBSzs7Ozs7OzBEQUNqQyw4REFBQ0o7Z0RBQUlaLFdBQVU7MERBQ2IsNEVBQUN0SCx1REFBV0E7b0RBQ1ZtQyxPQUFPTCxVQUFVTSxVQUFVO29EQUMzQm9GLE1BQU07b0RBQ05GLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3BCLDhEQUFDcEksZ0RBQUlBOzs4Q0FDSCw4REFBQ0EsZ0RBQUlBLENBQUNpSixHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDL0ksa0RBQU1BO3dDQUNMeUcsT0FBTTt3Q0FDTkQsT0FBTy9ELFVBQVVPLFlBQVk7d0NBQzdCb0YsVUFBVSxDQUFDNUIsUUFBVTlELGFBQWE0RSxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUV0RSxjQUFjd0QsU0FBUztnREFBRzt3Q0FDaEY4QyxNQUFNMUM7d0NBQ05vRSw0QkFBYyw4REFBQ3BLLCtMQUFRQTs0Q0FBQ3VILE1BQU07NENBQUlGLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR2hELDhEQUFDcEksZ0RBQUlBLENBQUNpSixHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDL0ksa0RBQU1BO3dDQUNMeUcsT0FBTTt3Q0FDTkQsT0FBTy9ELFVBQVVRLGdCQUFnQjt3Q0FDakNtRixVQUFVLENBQUM1QixRQUFVOUQsYUFBYTRFLENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRXJFLGtCQUFrQnVELFNBQVM7Z0RBQUc7d0NBQ3BGOEMsTUFBTXpDOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLWiw4REFBQ2dDOzRCQUFJWixXQUFVOzs4Q0FDYiw4REFBQ2hJLGtEQUFNQTtvQ0FDTHVKLFNBQVMvRyxVQUFVUyxhQUFhO29DQUNoQ2tGLFVBQVUsQ0FBQ2MsSUFBTXhHLGFBQWE0RSxDQUFBQSxPQUFTO2dEQUFFLEdBQUdBLElBQUk7Z0RBQUVwRSxlQUFlZ0csRUFBRU8sYUFBYSxDQUFDRCxPQUFPOzRDQUFDO29DQUN6Ri9DLE9BQU07b0NBQ053RSxlQUFjOzs7Ozs7OENBR2hCLDhEQUFDM0ssaURBQUtBOztzREFDSiw4REFBQ0osa0RBQU1BOzRDQUNMbUksU0FBUTs0Q0FDUnZGLE9BQU07NENBQ040RyxTQUFTLElBQU1oSSxvQkFBb0I7c0RBQ3BDOzs7Ozs7c0RBR0QsOERBQUN4QixrREFBTUE7NENBQ0xtSSxTQUFROzRDQUNSdkYsT0FBTTs0Q0FDTjRHLFNBQVNyQzs0Q0FDVDZELFVBQVUsQ0FBQ3pJLFVBQVVFLElBQUksSUFBSSxDQUFDRixVQUFVRyxXQUFXO3NEQUNwRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU1QsOERBQUN6QyxpREFBS0E7Z0JBQ0p3SyxRQUFRaEo7Z0JBQ1JpSixTQUFTLElBQU1oSixxQkFBcUI7Z0JBQ3BDaUMscUJBQ0UsOERBQUNnRjtvQkFBSVosV0FBVTs7c0NBQ2IsOERBQUNqSCw4TEFBWUE7NEJBQUNtSCxNQUFNOzs7Ozs7c0NBQ3BCLDhEQUFDckksZ0RBQUlBOzRCQUFDa0osSUFBSTtzQ0FBSzs7Ozs7Ozs7Ozs7O2dCQUduQmIsTUFBSztnQkFDTDBDLGlCQUFpQjswQkFFakIsNEVBQUNwTCxnREFBS0E7b0JBQUN1SSxLQUFJOztzQ0FDVCw4REFBQ25JLGdEQUFJQTs7OENBQ0gsOERBQUNBLGdEQUFJQSxDQUFDaUosR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQ2hKLHFEQUFTQTt3Q0FDUjBHLHFCQUFPLDhEQUFDc0M7O2dEQUFLOzhEQUFJLDhEQUFDQTtvREFBS2QsV0FBVTs4REFBZTs7Ozs7Ozs7Ozs7O3dDQUNoRHpCLE9BQU9yRCxXQUFXRSxJQUFJO3dDQUN0QitFLFVBQVUsQ0FBQ2MsSUFBTTlGLGNBQWNrRSxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUVqRSxNQUFNNkYsRUFBRUMsTUFBTSxDQUFDM0MsS0FBSztnREFBQzt3Q0FDeEVzRSxRQUFROzs7Ozs7Ozs7Ozs4Q0FHWiw4REFBQ2pMLGdEQUFJQSxDQUFDaUosR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQ0Y7OzBEQUNDLDhEQUFDL0ksZ0RBQUlBO2dEQUFDcUksTUFBSztnREFBS2EsSUFBSTtnREFBS0MsSUFBRzswREFBSzs7Ozs7OzBEQUNqQyw4REFBQ0o7Z0RBQUlaLFdBQVU7MERBQ2IsNEVBQUN0SCx1REFBV0E7b0RBQ1ZtQyxPQUFPSyxXQUFXTCxLQUFLO29EQUN2QnFGLE1BQU07b0RBQ05GLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3BCLDhEQUFDcEksZ0RBQUlBOzs4Q0FDSCw4REFBQ0EsZ0RBQUlBLENBQUNpSixHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDaEoscURBQVNBO3dDQUNSMEcsT0FBTTt3Q0FDTkQsT0FBT3JELFdBQVdQLFdBQVc7d0NBQzdCd0YsVUFBVSxDQUFDYyxJQUFNOUYsY0FBY2tFLENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRTFFLGFBQWFzRyxFQUFFQyxNQUFNLENBQUMzQyxLQUFLO2dEQUFDOzs7Ozs7Ozs7Ozs4Q0FHbkYsOERBQUMzRyxnREFBSUEsQ0FBQ2lKLEdBQUc7b0NBQUNDLE1BQU07OENBQ2QsNEVBQUMvSSxrREFBTUE7d0NBQ0x5RyxPQUFNO3dDQUNORCxPQUFPckQsV0FBV0YsZ0JBQWdCO3dDQUNsQ21GLFVBQVUsQ0FBQzVCLFFBQVVwRCxjQUFja0UsQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFckUsa0JBQWtCdUQsU0FBUztnREFBRzt3Q0FDckY4QyxNQUFNekM7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtaLDhEQUFDZ0M7NEJBQUlaLFdBQVU7OzhDQUNiLDhEQUFDaEksa0RBQU1BO29DQUNMdUosU0FBU3JHLFdBQVdHLFVBQVU7b0NBQzlCOEUsVUFBVSxDQUFDYyxJQUFNOUYsY0FBY2tFLENBQUFBLE9BQVM7Z0RBQUUsR0FBR0EsSUFBSTtnREFBRWhFLFlBQVk0RixFQUFFTyxhQUFhLENBQUNELE9BQU87NENBQUM7b0NBQ3ZGL0MsT0FBTTtvQ0FDTndFLGVBQWM7Ozs7Ozs4Q0FHaEIsOERBQUMzSyxpREFBS0E7O3NEQUNKLDhEQUFDSixrREFBTUE7NENBQ0xtSSxTQUFROzRDQUNSdkYsT0FBTTs0Q0FDTjRHLFNBQVMsSUFBTTlILHFCQUFxQjtzREFDckM7Ozs7OztzREFHRCw4REFBQzFCLGtEQUFNQTs0Q0FDTG1JLFNBQVE7NENBQ1J2RixPQUFNOzRDQUNONEcsU0FBUzlCOzRDQUNUc0QsVUFBVSxDQUFDL0gsV0FBV0UsSUFBSTtzREFDM0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNULDhEQUFDbEQsaURBQUtBO2dCQUNKd0ssUUFBUTlJO2dCQUNSK0ksU0FBUyxJQUFNOUksb0JBQW9CO2dCQUNuQytCLHFCQUNFLDhEQUFDZ0Y7b0JBQUlaLFdBQVU7O3NDQUNiLDhEQUFDNUcsOExBQVlBOzRCQUFDOEcsTUFBTTs7Ozs7O3NDQUNwQiw4REFBQ3JJLGdEQUFJQTs0QkFBQ2tKLElBQUk7c0NBQUs7Ozs7Ozs7Ozs7OztnQkFHbkJiLE1BQUs7Z0JBQ0wwQyxpQkFBaUI7MEJBRWpCLDRFQUFDcEwsZ0RBQUtBO29CQUFDdUksS0FBSTs7c0NBQ1QsOERBQUNuSSxnREFBSUE7OzhDQUNILDhEQUFDQSxnREFBSUEsQ0FBQ2lKLEdBQUc7b0NBQUNDLE1BQU07OENBQ2QsNEVBQUNoSixxREFBU0E7d0NBQ1IwRyxxQkFBTyw4REFBQ3NDOztnREFBSzs4REFBSSw4REFBQ0E7b0RBQUtkLFdBQVU7OERBQWU7Ozs7Ozs7Ozs7Ozt3Q0FDaER6QixPQUFPakQsVUFBVUYsSUFBSTt3Q0FDckIrRSxVQUFVLENBQUNjLElBQU0xRixhQUFhOEQsQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFakUsTUFBTTZGLEVBQUVDLE1BQU0sQ0FBQzNDLEtBQUs7Z0RBQUM7d0NBQ3ZFc0UsUUFBUTs7Ozs7Ozs7Ozs7OENBR1osOERBQUNqTCxnREFBSUEsQ0FBQ2lKLEdBQUc7b0NBQUNDLE1BQU07OENBQ2QsNEVBQUNoSixxREFBU0E7d0NBQ1IwRyxxQkFBTyw4REFBQ3NDOztnREFBSzs4REFBUyw4REFBQ0E7b0RBQUtkLFdBQVU7OERBQWU7Ozs7Ozs7Ozs7Ozt3Q0FDckR6QixPQUFPakQsVUFBVUcsUUFBUSxDQUFDNkcsUUFBUTt3Q0FDbENuQyxVQUFVLENBQUNjLElBQU0xRixhQUFhOEQsQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFNUQsVUFBVXFILFNBQVM3QixFQUFFQyxNQUFNLENBQUMzQyxLQUFLLEtBQUs7Z0RBQUU7d0NBQzFGL0MsTUFBSzt3Q0FDTHFILFFBQVE7Ozs7Ozs7Ozs7OzhDQUdaLDhEQUFDakwsZ0RBQUlBLENBQUNpSixHQUFHO29DQUFDQyxNQUFNOzhDQUNkLDRFQUFDRjs7MERBQ0MsOERBQUMvSSxnREFBSUE7Z0RBQUNxSSxNQUFLO2dEQUFLYSxJQUFJO2dEQUFLQyxJQUFHOzBEQUFLOzs7Ozs7MERBQ2pDLDhEQUFDSjtnREFBSVosV0FBVTswREFDYiw0RUFBQ3RILHVEQUFXQTtvREFDVm1DLE9BQU9TLFVBQVVULEtBQUs7b0RBQ3RCcUYsTUFBTTtvREFDTkYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPcEIsOERBQUNwSSxnREFBSUE7OzhDQUNILDhEQUFDQSxnREFBSUEsQ0FBQ2lKLEdBQUc7b0NBQUNDLE1BQU07OENBQ2QsNEVBQUNoSixxREFBU0E7d0NBQ1IwRyxPQUFNO3dDQUNORCxPQUFPakQsVUFBVVgsV0FBVzt3Q0FDNUJ3RixVQUFVLENBQUNjLElBQU0xRixhQUFhOEQsQ0FBQUEsT0FBUztvREFBRSxHQUFHQSxJQUFJO29EQUFFMUUsYUFBYXNHLEVBQUVDLE1BQU0sQ0FBQzNDLEtBQUs7Z0RBQUM7Ozs7Ozs7Ozs7OzhDQUdsRiw4REFBQzNHLGdEQUFJQSxDQUFDaUosR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQy9JLGtEQUFNQTt3Q0FDTHlHLE9BQU07d0NBQ05ELE9BQU9qRCxVQUFVTixnQkFBZ0I7d0NBQ2pDbUYsVUFBVSxDQUFDNUIsUUFBVWhELGFBQWE4RCxDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUVyRSxrQkFBa0J1RCxTQUFTO2dEQUFHO3dDQUNwRjhDLE1BQU16Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS1osOERBQUNnQzs7OENBQ0MsOERBQUMvSSxnREFBSUE7b0NBQUNxSSxNQUFLO29DQUFLYSxJQUFJO29DQUFLQyxJQUFHOzhDQUFLOzs7Ozs7OENBQ2pDLDhEQUFDSjtvQ0FBSVosV0FBVTs7c0RBQ2IsOERBQUNZOzRDQUFJWixXQUFVOzs4REFDYiw4REFBQ2tEO29EQUNDMUgsTUFBSztvREFDTDRCLElBQUc7b0RBQ0hoQyxNQUFLO29EQUNMbUcsU0FBU2pHLFVBQVVFLElBQUksS0FBSztvREFDNUIyRSxVQUFVLElBQU01RSxhQUFhOEQsQ0FBQUEsT0FBUztnRUFBRSxHQUFHQSxJQUFJO2dFQUFFN0QsTUFBTTs0REFBVTs7Ozs7OzhEQUVuRSw4REFBQ2dEO29EQUFNMkUsU0FBUTs4REFBZ0I7Ozs7Ozs7Ozs7OztzREFFakMsOERBQUN2Qzs0Q0FBSVosV0FBVTs7OERBQ2IsOERBQUNrRDtvREFDQzFILE1BQUs7b0RBQ0w0QixJQUFHO29EQUNIaEMsTUFBSztvREFDTG1HLFNBQVNqRyxVQUFVRSxJQUFJLEtBQUs7b0RBQzVCMkUsVUFBVSxJQUFNNUUsYUFBYThELENBQUFBLE9BQVM7Z0VBQUUsR0FBR0EsSUFBSTtnRUFBRTdELE1BQU07NERBQWU7Ozs7Ozs4REFFeEUsOERBQUNnRDtvREFBTTJFLFNBQVE7OERBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzFDLDhEQUFDdkM7NEJBQUlaLFdBQVU7c0NBQ2IsNEVBQUMzSCxpREFBS0E7O2tEQUNKLDhEQUFDSixrREFBTUE7d0NBQ0xtSSxTQUFRO3dDQUNSdkYsT0FBTTt3Q0FDTjRHLFNBQVMsSUFBTTVILG9CQUFvQjtrREFDcEM7Ozs7OztrREFHRCw4REFBQzVCLGtEQUFNQTt3Q0FDTG1JLFNBQVE7d0NBQ1J2RixPQUFNO3dDQUNOb0ksVUFBVSxDQUFDM0gsVUFBVUYsSUFBSTtrREFDMUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU1QsOERBQUNsRCxpREFBS0E7Z0JBQ0p3SyxRQUFRNUk7Z0JBQ1I2SSxTQUFTLElBQU01SSx3QkFBd0I7Z0JBQ3ZDNkIscUJBQ0UsOERBQUNnRjtvQkFBSVosV0FBVTs7c0NBQ2IsOERBQUNqSCw4TEFBWUE7NEJBQUNtSCxNQUFNOzs7Ozs7c0NBQ3BCLDhEQUFDckksZ0RBQUlBOzRCQUFDa0osSUFBSTtzQ0FBSzs7Ozs7Ozs7Ozs7O2dCQUduQmIsTUFBSztnQkFDTDBDLGlCQUFpQjswQkFFakIsNEVBQUNwTCxnREFBS0E7b0JBQUN1SSxLQUFJOztzQ0FDVCw4REFBQ2pJLHFEQUFTQTs0QkFDUjBHLHFCQUFPLDhEQUFDc0M7O29DQUFLO2tEQUFNLDhEQUFDQTt3Q0FBS2QsV0FBVTtrREFBZTs7Ozs7Ozs7Ozs7OzRCQUNsRHpCLE9BQU83QyxjQUFjRSxLQUFLOzRCQUMxQnVFLFVBQVUsQ0FBQ2MsSUFBTXRGLGlCQUFpQjBELENBQUFBLE9BQVM7d0NBQUUsR0FBR0EsSUFBSTt3Q0FBRXpELE9BQU9xRixFQUFFQyxNQUFNLENBQUMzQyxLQUFLO29DQUFDOzRCQUM1RXNFLFFBQVE7Ozs7OztzQ0FHViw4REFBQ2pMLGdEQUFJQTs7OENBQ0gsOERBQUNBLGdEQUFJQSxDQUFDaUosR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQ2hKLHFEQUFTQTt3Q0FDUjBHLHFCQUFPLDhEQUFDc0M7O2dEQUFLOzhEQUFXLDhEQUFDQTtvREFBS2QsV0FBVTs4REFBZTs7Ozs7Ozs7Ozs7O3dDQUN2RHpCLE9BQU83QyxjQUFjRyxTQUFTO3dDQUM5QnNFLFVBQVUsQ0FBQ2MsSUFBTXRGLGlCQUFpQjBELENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRXhELFdBQVdvRixFQUFFQyxNQUFNLENBQUMzQyxLQUFLO2dEQUFDO3dDQUNoRjRDLGFBQVk7d0NBQ1owQixRQUFROzs7Ozs7Ozs7Ozs4Q0FHWiw4REFBQ2pMLGdEQUFJQSxDQUFDaUosR0FBRztvQ0FBQ0MsTUFBTTs4Q0FDZCw0RUFBQ2hKLHFEQUFTQTt3Q0FDUjBHLHFCQUFPLDhEQUFDc0M7O2dEQUFLOzhEQUFTLDhEQUFDQTtvREFBS2QsV0FBVTs4REFBZTs7Ozs7Ozs7Ozs7O3dDQUNyRHpCLE9BQU83QyxjQUFjSSxPQUFPO3dDQUM1QnFFLFVBQVUsQ0FBQ2MsSUFBTXRGLGlCQUFpQjBELENBQUFBLE9BQVM7b0RBQUUsR0FBR0EsSUFBSTtvREFBRXZELFNBQVNtRixFQUFFQyxNQUFNLENBQUMzQyxLQUFLO2dEQUFDO3dDQUM5RTRDLGFBQVk7d0NBQ1owQixRQUFROzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLZCw4REFBQ2pDOzs4Q0FDQyw4REFBQy9JLGdEQUFJQTtvQ0FBQ3FJLE1BQUs7b0NBQUthLElBQUk7b0NBQUtDLElBQUc7OENBQUs7Ozs7Ozs4Q0FDakMsOERBQUNKO29DQUFJWixXQUFVOzhDQUNiLDRFQUFDdEgsdURBQVdBO3dDQUNWbUMsT0FBT2EsY0FBY2IsS0FBSzt3Q0FDMUJxRixNQUFNO3dDQUNORixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLaEIsOERBQUNZOzRCQUFJWixXQUFVOzs4Q0FDYiw4REFBQ1k7b0NBQUlaLFdBQVU7O3NEQUNiLDhEQUFDWTs0Q0FDQ1osV0FBVyx1Q0FBaUcsT0FBMUR0RSxjQUFjSyxZQUFZLEdBQUcsZUFBZTs0Q0FDOUYwRixTQUFTLElBQU05RixpQkFBaUIwRCxDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUV0RCxjQUFjLENBQUNzRCxLQUFLdEQsWUFBWTtvREFBQzs7Ozs7O3NEQUV2Riw4REFBQ2xFLGdEQUFJQTs0Q0FBQ3FJLE1BQUs7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFbEIsOERBQUNVO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQ0NaLFdBQVcsdUNBQStGLE9BQXhEdEUsY0FBY00sU0FBUyxHQUFHLGdCQUFnQjs0Q0FDNUZ5RixTQUFTLElBQU05RixpQkFBaUIwRCxDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUVyRCxXQUFXLENBQUNxRCxLQUFLckQsU0FBUztvREFBQzs7Ozs7O3NEQUVqRiw4REFBQ25FLGdEQUFJQTs0Q0FBQ3FJLE1BQUs7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFbEIsOERBQUNVO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQ0NaLFdBQVcsdUNBQWtHLE9BQTNEdEUsY0FBY08sWUFBWSxHQUFHLGdCQUFnQjs0Q0FDL0Z3RixTQUFTLElBQU05RixpQkFBaUIwRCxDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUVwRCxjQUFjLENBQUNvRCxLQUFLcEQsWUFBWTtvREFBQzs7Ozs7O3NEQUV2Riw4REFBQ3BFLGdEQUFJQTs0Q0FBQ3FJLE1BQUs7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFbEIsOERBQUNVO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQ0NaLFdBQVcsdUNBQWtHLE9BQTNEdEUsY0FBY1EsWUFBWSxHQUFHLGdCQUFnQjs0Q0FDL0Z1RixTQUFTLElBQU05RixpQkFBaUIwRCxDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUVuRCxjQUFjLENBQUNtRCxLQUFLbkQsWUFBWTtvREFBQzs7Ozs7O3NEQUV2Riw4REFBQ3JFLGdEQUFJQTs0Q0FBQ3FJLE1BQUs7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFbEIsOERBQUNVO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQ0NaLFdBQVcsdUNBQTZGLE9BQXREdEUsY0FBY1MsT0FBTyxHQUFHLGdCQUFnQjs0Q0FDMUZzRixTQUFTLElBQU05RixpQkFBaUIwRCxDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUVsRCxTQUFTLENBQUNrRCxLQUFLbEQsT0FBTztvREFBQzs7Ozs7O3NEQUU3RSw4REFBQ3RFLGdEQUFJQTs0Q0FBQ3FJLE1BQUs7c0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJcEIsOERBQUNVOzRCQUFJWixXQUFVO3NDQUNiLDRFQUFDM0gsaURBQUtBOztrREFDSiw4REFBQ0osa0RBQU1BO3dDQUNMbUksU0FBUTt3Q0FDUnZGLE9BQU07d0NBQ040RyxTQUFTLElBQU0xSCx3QkFBd0I7a0RBQ3hDOzs7Ozs7a0RBR0QsOERBQUM5QixrREFBTUE7d0NBQ0xtSSxTQUFRO3dDQUNSdkYsT0FBTTt3Q0FDTm9JLFVBQVUsQ0FBQ3ZILGNBQWNFLEtBQUs7a0RBQy9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU2Y7R0EvMENNdkM7S0FBQUE7QUFpMUNOLGlFQUFlQSxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvcnJ5XFxEZXNrdG9wXFxzdnBcXGZyb250ZW5kXFxtZWRpY2luZVxcc3JjXFxhcHBcXChkYXNoYm9hcmQpXFxzZXR0aW5nc1xccGFyYW1ldGVyc19kZV9iYXNlXFxBY2N1ZWlsRXRBZ2VuZGEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHtcclxuICBTdGFjayxcclxuICBUaXRsZSxcclxuICBUYWJzLFxyXG4gIFBhcGVyLFxyXG4gIEdyaWQsXHJcbiAgVGV4dCxcclxuICBUZXh0SW5wdXQsXHJcbiAgU2VsZWN0LFxyXG4gIFN3aXRjaCxcclxuICBCdXR0b24sXHJcbiAgTW9kYWwsXHJcbiAgVGFibGUsXHJcbiAgQWN0aW9uSWNvbixcclxuICBHcm91cCxcclxuICBDaGVja2JveCxcclxuICBUb29sdGlwLFxyXG4gIFNjcm9sbEFyZWEsXHJcbiAgQmFkZ2UsXHJcbiAgQ29sb3JTd2F0Y2gsXHJcbn0gZnJvbSAnQG1hbnRpbmUvY29yZSc7XHJcbmltcG9ydCB7XHJcbiAgSWNvblBsdXMsXHJcbiAgSWNvbkVkaXQsXHJcbiAgSWNvblRyYXNoLFxyXG4gIEljb25TZWFyY2gsXHJcbiAgSWNvbkNhbGVuZGFyLFxyXG4gIEljb25TZXR0aW5ncyxcclxuICBJY29uSG9tZSxcclxuICBJY29uQ29sb3JQaWNrZXIsXHJcbiAgSWNvblgsXHJcbiAgSWNvbkJ1aWxkaW5nLFxyXG4gIEljb25QYWxldHRlLFxyXG59IGZyb20gJ0B0YWJsZXIvaWNvbnMtcmVhY3QnO1xyXG5cclxuLy8gVHlwZXMgcG91ciBsZXMgZG9ubsOpZXNcclxuaW50ZXJmYWNlIE1vdGlmIHtcclxuICBpZDogbnVtYmVyO1xyXG4gIGNvZGU6IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGR1cmF0aW9uOiBudW1iZXI7XHJcbiAgY29sb3I6IHN0cmluZztcclxuICBjb2xvclJheWVlOiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBBZ2VuZGEge1xyXG4gIGlkOiBudW1iZXI7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbiAgY29sb3I6IHN0cmluZztcclxuICBpc1Jlc291cmNlOiBib29sZWFuO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgRG9jdGV1ciB7XHJcbiAgaWQ6IG51bWJlcjtcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgY29sb3I6IHN0cmluZztcclxufVxyXG5cclxuaW50ZXJmYWNlIFNhbGxlIHtcclxuICBpZDogbnVtYmVyO1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGNvbG9yOiBzdHJpbmc7XHJcbiAgdHlwZTogJ2F0dGVudGUnIHwgJ2NvbnN1bHRhdGlvbic7XHJcbiAgY2FwYWNpdHk6IG51bWJlcjtcclxufVxyXG5cclxuaW50ZXJmYWNlIEV2ZW5lbWVudCB7XHJcbiAgaWQ6IG51bWJlcjtcclxuICB0aXRsZTogc3RyaW5nO1xyXG4gIG1lZGVjaW5zOiBzdHJpbmc7XHJcbiAgZGF0ZURlYnV0OiBzdHJpbmc7XHJcbiAgZGF0ZUZpbjogc3RyaW5nO1xyXG4gIGNvbG9yOiBzdHJpbmc7XHJcbiAgaW5kaXNwb25pYmxlOiBib29sZWFuO1xyXG4gIHBlcm1hbmVudDogYm9vbGVhbjtcclxuICB0b3V0ZUpvdXJuZWU6IGJvb2xlYW47XHJcbiAgdG91c0xlc0pvdXJzOiBib29sZWFuO1xyXG4gIGNhYmluZXQ6IGJvb2xlYW47XHJcbn1cclxuXHJcbmNvbnN0IEFjY3VlaWxFdEFnZW5kYSA9ICgpID0+IHtcclxuICAvLyDDiXRhdHMgcG91ciBsZXMgb25nbGV0c1xyXG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZTxzdHJpbmc+KCdnZW5lcmFsJyk7XHJcblxyXG4gIC8vIMOJdGF0cyBwb3VyIGxlcyBtb2RhbGVzXHJcbiAgY29uc3QgW21vdGlmTW9kYWxPcGVuZWQsIHNldE1vdGlmTW9kYWxPcGVuZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFthZ2VuZGFNb2RhbE9wZW5lZCwgc2V0QWdlbmRhTW9kYWxPcGVuZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzYWxsZU1vZGFsT3BlbmVkLCBzZXRTYWxsZU1vZGFsT3BlbmVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbZXZlbmVtZW50TW9kYWxPcGVuZWQsIHNldEV2ZW5lbWVudE1vZGFsT3BlbmVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbZWRpdGluZ01vdGlmLCBzZXRFZGl0aW5nTW90aWZdID0gdXNlU3RhdGU8TW90aWYgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbZWRpdGluZ0FnZW5kYSwgc2V0RWRpdGluZ0FnZW5kYV0gPSB1c2VTdGF0ZTxBZ2VuZGEgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbZWRpdGluZ1NhbGxlLCBzZXRFZGl0aW5nU2FsbGVdID0gdXNlU3RhdGU8U2FsbGUgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbZWRpdGluZ0V2ZW5lbWVudCwgc2V0RWRpdGluZ0V2ZW5lbWVudF0gPSB1c2VTdGF0ZTxFdmVuZW1lbnQgfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gw4l0YXRzIHBvdXIgbGVzIGZvcm11bGFpcmVzXHJcbiAgY29uc3QgW21vdGlmRm9ybSwgc2V0TW90aWZGb3JtXSA9IHVzZVN0YXRlKHtcclxuICAgIGNvZGU6ICcnLFxyXG4gICAgZGVzY3JpcHRpb246ICcnLFxyXG4gICAgZHVyYXRpb246IDE1LFxyXG4gICAgY29sb3I6ICcjOWI0ZDkzJyxcclxuICAgIGNvbG9yUmF5ZWU6ICcjNzkyNDg1JyxcclxuICAgIGFnZW5kYURlZmF1dDogJycsXHJcbiAgICBzZXJ2aWNlc0Rlc2lnbmVzOiAnJyxcclxuICAgIGNvdWxldXJTb21icmU6IGZhbHNlLFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBbYWdlbmRhRm9ybSwgc2V0QWdlbmRhRm9ybV0gPSB1c2VTdGF0ZSh7XHJcbiAgICBuYW1lOiAnJyxcclxuICAgIGRlc2NyaXB0aW9uOiAnJyxcclxuICAgIGNvbG9yOiAnIzliNGQ5MycsXHJcbiAgICBzZXJ2aWNlc0Rlc2lnbmVzOiAnJyxcclxuICAgIGlzUmVzb3VyY2U6IGZhbHNlLFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBbc2FsbGVGb3JtLCBzZXRTYWxsZUZvcm1dID0gdXNlU3RhdGUoe1xyXG4gICAgbmFtZTogJycsXHJcbiAgICBkZXNjcmlwdGlvbjogJycsXHJcbiAgICBjb2xvcjogJyM5YjRkOTMnLFxyXG4gICAgdHlwZTogJ2F0dGVudGUnIGFzICdhdHRlbnRlJyB8ICdjb25zdWx0YXRpb24nLFxyXG4gICAgY2FwYWNpdHk6IDEsXHJcbiAgICBzZXJ2aWNlc0Rlc2lnbmVzOiAnJyxcclxuICB9KTtcclxuXHJcbiAgY29uc3QgW2V2ZW5lbWVudEZvcm0sIHNldEV2ZW5lbWVudEZvcm1dID0gdXNlU3RhdGUoe1xyXG4gICAgdGl0bGU6ICcnLFxyXG4gICAgZGF0ZURlYnV0OiAnJyxcclxuICAgIGRhdGVGaW46ICcnLFxyXG4gICAgY29sb3I6ICcjOWI0ZDkzJyxcclxuICAgIGluZGlzcG9uaWJsZTogZmFsc2UsXHJcbiAgICBwZXJtYW5lbnQ6IGZhbHNlLFxyXG4gICAgdG91dGVKb3VybmVlOiBmYWxzZSxcclxuICAgIHRvdXNMZXNKb3VyczogZmFsc2UsXHJcbiAgICBjYWJpbmV0OiBmYWxzZSxcclxuICB9KTtcclxuXHJcbiAgLy8gw4l0YXRzIHBvdXIgbGVzIHBhcmFtw6h0cmVzIGfDqW7DqXJhdXhcclxuICBjb25zdCBbZ2VuZXJhbFNldHRpbmdzLCBzZXRHZW5lcmFsU2V0dGluZ3NdID0gdXNlU3RhdGUoe1xyXG4gICAgaGV1cmVPdXZlcnR1cmU6ICcwODowMCcsXHJcbiAgICBoZXVyZUZlcm1ldHVyZTogJzIwOjAwJyxcclxuICAgIHByZW1pZXJKb3VyU2VtYWluZTogJ0x1bmRpJyxcclxuICAgIHZ1ZURlZmF1dENhbGVuZHJpZXI6ICdTZW1haW5lJyxcclxuICAgIG1pbmltdW1JbnRlcnZhbGxlOiAnMTUgbWludXRlcycsXHJcbiAgICBmdXNlYXVIb3JhaXJlOiAnQWZyaWNhL0Nhc2FibGFuY2EnLFxyXG4gICAgY2FjaGVySm91cnNGZXJtZXR1cmU6IGZhbHNlLFxyXG4gICAgY3Vyc2V1ckFnZW5kYUhldXJlQ3VycmVudDogdHJ1ZSxcclxuICAgIGZpbHRyZTI0aENhYmluZXQ6IHRydWUsXHJcbiAgICB1dGlsaXNlckFiYnJldmlhdGlvbjogZmFsc2UsXHJcbiAgICB1dGlsaXNlckxpc3RlUGF0aWVudHM6IGZhbHNlLFxyXG4gICAgYWZmaWNoZXJTZXVsZW1lbnRWaXNpdGVzVGVybWluZWVzOiBmYWxzZSxcclxuICB9KTtcclxuXHJcbiAgLy8gRG9ubsOpZXMgZGUgdGVzdCBwb3VyIGxlcyBtb3RpZnNcclxuICBjb25zdCBbbW90aWZzLCBzZXRNb3RpZnNdID0gdXNlU3RhdGU8TW90aWZbXT4oW1xyXG4gICAgeyBpZDogMSwgY29kZTogJ0NOUycsIGRlc2NyaXB0aW9uOiAnQ29uc3VsdGF0aW9uJywgZHVyYXRpb246IDE1LCBjb2xvcjogJyM5YjRkOTMnLCBjb2xvclJheWVlOiAnIzc5MjQ4NScgfSxcclxuICAgIHsgaWQ6IDIsIGNvZGU6ICdDVFInLCBkZXNjcmlwdGlvbjogJ0NvbnRyw7RsZScsIGR1cmF0aW9uOiAxNSwgY29sb3I6ICcjYzA1ODg5JywgY29sb3JSYXllZTogJyM3ZDE4NjgnIH0sXHJcbiAgICB7IGlkOiAzLCBjb2RlOiAnQ0hJJywgZGVzY3JpcHRpb246ICdDaGlydXJnaWUvUGFybycsIGR1cmF0aW9uOiAzMCwgY29sb3I6ICcjYzE0MzE3JywgY29sb3JSYXllZTogJyM5MjJhMTInIH0sXHJcbiAgICB7IGlkOiA0LCBjb2RlOiAnQ09NJywgZGVzY3JpcHRpb246ICdDb21wb3NpdGUnLCBkdXJhdGlvbjogMTUsIGNvbG9yOiAnIzI4MzZjZCcsIGNvbG9yUmF5ZWU6ICcjMDkxNDg2JyB9LFxyXG4gICAgeyBpZDogNSwgY29kZTogJ0RTJywgZGVzY3JpcHRpb246ICdEw6lwb3NlIFN1dHVyZXMnLCBkdXJhdGlvbjogMTUsIGNvbG9yOiAnIzJlN2QzMicsIGNvbG9yUmF5ZWU6ICcjMWI1ZTIwJyB9LFxyXG4gICAgeyBpZDogNiwgY29kZTogJ0NTJywgZGVzY3JpcHRpb246ICcxZXIgQ29uc3VsdGF0aW9uJywgZHVyYXRpb246IDE1LCBjb2xvcjogJyNlOTFlNjMnLCBjb2xvclJheWVlOiAnI2MyMTg1YicgfSxcclxuICAgIHsgaWQ6IDcsIGNvZGU6ICdERVYnLCBkZXNjcmlwdGlvbjogJ0RldmlzJywgZHVyYXRpb246IDE1LCBjb2xvcjogJ3JnYigyMywgMTYwLCAxMzEpJywgY29sb3JSYXllZTogJyMwMjRmM2YnIH0sXHJcbiAgICB7IGlkOiA4LCBjb2RlOiAnRU5EJywgZGVzY3JpcHRpb246ICdFbmRvZG9udGllJywgZHVyYXRpb246IDYwLCBjb2xvcjogJyNhMDg1MjMnLCBjb2xvclJheWVlOiAnIzU2NDUwNycgfSxcclxuICAgIHsgaWQ6IDksIGNvZGU6ICdGT1InLCBkZXNjcmlwdGlvbjogJ0Zvcm1hdGlvbicsIGR1cmF0aW9uOiA2MCwgY29sb3I6ICcjOTgyNTUyJywgY29sb3JSYXllZTogJyM3MjE4M2MnIH0sXHJcbiAgICB7IGlkOiAxMCwgY29kZTogJ0lNUCcsIGRlc2NyaXB0aW9uOiAnSW1wbGFudG9sb2dpZScsIGR1cmF0aW9uOiA2MCwgY29sb3I6ICcjMzg4OTgzJywgY29sb3JSYXllZTogJyMyNjY0NWUnIH0sXHJcbiAgICB7IGlkOiAxMSwgY29kZTogJ09SVCcsIGRlc2NyaXB0aW9uOiAnT3J0aG9kb250aWUnLCBkdXJhdGlvbjogMzAsIGNvbG9yOiAnIzQ2NDA5OCcsIGNvbG9yUmF5ZWU6ICcjMWE3YzgxJyB9LFxyXG4gICAgeyBpZDogMTIsIGNvZGU6ICdDTScsIGRlc2NyaXB0aW9uOiAnRlMgQ2hhc3Npcy9Nb250YScsIGR1cmF0aW9uOiAxNSwgY29sb3I6ICcjNmM0MDdhJywgY29sb3JSYXllZTogJyM5YzQxNjgnIH0sXHJcbiAgICB7IGlkOiAxMywgY29kZTogJ1BPUycsIGRlc2NyaXB0aW9uOiAnUEEgUG9zZScsIGR1cmF0aW9uOiAxNSwgY29sb3I6ICcjYjI1OTUzJywgY29sb3JSYXllZTogJyM5YzNlM2UnIH0sXHJcbiAgICB7IGlkOiAxNCwgY29kZTogJ0VNUCcsIGRlc2NyaXB0aW9uOiAnUEMgVFAgRU1QJywgZHVyYXRpb246IDMwLCBjb2xvcjogJyM0YzhkNTcnLCBjb2xvclJheWVlOiAnIzM2NmU0MicgfSxcclxuICAgIHsgaWQ6IDE1LCBjb2RlOiAnQVJNJywgZGVzY3JpcHRpb246ICdQQyBFU1MgQXJtYXR1cmUnLCBkdXJhdGlvbjogMzAsIGNvbG9yOiAnIzMyOTFlZicsIGNvbG9yUmF5ZWU6ICcjMGQ2NjdiJyB9LFxyXG4gICAgeyBpZDogMTYsIGNvZGU6ICdTQ0UnLCBkZXNjcmlwdGlvbjogJ1BDIFNjZWxsZW1lbnQnLCBkdXJhdGlvbjogMTUsIGNvbG9yOiAnI2MyMTg1YicsIGNvbG9yUmF5ZWU6ICcjODgwZTRmJyB9LFxyXG4gICAgeyBpZDogMTcsIGNvZGU6ICdQUk8nLCBkZXNjcmlwdGlvbjogJ1Byb3BoeWxheGllJywgZHVyYXRpb246IDE1LCBjb2xvcjogJyMwMDk2ODgnLCBjb2xvclJheWVlOiAnIzAwNjk1YycgfSxcclxuICBdKTtcclxuXHJcbiAgLy8gRG9ubsOpZXMgZGUgdGVzdCBwb3VyIGxlcyBhZ2VuZGFzXHJcbiAgY29uc3QgW2FnZW5kYXMsIHNldEFnZW5kYXNdID0gdXNlU3RhdGU8QWdlbmRhW10+KFtcclxuICAgIHsgaWQ6IDEsIG5hbWU6ICdEb21pY2lsZScsIGRlc2NyaXB0aW9uOiAnJywgY29sb3I6ICcjZjQ1MTFlJywgaXNSZXNvdXJjZTogZmFsc2UgfSxcclxuICAgIHsgaWQ6IDIsIG5hbWU6ICdDbGluaXF1ZScsIGRlc2NyaXB0aW9uOiAnJywgY29sb3I6ICcjNDNhMDQ3JywgaXNSZXNvdXJjZTogZmFsc2UgfSxcclxuICAgIHsgaWQ6IDMsIG5hbWU6ICdDYWJpbmV0JywgZGVzY3JpcHRpb246ICcnLCBjb2xvcjogJyMwMDk3YTcnLCBpc1Jlc291cmNlOiBmYWxzZSB9LFxyXG4gIF0pO1xyXG5cclxuICAvLyBEb25uw6llcyBkZSB0ZXN0IHBvdXIgbGVzIGRvY3RldXJzIChDb2RlIGNvdWxldXJzIG3DqWRlY2luZSlcclxuICBjb25zdCBbZG9jdGV1cnMsIHNldERvY3RldXJzXSA9IHVzZVN0YXRlPERvY3RldXJbXT4oW1xyXG4gICAgeyBpZDogMSwgbmFtZTogJ0RvY3RldXInLCBjb2xvcjogJyNkNTAwMDAnIH0sXHJcbiAgXSk7XHJcblxyXG4gIC8vIERvbm7DqWVzIGRlIHRlc3QgcG91ciBsZXMgc2FsbGVzXHJcbiAgY29uc3QgW3NhbGxlcywgc2V0U2FsbGVzXSA9IHVzZVN0YXRlPFNhbGxlW10+KFtcclxuICAgIHsgaWQ6IDEsIG5hbWU6ICdTTFQnLCBkZXNjcmlwdGlvbjogJ1NhbGxlIGRcXCdhdHRlbnRlJywgY29sb3I6ICcjMDM5YmU1JywgdHlwZTogJ2F0dGVudGUnLCBjYXBhY2l0eTogMzAgfSxcclxuICAgIHsgaWQ6IDIsIG5hbWU6ICdGVEwnLCBkZXNjcmlwdGlvbjogJ0ZhdXRldWlsIDEnLCBjb2xvcjogJyNmZjU3MjInLCB0eXBlOiAnY29uc3VsdGF0aW9uJywgY2FwYWNpdHk6IDEgfSxcclxuICAgIHsgaWQ6IDMsIG5hbWU6ICdGVCAxJywgZGVzY3JpcHRpb246ICcnLCBjb2xvcjogJyMwMDAwMDAnLCB0eXBlOiAnY29uc3VsdGF0aW9uJywgY2FwYWNpdHk6IDEgfSxcclxuICAgIHsgaWQ6IDQsIG5hbWU6ICdTQUxMRSBERSBDT05TVUxUQVRJT04gMicsIGRlc2NyaXB0aW9uOiAnJywgY29sb3I6ICcjMDAwMDAwJywgdHlwZTogJ2NvbnN1bHRhdGlvbicsIGNhcGFjaXR5OiAxIH0sXHJcbiAgXSk7XHJcblxyXG4gIC8vIERvbm7DqWVzIGRlIHRlc3QgcG91ciBsZXMgw6l2w6luZW1lbnRzXHJcbiAgY29uc3QgW2V2ZW5lbWVudHMsIHNldEV2ZW5lbWVudHNdID0gdXNlU3RhdGU8RXZlbmVtZW50W10+KFtcclxuICAgIHtcclxuICAgICAgaWQ6IDEsXHJcbiAgICAgIHRpdGxlOiAnQ09OR1JFUycsXHJcbiAgICAgIG1lZGVjaW5zOiAnREVNTyBERU1PJyxcclxuICAgICAgZGF0ZURlYnV0OiAnMjEvMDQvMjAyNScsXHJcbiAgICAgIGRhdGVGaW46ICcyMy8wNC8yMDI1JyxcclxuICAgICAgY29sb3I6ICcjZjUyZGJlJyxcclxuICAgICAgaW5kaXNwb25pYmxlOiB0cnVlLFxyXG4gICAgICBwZXJtYW5lbnQ6IGZhbHNlLFxyXG4gICAgICB0b3V0ZUpvdXJuZWU6IHRydWUsXHJcbiAgICAgIHRvdXNMZXNKb3VyczogZmFsc2UsXHJcbiAgICAgIGNhYmluZXQ6IGZhbHNlXHJcbiAgICB9LFxyXG4gIF0pO1xyXG5cclxuICAvLyDDiXRhdHMgcG91ciBsYSByZWNoZXJjaGVcclxuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZSgnJyk7XHJcblxyXG4gIC8vIEZpbHRyZXIgbGVzIG1vdGlmcyBzZWxvbiBsYSByZWNoZXJjaGVcclxuICBjb25zdCBmaWx0ZXJlZE1vdGlmcyA9IG1vdGlmcy5maWx0ZXIobW90aWYgPT5cclxuICAgIG1vdGlmLmNvZGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XHJcbiAgICBtb3RpZi5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSlcclxuICApO1xyXG5cclxuICAvLyBKb3VycyBkZSBsYSBzZW1haW5lXHJcbiAgY29uc3Qgam91cnNTZW1haW5lID0gWydMdW5kaScsICdNYXJkaScsICdNZXJjcmVkaScsICdKZXVkaScsICdWZW5kcmVkaScsICdTYW1lZGknLCAnRGltYW5jaGUnXTtcclxuXHJcbiAgLy8gT3B0aW9ucyBwb3VyIGxlcyBzZWxlY3RzXHJcbiAgY29uc3QgdnVlQ2FsZW5kcmllck9wdGlvbnMgPSBbXHJcbiAgICB7IHZhbHVlOiAnU2VtYWluZScsIGxhYmVsOiAnU2VtYWluZScgfSxcclxuICAgIHsgdmFsdWU6ICdNb2lzJywgbGFiZWw6ICdNb2lzJyB9LFxyXG4gICAgeyB2YWx1ZTogJ0pvdXInLCBsYWJlbDogJ0pvdXInIH0sXHJcbiAgXTtcclxuXHJcbiAgY29uc3QgaW50ZXJ2YWxsZU9wdGlvbnMgPSBbXHJcbiAgICB7IHZhbHVlOiAnNSBtaW51dGVzJywgbGFiZWw6ICc1IG1pbnV0ZXMnIH0sXHJcbiAgICB7IHZhbHVlOiAnMTAgbWludXRlcycsIGxhYmVsOiAnMTAgbWludXRlcycgfSxcclxuICAgIHsgdmFsdWU6ICcxNSBtaW51dGVzJywgbGFiZWw6ICcxNSBtaW51dGVzJyB9LFxyXG4gICAgeyB2YWx1ZTogJzMwIG1pbnV0ZXMnLCBsYWJlbDogJzMwIG1pbnV0ZXMnIH0sXHJcbiAgXTtcclxuXHJcbiAgY29uc3QgZnVzZWF1SG9yYWlyZU9wdGlvbnMgPSBbXHJcbiAgICB7IHZhbHVlOiAnQWZyaWNhL0Nhc2FibGFuY2EnLCBsYWJlbDogJ0FmcmljYS9DYXNhYmxhbmNhJyB9LFxyXG4gICAgeyB2YWx1ZTogJ0V1cm9wZS9QYXJpcycsIGxhYmVsOiAnRXVyb3BlL1BhcmlzJyB9LFxyXG4gICAgeyB2YWx1ZTogJ1VUQycsIGxhYmVsOiAnVVRDJyB9LFxyXG4gIF07XHJcblxyXG4gIGNvbnN0IGFnZW5kYURlZmF1dE9wdGlvbnMgPSBbXHJcbiAgICB7IHZhbHVlOiAnYWdlbmRhMScsIGxhYmVsOiAnQWdlbmRhIHBhciBkw6lmYXV0JyB9LFxyXG4gICAgeyB2YWx1ZTogJ2FnZW5kYTInLCBsYWJlbDogJ0FnZW5kYSAyJyB9LFxyXG4gIF07XHJcblxyXG4gIGNvbnN0IHNlcnZpY2VzRGVzaWduZXNPcHRpb25zID0gW1xyXG4gICAgeyB2YWx1ZTogJ3NlcnZpY2UxJywgbGFiZWw6ICdTZXJ2aWNlcyBkw6lzaWduw6lzJyB9LFxyXG4gICAgeyB2YWx1ZTogJ3NlcnZpY2UyJywgbGFiZWw6ICdTZXJ2aWNlIDInIH0sXHJcbiAgXTtcclxuXHJcbiAgLy8gRm9uY3Rpb25zIHBvdXIgZ8OpcmVyIGxlcyBtb2RhbGVzXHJcbiAgY29uc3Qgb3Blbk1vdGlmTW9kYWwgPSAobW90aWY/OiBNb3RpZikgPT4ge1xyXG4gICAgaWYgKG1vdGlmKSB7XHJcbiAgICAgIHNldEVkaXRpbmdNb3RpZihtb3RpZik7XHJcbiAgICAgIHNldE1vdGlmRm9ybSh7XHJcbiAgICAgICAgY29kZTogbW90aWYuY29kZSxcclxuICAgICAgICBkZXNjcmlwdGlvbjogbW90aWYuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgZHVyYXRpb246IG1vdGlmLmR1cmF0aW9uLFxyXG4gICAgICAgIGNvbG9yOiBtb3RpZi5jb2xvcixcclxuICAgICAgICBjb2xvclJheWVlOiBtb3RpZi5jb2xvclJheWVlLFxyXG4gICAgICAgIGFnZW5kYURlZmF1dDogJycsXHJcbiAgICAgICAgc2VydmljZXNEZXNpZ25lczogJycsXHJcbiAgICAgICAgY291bGV1clNvbWJyZTogZmFsc2UsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0RWRpdGluZ01vdGlmKG51bGwpO1xyXG4gICAgICBzZXRNb3RpZkZvcm0oe1xyXG4gICAgICAgIGNvZGU6ICcnLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnJyxcclxuICAgICAgICBkdXJhdGlvbjogMTUsXHJcbiAgICAgICAgY29sb3I6ICcjOWI0ZDkzJyxcclxuICAgICAgICBjb2xvclJheWVlOiAnIzc5MjQ4NScsXHJcbiAgICAgICAgYWdlbmRhRGVmYXV0OiAnJyxcclxuICAgICAgICBzZXJ2aWNlc0Rlc2lnbmVzOiAnJyxcclxuICAgICAgICBjb3VsZXVyU29tYnJlOiBmYWxzZSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgICBzZXRNb3RpZk1vZGFsT3BlbmVkKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IG9wZW5BZ2VuZGFNb2RhbCA9IChhZ2VuZGE/OiBBZ2VuZGEpID0+IHtcclxuICAgIGlmIChhZ2VuZGEpIHtcclxuICAgICAgc2V0RWRpdGluZ0FnZW5kYShhZ2VuZGEpO1xyXG4gICAgICBzZXRBZ2VuZGFGb3JtKHtcclxuICAgICAgICBuYW1lOiBhZ2VuZGEubmFtZSxcclxuICAgICAgICBkZXNjcmlwdGlvbjogYWdlbmRhLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgIGNvbG9yOiBhZ2VuZGEuY29sb3IsXHJcbiAgICAgICAgc2VydmljZXNEZXNpZ25lczogJycsXHJcbiAgICAgICAgaXNSZXNvdXJjZTogYWdlbmRhLmlzUmVzb3VyY2UsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0RWRpdGluZ0FnZW5kYShudWxsKTtcclxuICAgICAgc2V0QWdlbmRhRm9ybSh7XHJcbiAgICAgICAgbmFtZTogJycsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICcnLFxyXG4gICAgICAgIGNvbG9yOiAnIzliNGQ5MycsXHJcbiAgICAgICAgc2VydmljZXNEZXNpZ25lczogJycsXHJcbiAgICAgICAgaXNSZXNvdXJjZTogZmFsc2UsXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgc2V0QWdlbmRhTW9kYWxPcGVuZWQodHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3Qgb3BlblNhbGxlTW9kYWwgPSAoc2FsbGU/OiBTYWxsZSkgPT4ge1xyXG4gICAgaWYgKHNhbGxlKSB7XHJcbiAgICAgIHNldEVkaXRpbmdTYWxsZShzYWxsZSk7XHJcbiAgICAgIHNldFNhbGxlRm9ybSh7XHJcbiAgICAgICAgbmFtZTogc2FsbGUubmFtZSxcclxuICAgICAgICBkZXNjcmlwdGlvbjogc2FsbGUuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgY29sb3I6IHNhbGxlLmNvbG9yLFxyXG4gICAgICAgIHR5cGU6IHNhbGxlLnR5cGUsXHJcbiAgICAgICAgY2FwYWNpdHk6IHNhbGxlLmNhcGFjaXR5LFxyXG4gICAgICAgIHNlcnZpY2VzRGVzaWduZXM6ICcnLFxyXG4gICAgICB9KTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldEVkaXRpbmdTYWxsZShudWxsKTtcclxuICAgICAgc2V0U2FsbGVGb3JtKHtcclxuICAgICAgICBuYW1lOiAnJyxcclxuICAgICAgICBkZXNjcmlwdGlvbjogJycsXHJcbiAgICAgICAgY29sb3I6ICcjOWI0ZDkzJyxcclxuICAgICAgICB0eXBlOiAnYXR0ZW50ZScsXHJcbiAgICAgICAgY2FwYWNpdHk6IDEsXHJcbiAgICAgICAgc2VydmljZXNEZXNpZ25lczogJycsXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgc2V0U2FsbGVNb2RhbE9wZW5lZCh0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBvcGVuRXZlbmVtZW50TW9kYWwgPSAoZXZlbmVtZW50PzogRXZlbmVtZW50KSA9PiB7XHJcbiAgICBpZiAoZXZlbmVtZW50KSB7XHJcbiAgICAgIHNldEVkaXRpbmdFdmVuZW1lbnQoZXZlbmVtZW50KTtcclxuICAgICAgc2V0RXZlbmVtZW50Rm9ybSh7XHJcbiAgICAgICAgdGl0bGU6IGV2ZW5lbWVudC50aXRsZSxcclxuICAgICAgICBkYXRlRGVidXQ6IGV2ZW5lbWVudC5kYXRlRGVidXQsXHJcbiAgICAgICAgZGF0ZUZpbjogZXZlbmVtZW50LmRhdGVGaW4sXHJcbiAgICAgICAgY29sb3I6IGV2ZW5lbWVudC5jb2xvcixcclxuICAgICAgICBpbmRpc3BvbmlibGU6IGV2ZW5lbWVudC5pbmRpc3BvbmlibGUsXHJcbiAgICAgICAgcGVybWFuZW50OiBldmVuZW1lbnQucGVybWFuZW50LFxyXG4gICAgICAgIHRvdXRlSm91cm5lZTogZXZlbmVtZW50LnRvdXRlSm91cm5lZSxcclxuICAgICAgICB0b3VzTGVzSm91cnM6IGV2ZW5lbWVudC50b3VzTGVzSm91cnMsXHJcbiAgICAgICAgY2FiaW5ldDogZXZlbmVtZW50LmNhYmluZXQsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0RWRpdGluZ0V2ZW5lbWVudChudWxsKTtcclxuICAgICAgc2V0RXZlbmVtZW50Rm9ybSh7XHJcbiAgICAgICAgdGl0bGU6ICcnLFxyXG4gICAgICAgIGRhdGVEZWJ1dDogJycsXHJcbiAgICAgICAgZGF0ZUZpbjogJycsXHJcbiAgICAgICAgY29sb3I6ICcjOWI0ZDkzJyxcclxuICAgICAgICBpbmRpc3BvbmlibGU6IGZhbHNlLFxyXG4gICAgICAgIHBlcm1hbmVudDogZmFsc2UsXHJcbiAgICAgICAgdG91dGVKb3VybmVlOiBmYWxzZSxcclxuICAgICAgICB0b3VzTGVzSm91cnM6IGZhbHNlLFxyXG4gICAgICAgIGNhYmluZXQ6IGZhbHNlLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICAgIHNldEV2ZW5lbWVudE1vZGFsT3BlbmVkKHRydWUpO1xyXG4gIH07XHJcblxyXG4gIC8vIEZvbmN0aW9ucyBwb3VyIHNhdXZlZ2FyZGVyXHJcbiAgY29uc3QgaGFuZGxlU2F2ZU1vdGlmID0gKCkgPT4ge1xyXG4gICAgaWYgKGVkaXRpbmdNb3RpZikge1xyXG4gICAgICAvLyBNb2RpZmllciB1biBtb3RpZiBleGlzdGFudFxyXG4gICAgICBzZXRNb3RpZnMocHJldiA9PiBwcmV2Lm1hcChtID0+XHJcbiAgICAgICAgbS5pZCA9PT0gZWRpdGluZ01vdGlmLmlkXHJcbiAgICAgICAgICA/IHsgLi4ubSwgLi4ubW90aWZGb3JtIH1cclxuICAgICAgICAgIDogbVxyXG4gICAgICApKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIEFqb3V0ZXIgdW4gbm91dmVhdSBtb3RpZlxyXG4gICAgICBjb25zdCBuZXdNb3RpZjogTW90aWYgPSB7XHJcbiAgICAgICAgaWQ6IERhdGUubm93KCksXHJcbiAgICAgICAgY29kZTogbW90aWZGb3JtLmNvZGUsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IG1vdGlmRm9ybS5kZXNjcmlwdGlvbixcclxuICAgICAgICBkdXJhdGlvbjogbW90aWZGb3JtLmR1cmF0aW9uLFxyXG4gICAgICAgIGNvbG9yOiBtb3RpZkZvcm0uY29sb3IsXHJcbiAgICAgICAgY29sb3JSYXllZTogbW90aWZGb3JtLmNvbG9yUmF5ZWUsXHJcbiAgICAgIH07XHJcbiAgICAgIHNldE1vdGlmcyhwcmV2ID0+IFsuLi5wcmV2LCBuZXdNb3RpZl0pO1xyXG4gICAgfVxyXG4gICAgc2V0TW90aWZNb2RhbE9wZW5lZChmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2F2ZUFnZW5kYSA9ICgpID0+IHtcclxuICAgIGlmIChlZGl0aW5nQWdlbmRhKSB7XHJcbiAgICAgIC8vIE1vZGlmaWVyIHVuIGFnZW5kYSBleGlzdGFudFxyXG4gICAgICBzZXRBZ2VuZGFzKHByZXYgPT4gcHJldi5tYXAoYSA9PlxyXG4gICAgICAgIGEuaWQgPT09IGVkaXRpbmdBZ2VuZGEuaWRcclxuICAgICAgICAgID8geyAuLi5hLCAuLi5hZ2VuZGFGb3JtIH1cclxuICAgICAgICAgIDogYVxyXG4gICAgICApKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIEFqb3V0ZXIgdW4gbm91dmVsIGFnZW5kYVxyXG4gICAgICBjb25zdCBuZXdBZ2VuZGE6IEFnZW5kYSA9IHtcclxuICAgICAgICBpZDogRGF0ZS5ub3coKSxcclxuICAgICAgICBuYW1lOiBhZ2VuZGFGb3JtLm5hbWUsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IGFnZW5kYUZvcm0uZGVzY3JpcHRpb24sXHJcbiAgICAgICAgY29sb3I6IGFnZW5kYUZvcm0uY29sb3IsXHJcbiAgICAgICAgaXNSZXNvdXJjZTogYWdlbmRhRm9ybS5pc1Jlc291cmNlLFxyXG4gICAgICB9O1xyXG4gICAgICBzZXRBZ2VuZGFzKHByZXYgPT4gWy4uLnByZXYsIG5ld0FnZW5kYV0pO1xyXG4gICAgfVxyXG4gICAgc2V0QWdlbmRhTW9kYWxPcGVuZWQoZmFsc2UpO1xyXG4gIH07XHJcblxyXG4gIC8vIEZvbmN0aW9uIHBvdXIgc3VwcHJpbWVyIHVuIG1vdGlmXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlTW90aWYgPSAoaWQ6IG51bWJlcikgPT4ge1xyXG4gICAgc2V0TW90aWZzKHByZXYgPT4gcHJldi5maWx0ZXIobSA9PiBtLmlkICE9PSBpZCkpO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8U3RhY2sgZ2FwPVwibGdcIiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgPFRpdGxlIG9yZGVyPXsyfSBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktODAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgPEljb25Ib21lIHNpemU9ezI0fSBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwXCIgLz5cclxuICAgICAgICBBY2N1ZWlsIGV0IEFnZW5kYVxyXG4gICAgICA8L1RpdGxlPlxyXG5cclxuICAgICAgPFRhYnMgdmFsdWU9e2FjdGl2ZVRhYn0gb25DaGFuZ2U9eyh2YWx1ZSkgPT4gc2V0QWN0aXZlVGFiKHZhbHVlIHx8ICdnZW5lcmFsJyl9IHZhcmlhbnQ9XCJvdXRsaW5lXCI+XHJcbiAgICAgICAgPFRhYnMuTGlzdD5cclxuICAgICAgICAgIDxUYWJzLlRhYiB2YWx1ZT1cImdlbmVyYWxcIiBsZWZ0U2VjdGlvbj17PEljb25TZXR0aW5ncyBzaXplPXsxNn0gLz59PlxyXG4gICAgICAgICAgICBHw6luw6lyYWxcclxuICAgICAgICAgIDwvVGFicy5UYWI+XHJcbiAgICAgICAgICA8VGFicy5UYWIgdmFsdWU9XCJtb3RpZnNcIiBsZWZ0U2VjdGlvbj17PEljb25DYWxlbmRhciBzaXplPXsxNn0gLz59PlxyXG4gICAgICAgICAgICBNb3RpZnNcclxuICAgICAgICAgIDwvVGFicy5UYWI+XHJcbiAgICAgICAgICA8VGFicy5UYWIgdmFsdWU9XCJjb2RlLWNvdWxldXJzXCIgbGVmdFNlY3Rpb249ezxJY29uQ29sb3JQaWNrZXIgc2l6ZT17MTZ9IC8+fT5cclxuICAgICAgICAgICAgQ29kZSBjb3VsZXVycyBtw6lkZWNpblxyXG4gICAgICAgICAgPC9UYWJzLlRhYj5cclxuICAgICAgICAgIDxUYWJzLlRhYiB2YWx1ZT1cImFnZW5kYXNcIiBsZWZ0U2VjdGlvbj17PEljb25DYWxlbmRhciBzaXplPXsxNn0gLz59PlxyXG4gICAgICAgICAgICBBZ2VuZGFzXHJcbiAgICAgICAgICA8L1RhYnMuVGFiPlxyXG4gICAgICAgICAgPFRhYnMuVGFiIHZhbHVlPVwic2FsbGVzXCIgbGVmdFNlY3Rpb249ezxJY29uQnVpbGRpbmcgc2l6ZT17MTZ9IC8+fT5cclxuICAgICAgICAgICAgU2FsbGVzXHJcbiAgICAgICAgICA8L1RhYnMuVGFiPlxyXG4gICAgICAgICAgPFRhYnMuVGFiIHZhbHVlPVwiZXZlbmVtZW50c1wiIGxlZnRTZWN0aW9uPXs8SWNvblNldHRpbmdzIHNpemU9ezE2fSAvPn0+XHJcbiAgICAgICAgICAgIMOJdsOpbmVtZW50c1xyXG4gICAgICAgICAgPC9UYWJzLlRhYj5cclxuICAgICAgICA8L1RhYnMuTGlzdD5cclxuXHJcbiAgICAgICAgey8qIE9uZ2xldCBHw6luw6lyYWwgKi99XHJcbiAgICAgICAgPFRhYnMuUGFuZWwgdmFsdWU9XCJnZW5lcmFsXCIgcHQ9XCJtZFwiPlxyXG4gICAgICAgICAgPFBhcGVyIHA9XCJtZFwiIHdpdGhCb3JkZXI+XHJcbiAgICAgICAgICAgIDxTdGFjayBnYXA9XCJsZ1wiPlxyXG4gICAgICAgICAgICAgIHsvKiBIb3JhaXJlcyBkdSBjZW50cmUgKi99XHJcbiAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgIDxUaXRsZSBvcmRlcj17NH0gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBtYi1tZFwiPlxyXG4gICAgICAgICAgICAgICAgICBIb3JhaXJlcyBkdSBjZW50cmVcclxuICAgICAgICAgICAgICAgIDwvVGl0bGU+XHJcbiAgICAgICAgICAgICAgICA8R3JpZD5cclxuICAgICAgICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGZ3PXs1MDB9IG1iPVwieHNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIEhldXJlIGQnb3V2ZXJ0dXJlKHMpXHJcbiAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtnZW5lcmFsU2V0dGluZ3MuaGV1cmVPdXZlcnR1cmV9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEdlbmVyYWxTZXR0aW5ncyhwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhldXJlT3V2ZXJ0dXJlOiBlLnRhcmdldC52YWx1ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwODowMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGZ3PXs1MDB9IG1iPVwieHNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIEhldXJlIGRlIGZlcm1ldHVyZShzKVxyXG4gICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbFNldHRpbmdzLmhldXJlRmVybWV0dXJlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZXVyZUZlcm1ldHVyZTogZS50YXJnZXQudmFsdWVcclxuICAgICAgICAgICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMjA6MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIEpvdXJzIGRlIGZlcm1ldHVyZSAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNFwiPlxyXG4gICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBmdz17NTAwfSBtYj1cInhzXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgSm91cnMgZGUgZmVybWV0dXJlXHJcbiAgICAgICAgICAgICAgICAgIDwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yIGZsZXgtd3JhcFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtqb3Vyc1NlbWFpbmUubWFwKChqb3VyKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtqb3VyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwibGlnaHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtqb3VyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgey8qIFBhcmFtw6h0cmVzIGQnYWZmaWNoYWdlICovfVxyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8VGl0bGUgb3JkZXI9ezR9IGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgbWItbWRcIj5cclxuICAgICAgICAgICAgICAgICAgUGFyYW3DqHRyZXMgZCdhZmZpY2hhZ2VcclxuICAgICAgICAgICAgICAgIDwvVGl0bGU+XHJcbiAgICAgICAgICAgICAgICA8R3JpZD5cclxuICAgICAgICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiUHJlbWllciBqb3VyIGRlIGxhIHNlbWFpbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2dlbmVyYWxTZXR0aW5ncy5wcmVtaWVySm91clNlbWFpbmV9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwcmVtaWVySm91clNlbWFpbmU6IHZhbHVlIHx8ICdMdW5kaSdcclxuICAgICAgICAgICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICAgIGRhdGE9e2pvdXJzU2VtYWluZS5tYXAoam91ciA9PiAoeyB2YWx1ZTogam91ciwgbGFiZWw6IGpvdXIgfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlZ1ZSBwYXIgZMOpZmF1dCBkdSBjYWxlbmRyaWVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtnZW5lcmFsU2V0dGluZ3MudnVlRGVmYXV0Q2FsZW5kcmllcn1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsodmFsdWUpID0+IHNldEdlbmVyYWxTZXR0aW5ncyhwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZ1ZURlZmF1dENhbGVuZHJpZXI6IHZhbHVlIHx8ICdTZW1haW5lJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgZGF0YT17dnVlQ2FsZW5kcmllck9wdGlvbnN9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiTWluaW11bSBpbnRlcnZhbGxlIGRlIHRlbXBzXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtnZW5lcmFsU2V0dGluZ3MubWluaW11bUludGVydmFsbGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW5pbXVtSW50ZXJ2YWxsZTogdmFsdWUgfHwgJzE1IG1pbnV0ZXMnXHJcbiAgICAgICAgICAgICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBkYXRhPXtpbnRlcnZhbGxlT3B0aW9uc31cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJGdXNlYXUgaG9yYWlyZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Z2VuZXJhbFNldHRpbmdzLmZ1c2VhdUhvcmFpcmV9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmdXNlYXVIb3JhaXJlOiB2YWx1ZSB8fCAnQWZyaWNhL0Nhc2FibGFuY2EnXHJcbiAgICAgICAgICAgICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBkYXRhPXtmdXNlYXVIb3JhaXJlT3B0aW9uc31cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgICAgICAgIHsvKiBPcHRpb25zIGQnYWZmaWNoYWdlICovfVxyXG4gICAgICAgICAgICAgICAgPFN0YWNrIGdhcD1cInhzXCIgbXQ9XCJtZFwiPlxyXG4gICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtnZW5lcmFsU2V0dGluZ3MuY2FjaGVySm91cnNGZXJtZXR1cmV9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgIGNhY2hlckpvdXJzRmVybWV0dXJlOiBlLmN1cnJlbnRUYXJnZXQuY2hlY2tlZFxyXG4gICAgICAgICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkNhY2hlciBsZXMgam91cnMgZGUgZmVybWV0dXJlXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Z2VuZXJhbFNldHRpbmdzLmN1cnNldXJBZ2VuZGFIZXVyZUN1cnJlbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgIGN1cnNldXJBZ2VuZGFIZXVyZUN1cnJlbnQ6IGUuY3VycmVudFRhcmdldC5jaGVja2VkXHJcbiAgICAgICAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiQ3Vyc2V1ciBkJ2FnZW5kYSDDoCBsJ2hldXJlIGN1cnJlbnRcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtnZW5lcmFsU2V0dGluZ3MuZmlsdHJlMjRoQ2FiaW5ldH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEdlbmVyYWxTZXR0aW5ncyhwcmV2ID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgICAgICAgZmlsdHJlMjRoQ2FiaW5ldDogZS5jdXJyZW50VGFyZ2V0LmNoZWNrZWRcclxuICAgICAgICAgICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJGaWx0cmUgZGUgMjRoIGR1IGNhYmluZXRcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtnZW5lcmFsU2V0dGluZ3MudXRpbGlzZXJBYmJyZXZpYXRpb259XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgIHV0aWxpc2VyQWJicmV2aWF0aW9uOiBlLmN1cnJlbnRUYXJnZXQuY2hlY2tlZFxyXG4gICAgICAgICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlV0aWxpc2VyIGFicsOpdmlhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxDaGVja2JveFxyXG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2dlbmVyYWxTZXR0aW5ncy51dGlsaXNlckxpc3RlUGF0aWVudHN9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRHZW5lcmFsU2V0dGluZ3MocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgIHV0aWxpc2VyTGlzdGVQYXRpZW50czogZS5jdXJyZW50VGFyZ2V0LmNoZWNrZWRcclxuICAgICAgICAgICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJVdGlsaXNlciBsYSBsaXN0ZSBkZXMgcGF0aWVudHNcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtnZW5lcmFsU2V0dGluZ3MuYWZmaWNoZXJTZXVsZW1lbnRWaXNpdGVzVGVybWluZWVzfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0R2VuZXJhbFNldHRpbmdzKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICBhZmZpY2hlclNldWxlbWVudFZpc2l0ZXNUZXJtaW5lZXM6IGUuY3VycmVudFRhcmdldC5jaGVja2VkXHJcbiAgICAgICAgICAgICAgICAgICAgfSkpfVxyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiQWZmaWNoZXIgc2V1bGVtZW50IGxlcyB2aXNpdGVzIHRlcm1pbsOpZXMgZGFucyAnSGlzdG9yaXF1ZSBqb3VybmFsaWVyJ1wiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L1N0YWNrPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICB7LyogUmFwcGVsIGRlIHJlbmRlei12b3VzICovfVxyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8VGl0bGUgb3JkZXI9ezR9IGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgbWItbWRcIj5cclxuICAgICAgICAgICAgICAgICAgUmFwcGVsIGRlIHJlbmRlei12b3VzXHJcbiAgICAgICAgICAgICAgICA8L1RpdGxlPlxyXG4gICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0gbWI9XCJ4c1wiPlxyXG4gICAgICAgICAgICAgICAgICBEdXLDqWUgYXZhbnQgUkRWXHJcbiAgICAgICAgICAgICAgICA8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICA8R3JpZD5cclxuICAgICAgICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiNVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBkYXRhPXtbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsgdmFsdWU6ICc1JywgbGFiZWw6ICdNb2TDqGxlIFNNUycgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgeyB2YWx1ZTogJzEwJywgbGFiZWw6ICcxMCBtaW51dGVzJyB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7IHZhbHVlOiAnMTUnLCBsYWJlbDogJzE1IG1pbnV0ZXMnIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICBdfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk1vZMOobGUgRW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgZGF0YT17W1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB7IHZhbHVlOiAnZW1haWwxJywgbGFiZWw6ICdNb2TDqGxlIEVtYWlsJyB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7IHZhbHVlOiAnZW1haWwyJywgbGFiZWw6ICdFbWFpbCAyJyB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L1N0YWNrPlxyXG4gICAgICAgICAgPC9QYXBlcj5cclxuICAgICAgICA8L1RhYnMuUGFuZWw+XHJcblxyXG4gICAgICAgIHsvKiBPbmdsZXQgTW90aWZzICovfVxyXG4gICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwibW90aWZzXCIgcHQ9XCJtZFwiPlxyXG4gICAgICAgICAgPFBhcGVyIHA9XCJtZFwiIHdpdGhCb3JkZXI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTRcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUmVjaGVyY2hlclwiXHJcbiAgICAgICAgICAgICAgICAgIGxlZnRTZWN0aW9uPXs8SWNvblNlYXJjaCBzaXplPXsxNn0gLz59XHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hUZXJtfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTY0XCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgbGVmdFNlY3Rpb249ezxJY29uUGx1cyBzaXplPXsxNn0gLz59XHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuTW90aWZNb2RhbCgpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgaG92ZXI6YmctYmx1ZS02MDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIEFqb3V0ZXIgbm91dmVhdSBtb3RpZlxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxTY3JvbGxBcmVhPlxyXG4gICAgICAgICAgICAgIDxUYWJsZSBzdHJpcGVkIGhpZ2hsaWdodE9uSG92ZXIgd2l0aFRhYmxlQm9yZGVyPlxyXG4gICAgICAgICAgICAgICAgPFRhYmxlLlRoZWFkPlxyXG4gICAgICAgICAgICAgICAgICA8VGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPkNvZGU8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5Nb3RpZjwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPkR1csOpZSAobWluKTwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPkNvdWxldXI8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5Db3VsZXVyIHJhecOpZTwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoIHN0eWxlPXt7IHdpZHRoOiAxMDAgfX0+PC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgPC9UYWJsZS5Ucj5cclxuICAgICAgICAgICAgICAgIDwvVGFibGUuVGhlYWQ+XHJcbiAgICAgICAgICAgICAgICA8VGFibGUuVGJvZHk+XHJcbiAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZE1vdGlmcy5tYXAoKG1vdGlmKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRyIGtleT17bW90aWYuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmdz17NTAwfT57bW90aWYuY29kZX08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPnttb3RpZi5kZXNjcmlwdGlvbn08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPnttb3RpZi5kdXJhdGlvbn08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoIGNvbG9yPXttb3RpZi5jb2xvcn0gc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+e21vdGlmLmNvbG9yfTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoIGNvbG9yPXttb3RpZi5jb2xvclJheWVlfSBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIj57bW90aWYuY29sb3JSYXllZX08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEdyb3VwIGdhcD1cInhzXCIganVzdGlmeT1cImNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGxhYmVsPVwiTW9kaWZpZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBY3Rpb25JY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzdWJ0bGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuTW90aWZNb2RhbChtb3RpZil9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uRWRpdCBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWN0aW9uSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgbGFiZWw9XCJTdXBwcmltZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBY3Rpb25JY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzdWJ0bGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cInJlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZU1vdGlmKG1vdGlmLmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25UcmFzaCBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWN0aW9uSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvR3JvdXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgPC9UYWJsZS5UYm9keT5cclxuICAgICAgICAgICAgICA8L1RhYmxlPlxyXG4gICAgICAgICAgICA8L1Njcm9sbEFyZWE+XHJcbiAgICAgICAgICA8L1BhcGVyPlxyXG4gICAgICAgIDwvVGFicy5QYW5lbD5cclxuXHJcbiAgICAgICAgey8qIE9uZ2xldCBDb2RlIGNvdWxldXJzIG3DqWRlY2luICovfVxyXG4gICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwiY29kZS1jb3VsZXVyc1wiIHB0PVwibWRcIj5cclxuICAgICAgICAgIDxQYXBlciBwPVwibWRcIiB3aXRoQm9yZGVyPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi00XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRvY3RldXJcIlxyXG4gICAgICAgICAgICAgICAgICBkYXRhPXtkb2N0ZXVycy5tYXAoZCA9PiAoeyB2YWx1ZTogZC5pZC50b1N0cmluZygpLCBsYWJlbDogZC5uYW1lIH0pKX1cclxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IDIwMCB9fVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0+Q291bGV1cjwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoIGNvbG9yPVwiI2Q1MDAwMFwiIHNpemU9ezIwfSAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImZpbGxlZFwiXHJcbiAgICAgICAgICAgICAgICBjb2xvcj1cImdyYXlcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIEVucmVnaXN0cmVyXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgPFNjcm9sbEFyZWE+XHJcbiAgICAgICAgICAgICAgPFRhYmxlIHN0cmlwZWQgaGlnaGxpZ2h0T25Ib3ZlciB3aXRoVGFibGVCb3JkZXI+XHJcbiAgICAgICAgICAgICAgICA8VGFibGUuVGhlYWQ+XHJcbiAgICAgICAgICAgICAgICAgIDxUYWJsZS5Ucj5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGg+RG9jdGV1cjwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPkNvdWxldXI8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaCBzdHlsZT17eyB3aWR0aDogMTAwIH19PjwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgIDwvVGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICA8L1RhYmxlLlRoZWFkPlxyXG4gICAgICAgICAgICAgICAgPFRhYmxlLlRib2R5PlxyXG4gICAgICAgICAgICAgICAgICB7ZG9jdGV1cnMubWFwKChkb2N0ZXVyKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRyIGtleT17ZG9jdGV1ci5pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZ3PXs1MDB9Pntkb2N0ZXVyLm5hbWV9PC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDb2xvclN3YXRjaCBjb2xvcj17ZG9jdGV1ci5jb2xvcn0gc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+e2RvY3RldXIuY29sb3J9PC9UZXh0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxHcm91cCBnYXA9XCJ4c1wiIGp1c3RpZnk9XCJjZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBsYWJlbD1cIlN1cHByaW1lclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjdGlvbkljb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInN1YnRsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPVwicmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25YIHNpemU9ezE2fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY3Rpb25JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Hcm91cD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5Ucj5cclxuICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICA8L1RhYmxlLlRib2R5PlxyXG4gICAgICAgICAgICAgIDwvVGFibGU+XHJcbiAgICAgICAgICAgIDwvU2Nyb2xsQXJlYT5cclxuICAgICAgICAgIDwvUGFwZXI+XHJcbiAgICAgICAgPC9UYWJzLlBhbmVsPlxyXG5cclxuICAgICAgICB7LyogT25nbGV0IEFnZW5kYXMgKi99XHJcbiAgICAgICAgPFRhYnMuUGFuZWwgdmFsdWU9XCJhZ2VuZGFzXCIgcHQ9XCJtZFwiPlxyXG4gICAgICAgICAgPFBhcGVyIHA9XCJtZFwiIHdpdGhCb3JkZXI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTRcIj5cclxuICAgICAgICAgICAgICA8VGl0bGUgb3JkZXI9ezR9PkFnZW5kYXM8L1RpdGxlPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIGxlZnRTZWN0aW9uPXs8SWNvblBsdXMgc2l6ZT17MTZ9IC8+fVxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3BlbkFnZW5kYU1vZGFsKCl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTYwMFwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgTm91dmVsIGFnZW5kYVxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxTY3JvbGxBcmVhPlxyXG4gICAgICAgICAgICAgIDxUYWJsZSBzdHJpcGVkIGhpZ2hsaWdodE9uSG92ZXIgd2l0aFRhYmxlQm9yZGVyPlxyXG4gICAgICAgICAgICAgICAgPFRhYmxlLlRoZWFkPlxyXG4gICAgICAgICAgICAgICAgICA8VGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPk5vbTwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPkRlc2NyaXB0aW9uPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGg+Q291bGV1cjwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPlJlc3NvdXJjZTwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoIHN0eWxlPXt7IHdpZHRoOiAxMDAgfX0+PC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgPC9UYWJsZS5Ucj5cclxuICAgICAgICAgICAgICAgIDwvVGFibGUuVGhlYWQ+XHJcbiAgICAgICAgICAgICAgICA8VGFibGUuVGJvZHk+XHJcbiAgICAgICAgICAgICAgICAgIHthZ2VuZGFzLmxlbmd0aCA9PT0gMCA/IChcclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQgY29sU3Bhbj17NX0gY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1ncmF5LTUwMCBweS04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIEF1Y3VuIGFnZW5kYSB0cm91dsOpXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgYWdlbmRhcy5tYXAoKGFnZW5kYSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRyIGtleT17YWdlbmRhLmlkfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZ3PXs1MDB9PnthZ2VuZGEubmFtZX08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD57YWdlbmRhLmRlc2NyaXB0aW9ufTwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29sb3JTd2F0Y2ggY29sb3I9e2FnZW5kYS5jb2xvcn0gc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIj57YWdlbmRhLmNvbG9yfTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSBjb2xvcj17YWdlbmRhLmlzUmVzb3VyY2UgPyAnZ3JlZW4nIDogJ2dyYXknfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthZ2VuZGEuaXNSZXNvdXJjZSA/ICdPdWknIDogJ05vbid9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxHcm91cCBnYXA9XCJ4c1wiIGp1c3RpZnk9XCJjZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGxhYmVsPVwiTW9kaWZpZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFjdGlvbkljb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwic3VidGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5BZ2VuZGFNb2RhbChhZ2VuZGEpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25FZGl0IHNpemU9ezE2fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjdGlvbkljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Hcm91cD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICAgICAgKSlcclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvVGFibGUuVGJvZHk+XHJcbiAgICAgICAgICAgICAgPC9UYWJsZT5cclxuICAgICAgICAgICAgPC9TY3JvbGxBcmVhPlxyXG4gICAgICAgICAgPC9QYXBlcj5cclxuICAgICAgICA8L1RhYnMuUGFuZWw+XHJcblxyXG4gICAgICAgIHsvKiBPbmdsZXQgU2FsbGVzICovfVxyXG4gICAgICAgIDxUYWJzLlBhbmVsIHZhbHVlPVwic2FsbGVzXCIgcHQ9XCJtZFwiPlxyXG4gICAgICAgICAgPFBhcGVyIHA9XCJtZFwiIHdpdGhCb3JkZXI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTRcIj5cclxuICAgICAgICAgICAgICA8VGl0bGUgb3JkZXI9ezR9PlNhbGxlczwvVGl0bGU+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgbGVmdFNlY3Rpb249ezxJY29uUGx1cyBzaXplPXsxNn0gLz59XHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuU2FsbGVNb2RhbCgpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgaG92ZXI6YmctYmx1ZS02MDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIEFqb3V0ZXIgc2FsbGVcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICA8U2Nyb2xsQXJlYT5cclxuICAgICAgICAgICAgICA8VGFibGUgc3RyaXBlZCBoaWdobGlnaHRPbkhvdmVyIHdpdGhUYWJsZUJvcmRlcj5cclxuICAgICAgICAgICAgICAgIDxUYWJsZS5UaGVhZD5cclxuICAgICAgICAgICAgICAgICAgPFRhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5Ob208L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5EZXNjcmlwdGlvbjwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPkNvdWxldXI8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5UeXBlPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGg+Q2FwYWNpdMOpPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGggc3R5bGU9e3sgd2lkdGg6IDEwMCB9fT48L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgPC9UYWJsZS5UaGVhZD5cclxuICAgICAgICAgICAgICAgIDxUYWJsZS5UYm9keT5cclxuICAgICAgICAgICAgICAgICAge3NhbGxlcy5tYXAoKHNhbGxlKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRyIGtleT17c2FsbGUuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmdz17NTAwfT57c2FsbGUubmFtZX08L1RleHQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPntzYWxsZS5kZXNjcmlwdGlvbn08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoIGNvbG9yPXtzYWxsZS5jb2xvcn0gc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+e3NhbGxlLmNvbG9yfTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj17c2FsbGUudHlwZSA9PT0gJ2F0dGVudGUnID8gJ2JsdWUnIDogJ3BpbmsnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJsaWdodFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c2FsbGUudHlwZSA9PT0gJ2F0dGVudGUnID8gJ1NhbGxlIGRcXCdhdHRlbnRlJyA6ICdTYWxsZSBkZSBjb25zdWx0YXRpb24nfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQ+e3NhbGxlLmNhcGFjaXR5fTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxHcm91cCBnYXA9XCJ4c1wiIGp1c3RpZnk9XCJjZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBsYWJlbD1cIk1vZGlmaWVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWN0aW9uSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwic3VidGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3BlblNhbGxlTW9kYWwoc2FsbGUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkVkaXQgc2l6ZT17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjdGlvbkljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGxhYmVsPVwiU3VwcHJpbWVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWN0aW9uSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwic3VidGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJyZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvblRyYXNoIHNpemU9ezE2fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BY3Rpb25JY29uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Hcm91cD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5Ucj5cclxuICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICA8L1RhYmxlLlRib2R5PlxyXG4gICAgICAgICAgICAgIDwvVGFibGU+XHJcbiAgICAgICAgICAgIDwvU2Nyb2xsQXJlYT5cclxuICAgICAgICAgIDwvUGFwZXI+XHJcbiAgICAgICAgPC9UYWJzLlBhbmVsPlxyXG5cclxuICAgICAgICB7LyogT25nbGV0IMOJdsOpbmVtZW50cyAqL31cclxuICAgICAgICA8VGFicy5QYW5lbCB2YWx1ZT1cImV2ZW5lbWVudHNcIiBwdD1cIm1kXCI+XHJcbiAgICAgICAgICA8UGFwZXIgcD1cIm1kXCIgd2l0aEJvcmRlcj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItNFwiPlxyXG4gICAgICAgICAgICAgIDxUaXRsZSBvcmRlcj17NH0+w4l2w6luZW1lbnRzPC9UaXRsZT5cclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICBsZWZ0U2VjdGlvbj17PEljb25QbHVzIHNpemU9ezE2fSAvPn1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5FdmVuZW1lbnRNb2RhbCgpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAgaG92ZXI6YmctYmx1ZS02MDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIE5vdXZlbCDDqXbDqW5lbWVudFxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIDxTY3JvbGxBcmVhPlxyXG4gICAgICAgICAgICAgIDxUYWJsZSBzdHJpcGVkIGhpZ2hsaWdodE9uSG92ZXIgd2l0aFRhYmxlQm9yZGVyPlxyXG4gICAgICAgICAgICAgICAgPFRhYmxlLlRoZWFkPlxyXG4gICAgICAgICAgICAgICAgICA8VGFibGUuVHI+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPlRpdHJlPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGg+TcOpZGVjaW5zPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGg+RGlzcG9uaWJpbGl0w6k8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5Ew6lidXQ8L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5GaW48L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UaD5kZTwvVGFibGUuVGg+XHJcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRoPsOgPC9UYWJsZS5UaD5cclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGggc3R5bGU9e3sgd2lkdGg6IDEwMCB9fT48L1RhYmxlLlRoPlxyXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgPC9UYWJsZS5UaGVhZD5cclxuICAgICAgICAgICAgICAgIDxUYWJsZS5UYm9keT5cclxuICAgICAgICAgICAgICAgICAge2V2ZW5lbWVudHMubWFwKChldmVuZW1lbnQpID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8VGFibGUuVHIga2V5PXtldmVuZW1lbnQuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmdz17NTAwfT57ZXZlbmVtZW50LnRpdGxlfTwvVGV4dD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+e2V2ZW5lbWVudC5tZWRlY2luc308L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj17ZXZlbmVtZW50LmluZGlzcG9uaWJsZSA/ICcjZjUyZGJlJyA6ICcjNGNhZjUwJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9ezIwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD57ZXZlbmVtZW50LmRhdGVEZWJ1dH08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPntldmVuZW1lbnQuZGF0ZUZpbn08L1RhYmxlLlRkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlLlRkPmRlPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZS5UZD7DoDwvVGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGUuVGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxHcm91cCBnYXA9XCJ4c1wiIGp1c3RpZnk9XCJjZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBsYWJlbD1cIk1vZGlmaWVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QWN0aW9uSWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwic3VidGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3BlbkV2ZW5lbWVudE1vZGFsKGV2ZW5lbWVudCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uRWRpdCBzaXplPXsxNn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQWN0aW9uSWNvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgbGFiZWw9XCJTdXBwcmltZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBY3Rpb25JY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzdWJ0bGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cInJlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uVHJhc2ggc2l6ZT17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0FjdGlvbkljb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0dyb3VwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZS5UZD5cclxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlLlRyPlxyXG4gICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgIDwvVGFibGUuVGJvZHk+XHJcbiAgICAgICAgICAgICAgPC9UYWJsZT5cclxuICAgICAgICAgICAgPC9TY3JvbGxBcmVhPlxyXG4gICAgICAgICAgPC9QYXBlcj5cclxuICAgICAgICA8L1RhYnMuUGFuZWw+XHJcbiAgICAgIDwvVGFicz5cclxuXHJcbiAgICAgIHsvKiBNb2RhbCBwb3VyIE1vdGlmICovfVxyXG4gICAgICA8TW9kYWxcclxuICAgICAgICBvcGVuZWQ9e21vdGlmTW9kYWxPcGVuZWR9XHJcbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0TW90aWZNb2RhbE9wZW5lZChmYWxzZSl9XHJcbiAgICAgICAgdGl0bGU9e1xyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiAtbS00IG1iLTRcIj5cclxuICAgICAgICAgICAgPEljb25DYWxlbmRhciBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgPFRleHQgZnc9ezYwMH0+QWN0ZXM8L1RleHQ+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICB9XHJcbiAgICAgICAgc2l6ZT1cImxnXCJcclxuICAgICAgICB3aXRoQ2xvc2VCdXR0b249e2ZhbHNlfVxyXG4gICAgICA+XHJcbiAgICAgICAgPFN0YWNrIGdhcD1cIm1kXCI+XHJcbiAgICAgICAgICA8R3JpZD5cclxuICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgICAgIGxhYmVsPXs8c3Bhbj5Db2RlIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+PC9zcGFuPn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXttb3RpZkZvcm0uY29kZX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TW90aWZGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgY29kZTogZS50YXJnZXQudmFsdWUgfSkpfVxyXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICAgICAgbGFiZWw9ezxzcGFuPkRlc2NyaXB0aW9uIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+PC9zcGFuPn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXttb3RpZkZvcm0uZGVzY3JpcHRpb259XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE1vdGlmRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KSl9XHJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICBsYWJlbD17PHNwYW4+RHVyw6llIChtaW4pIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+PC9zcGFuPn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXttb3RpZkZvcm0uZHVyYXRpb24udG9TdHJpbmcoKX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TW90aWZGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZHVyYXRpb246IHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB8fCAxNSB9KSl9XHJcbiAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcclxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezN9PlxyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIiBmdz17NTAwfSBtYj1cInhzXCI+Q291bGV1cjwvVGV4dD5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoXHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I9e21vdGlmRm9ybS5jb2xvcn1cclxuICAgICAgICAgICAgICAgICAgICBzaXplPXszMH1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBib3JkZXIgYm9yZGVyLWdyYXktMzAwXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17M30+XHJcbiAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGZ3PXs1MDB9IG1iPVwieHNcIj5Db3VsZXVyIHJhecOpZTwvVGV4dD5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgPENvbG9yU3dhdGNoXHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I9e21vdGlmRm9ybS5jb2xvclJheWVlfVxyXG4gICAgICAgICAgICAgICAgICAgIHNpemU9ezMwfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGJvcmRlciBib3JkZXItZ3JheS0zMDBcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkFnZW5kYSBwYXIgZMOpZmF1dFwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17bW90aWZGb3JtLmFnZW5kYURlZmF1dH1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsodmFsdWUpID0+IHNldE1vdGlmRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIGFnZW5kYURlZmF1dDogdmFsdWUgfHwgJycgfSkpfVxyXG4gICAgICAgICAgICAgICAgZGF0YT17YWdlbmRhRGVmYXV0T3B0aW9uc31cclxuICAgICAgICAgICAgICAgIHJpZ2h0U2VjdGlvbj17PEljb25QbHVzIHNpemU9ezE2fSBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciB0ZXh0LWJsdWUtNTAwXCIgLz59XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgIDxTZWxlY3RcclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiU2VydmljZXMgZMOpc2lnbsOpc1wiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17bW90aWZGb3JtLnNlcnZpY2VzRGVzaWduZXN9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRNb3RpZkZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBzZXJ2aWNlc0Rlc2lnbmVzOiB2YWx1ZSB8fCAnJyB9KSl9XHJcbiAgICAgICAgICAgICAgICBkYXRhPXtzZXJ2aWNlc0Rlc2lnbmVzT3B0aW9uc31cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB0LTQgYm9yZGVyLXRcIj5cclxuICAgICAgICAgICAgPFN3aXRjaFxyXG4gICAgICAgICAgICAgIGNoZWNrZWQ9e21vdGlmRm9ybS5jb3VsZXVyU29tYnJlfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TW90aWZGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgY291bGV1clNvbWJyZTogZS5jdXJyZW50VGFyZ2V0LmNoZWNrZWQgfSkpfVxyXG4gICAgICAgICAgICAgIGxhYmVsPVwiQ291bGV1ciBzb21icmVcIlxyXG4gICAgICAgICAgICAgIGxhYmVsUG9zaXRpb249XCJyaWdodFwiXHJcbiAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICA8R3JvdXA+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImZpbGxlZFwiXHJcbiAgICAgICAgICAgICAgICBjb2xvcj1cImdyYXlcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0TW90aWZNb2RhbE9wZW5lZChmYWxzZSl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgQW5udWxlclxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJmaWxsZWRcIlxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNhdmVNb3RpZn1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshbW90aWZGb3JtLmNvZGUgfHwgIW1vdGlmRm9ybS5kZXNjcmlwdGlvbn1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBFbnJlZ2lzdHJlclxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L0dyb3VwPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9TdGFjaz5cclxuICAgICAgPC9Nb2RhbD5cclxuXHJcbiAgICAgIHsvKiBNb2RhbCBwb3VyIEFnZW5kYSAqL31cclxuICAgICAgPE1vZGFsXHJcbiAgICAgICAgb3BlbmVkPXthZ2VuZGFNb2RhbE9wZW5lZH1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRBZ2VuZGFNb2RhbE9wZW5lZChmYWxzZSl9XHJcbiAgICAgICAgdGl0bGU9e1xyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiAtbS00IG1iLTRcIj5cclxuICAgICAgICAgICAgPEljb25DYWxlbmRhciBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgPFRleHQgZnc9ezYwMH0+QWdlbmRhPC9UZXh0PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgd2l0aENsb3NlQnV0dG9uPXtmYWxzZX1cclxuICAgICAgPlxyXG4gICAgICAgIDxTdGFjayBnYXA9XCJtZFwiPlxyXG4gICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs4fT5cclxuICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICBsYWJlbD17PHNwYW4+Tm9tIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+PC9zcGFuPn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXthZ2VuZGFGb3JtLm5hbWV9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEFnZW5kYUZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XHJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs0fT5cclxuICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0gbWI9XCJ4c1wiPkNvdWxldXI8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxDb2xvclN3YXRjaFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yPXthZ2VuZGFGb3JtLmNvbG9yfVxyXG4gICAgICAgICAgICAgICAgICAgIHNpemU9ezMwfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGJvcmRlciBib3JkZXItZ3JheS0zMDBcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkRlc2NyaXB0aW9uXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXthZ2VuZGFGb3JtLmRlc2NyaXB0aW9ufVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRBZ2VuZGFGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgbGFiZWw9XCJTZXJ2aWNlcyBkw6lzaWduw6lzXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXthZ2VuZGFGb3JtLnNlcnZpY2VzRGVzaWduZXN9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlKSA9PiBzZXRBZ2VuZGFGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgc2VydmljZXNEZXNpZ25lczogdmFsdWUgfHwgJycgfSkpfVxyXG4gICAgICAgICAgICAgICAgZGF0YT17c2VydmljZXNEZXNpZ25lc09wdGlvbnN9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC00IGJvcmRlci10XCI+XHJcbiAgICAgICAgICAgIDxTd2l0Y2hcclxuICAgICAgICAgICAgICBjaGVja2VkPXthZ2VuZGFGb3JtLmlzUmVzb3VyY2V9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRBZ2VuZGFGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgaXNSZXNvdXJjZTogZS5jdXJyZW50VGFyZ2V0LmNoZWNrZWQgfSkpfVxyXG4gICAgICAgICAgICAgIGxhYmVsPVwiQWZmaWNoZXIgY29tbWUgcmVzc291cmNlXCJcclxuICAgICAgICAgICAgICBsYWJlbFBvc2l0aW9uPVwicmlnaHRcIlxyXG4gICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgPEdyb3VwPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJmaWxsZWRcIlxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJncmF5XCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFnZW5kYU1vZGFsT3BlbmVkKGZhbHNlKX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBBbm51bGVyXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImZpbGxlZFwiXHJcbiAgICAgICAgICAgICAgICBjb2xvcj1cImJsdWVcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2F2ZUFnZW5kYX1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshYWdlbmRhRm9ybS5uYW1lfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIEVucmVnaXN0cmVyXHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvR3JvdXA+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L1N0YWNrPlxyXG4gICAgICA8L01vZGFsPlxyXG5cclxuICAgICAgey8qIE1vZGFsIHBvdXIgU2FsbGUgKi99XHJcbiAgICAgIDxNb2RhbFxyXG4gICAgICAgIG9wZW5lZD17c2FsbGVNb2RhbE9wZW5lZH1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTYWxsZU1vZGFsT3BlbmVkKGZhbHNlKX1cclxuICAgICAgICB0aXRsZT17XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcHgtNCBweS0yIC1tLTQgbWItNFwiPlxyXG4gICAgICAgICAgICA8SWNvbkJ1aWxkaW5nIHNpemU9ezIwfSAvPlxyXG4gICAgICAgICAgICA8VGV4dCBmdz17NjAwfT5Bam91dCBkdSBzYWxsZTwvVGV4dD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIH1cclxuICAgICAgICBzaXplPVwibGdcIlxyXG4gICAgICAgIHdpdGhDbG9zZUJ1dHRvbj17ZmFsc2V9XHJcbiAgICAgID5cclxuICAgICAgICA8U3RhY2sgZ2FwPVwibWRcIj5cclxuICAgICAgICAgIDxHcmlkPlxyXG4gICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICAgICAgbGFiZWw9ezxzcGFuPk5vbSA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDBcIj4qPC9zcGFuPjwvc3Bhbj59XHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2FsbGVGb3JtLm5hbWV9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNhbGxlRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIG5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cclxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezN9PlxyXG4gICAgICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgICAgIGxhYmVsPXs8c3Bhbj5DYXBhY2l0w6kgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+Kjwvc3Bhbj48L3NwYW4+fVxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3NhbGxlRm9ybS5jYXBhY2l0eS50b1N0cmluZygpfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTYWxsZUZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBjYXBhY2l0eTogcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpIHx8IDEgfSkpfVxyXG4gICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXszfT5cclxuICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0gbWI9XCJ4c1wiPkNvdWxldXI8L1RleHQ+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxDb2xvclN3YXRjaFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yPXtzYWxsZUZvcm0uY29sb3J9XHJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT17MzB9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMFwiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9HcmlkLkNvbD5cclxuICAgICAgICAgIDwvR3JpZD5cclxuXHJcbiAgICAgICAgICA8R3JpZD5cclxuICAgICAgICAgICAgPEdyaWQuQ29sIHNwYW49ezZ9PlxyXG4gICAgICAgICAgICAgIDxUZXh0SW5wdXRcclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiRGVzY3JpcHRpb25cIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3NhbGxlRm9ybS5kZXNjcmlwdGlvbn1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2FsbGVGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgPFNlbGVjdFxyXG4gICAgICAgICAgICAgICAgbGFiZWw9XCJTZXJ2aWNlcyBkw6lzaWduw6lzXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXtzYWxsZUZvcm0uc2VydmljZXNEZXNpZ25lc31cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsodmFsdWUpID0+IHNldFNhbGxlRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIHNlcnZpY2VzRGVzaWduZXM6IHZhbHVlIHx8ICcnIH0pKX1cclxuICAgICAgICAgICAgICAgIGRhdGE9e3NlcnZpY2VzRGVzaWduZXNPcHRpb25zfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvR3JpZC5Db2w+XHJcbiAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCIgZnc9ezUwMH0gbWI9XCJ4c1wiPlR5cGU8L1RleHQ+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICB0eXBlPVwicmFkaW9cIlxyXG4gICAgICAgICAgICAgICAgICBpZD1cInNhbGxlLWF0dGVudGVcIlxyXG4gICAgICAgICAgICAgICAgICBuYW1lPVwic2FsbGVUeXBlXCJcclxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c2FsbGVGb3JtLnR5cGUgPT09ICdhdHRlbnRlJ31cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eygpID0+IHNldFNhbGxlRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIHR5cGU6ICdhdHRlbnRlJyB9KSl9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJzYWxsZS1hdHRlbnRlXCI+U2FsbGUgZCdhdHRlbnRlPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cInJhZGlvXCJcclxuICAgICAgICAgICAgICAgICAgaWQ9XCJzYWxsZS1jb25zdWx0YXRpb25cIlxyXG4gICAgICAgICAgICAgICAgICBuYW1lPVwic2FsbGVUeXBlXCJcclxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c2FsbGVGb3JtLnR5cGUgPT09ICdjb25zdWx0YXRpb24nfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KCkgPT4gc2V0U2FsbGVGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgdHlwZTogJ2NvbnN1bHRhdGlvbicgfSkpfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwic2FsbGUtY29uc3VsdGF0aW9uXCI+U2FsbGUgZGUgY29uc3VsdGF0aW9uPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktZW5kIHB0LTQgYm9yZGVyLXRcIj5cclxuICAgICAgICAgICAgPEdyb3VwPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJmaWxsZWRcIlxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJyZWRcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2FsbGVNb2RhbE9wZW5lZChmYWxzZSl9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgQW5udWxlclxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJmaWxsZWRcIlxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshc2FsbGVGb3JtLm5hbWV9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgRW5yZWdpc3RyZXJcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9Hcm91cD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvU3RhY2s+XHJcbiAgICAgIDwvTW9kYWw+XHJcblxyXG4gICAgICB7LyogTW9kYWwgcG91ciDDiXbDqW5lbWVudCAqL31cclxuICAgICAgPE1vZGFsXHJcbiAgICAgICAgb3BlbmVkPXtldmVuZW1lbnRNb2RhbE9wZW5lZH1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRFdmVuZW1lbnRNb2RhbE9wZW5lZChmYWxzZSl9XHJcbiAgICAgICAgdGl0bGU9e1xyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiAtbS00IG1iLTRcIj5cclxuICAgICAgICAgICAgPEljb25DYWxlbmRhciBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgPFRleHQgZnc9ezYwMH0+R8OpcmVyIMOpdsOpbmVtZW50PC9UZXh0PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgd2l0aENsb3NlQnV0dG9uPXtmYWxzZX1cclxuICAgICAgPlxyXG4gICAgICAgIDxTdGFjayBnYXA9XCJtZFwiPlxyXG4gICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICBsYWJlbD17PHNwYW4+VGl0cmUgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+Kjwvc3Bhbj48L3NwYW4+fVxyXG4gICAgICAgICAgICB2YWx1ZT17ZXZlbmVtZW50Rm9ybS50aXRsZX1cclxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFdmVuZW1lbnRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgdGl0bGU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cclxuICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgPEdyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkLkNvbCBzcGFuPXs2fT5cclxuICAgICAgICAgICAgICA8VGV4dElucHV0XHJcbiAgICAgICAgICAgICAgICBsYWJlbD17PHNwYW4+RGF0ZSBkw6lidXQgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwXCI+Kjwvc3Bhbj48L3NwYW4+fVxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e2V2ZW5lbWVudEZvcm0uZGF0ZURlYnV0fVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFdmVuZW1lbnRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZGF0ZURlYnV0OiBlLnRhcmdldC52YWx1ZSB9KSl9XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkREL01NL1lZWVlcIlxyXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgICA8R3JpZC5Db2wgc3Bhbj17Nn0+XHJcbiAgICAgICAgICAgICAgPFRleHRJbnB1dFxyXG4gICAgICAgICAgICAgICAgbGFiZWw9ezxzcGFuPkRhdGUgZmluIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMFwiPio8L3NwYW4+PC9zcGFuPn1cclxuICAgICAgICAgICAgICAgIHZhbHVlPXtldmVuZW1lbnRGb3JtLmRhdGVGaW59XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEV2ZW5lbWVudEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBkYXRlRmluOiBlLnRhcmdldC52YWx1ZSB9KSl9XHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkREL01NL1lZWVlcIlxyXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0dyaWQuQ29sPlxyXG4gICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiIGZ3PXs1MDB9IG1iPVwieHNcIj5Db3VsZXVyPC9UZXh0PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgPENvbG9yU3dhdGNoXHJcbiAgICAgICAgICAgICAgICBjb2xvcj17ZXZlbmVtZW50Rm9ybS5jb2xvcn1cclxuICAgICAgICAgICAgICAgIHNpemU9ezMwfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMFwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC00XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTQgaC00IHJvdW5kZWQtZnVsbCBjdXJzb3ItcG9pbnRlciAke2V2ZW5lbWVudEZvcm0uaW5kaXNwb25pYmxlID8gJ2JnLXJlZC01MDAnIDogJ2JnLWdyYXktMzAwJ31gfVxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RXZlbmVtZW50Rm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIGluZGlzcG9uaWJsZTogIXByZXYuaW5kaXNwb25pYmxlIH0pKX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxUZXh0IHNpemU9XCJzbVwiPkluZGlzcG9uaWJsZTwvVGV4dD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTQgaC00IHJvdW5kZWQtZnVsbCBjdXJzb3ItcG9pbnRlciAke2V2ZW5lbWVudEZvcm0ucGVybWFuZW50ID8gJ2JnLWdyYXktNTAwJyA6ICdiZy1ncmF5LTMwMCd9YH1cclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEV2ZW5lbWVudEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBwZXJtYW5lbnQ6ICFwcmV2LnBlcm1hbmVudCB9KSl9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8VGV4dCBzaXplPVwic21cIj5QZXJtYW5lbnQ8L1RleHQ+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy00IGgtNCByb3VuZGVkLWZ1bGwgY3Vyc29yLXBvaW50ZXIgJHtldmVuZW1lbnRGb3JtLnRvdXRlSm91cm5lZSA/ICdiZy1ibHVlLTUwMCcgOiAnYmctZ3JheS0zMDAnfWB9XHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRFdmVuZW1lbnRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgdG91dGVKb3VybmVlOiAhcHJldi50b3V0ZUpvdXJuZWUgfSkpfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+VG91dGUgbGEgam91cm7DqWU8L1RleHQ+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy00IGgtNCByb3VuZGVkLWZ1bGwgY3Vyc29yLXBvaW50ZXIgJHtldmVuZW1lbnRGb3JtLnRvdXNMZXNKb3VycyA/ICdiZy1ibHVlLTUwMCcgOiAnYmctZ3JheS0zMDAnfWB9XHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRFdmVuZW1lbnRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgdG91c0xlc0pvdXJzOiAhcHJldi50b3VzTGVzSm91cnMgfSkpfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+VG91cyBsZXMgam91cnM8L1RleHQ+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy00IGgtNCByb3VuZGVkLWZ1bGwgY3Vyc29yLXBvaW50ZXIgJHtldmVuZW1lbnRGb3JtLmNhYmluZXQgPyAnYmctYmx1ZS01MDAnIDogJ2JnLWdyYXktMzAwJ31gfVxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RXZlbmVtZW50Rm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIGNhYmluZXQ6ICFwcmV2LmNhYmluZXQgfSkpfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPFRleHQgc2l6ZT1cInNtXCI+Q2FiaW5ldDwvVGV4dD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktZW5kIHB0LTQgYm9yZGVyLXRcIj5cclxuICAgICAgICAgICAgPEdyb3VwPlxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJmaWxsZWRcIlxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJyZWRcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RXZlbmVtZW50TW9kYWxPcGVuZWQoZmFsc2UpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIEFubnVsZXJcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZmlsbGVkXCJcclxuICAgICAgICAgICAgICAgIGNvbG9yPVwiYmx1ZVwiXHJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWV2ZW5lbWVudEZvcm0udGl0bGV9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgRW5yZWdpc3RyZXJcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9Hcm91cD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvU3RhY2s+XHJcbiAgICAgIDwvTW9kYWw+XHJcbiAgICA8L1N0YWNrPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBBY2N1ZWlsRXRBZ2VuZGE7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiU3RhY2siLCJUaXRsZSIsIlRhYnMiLCJQYXBlciIsIkdyaWQiLCJUZXh0IiwiVGV4dElucHV0IiwiU2VsZWN0IiwiU3dpdGNoIiwiQnV0dG9uIiwiTW9kYWwiLCJUYWJsZSIsIkFjdGlvbkljb24iLCJHcm91cCIsIkNoZWNrYm94IiwiVG9vbHRpcCIsIlNjcm9sbEFyZWEiLCJCYWRnZSIsIkNvbG9yU3dhdGNoIiwiSWNvblBsdXMiLCJJY29uRWRpdCIsIkljb25UcmFzaCIsIkljb25TZWFyY2giLCJJY29uQ2FsZW5kYXIiLCJJY29uU2V0dGluZ3MiLCJJY29uSG9tZSIsIkljb25Db2xvclBpY2tlciIsIkljb25YIiwiSWNvbkJ1aWxkaW5nIiwiQWNjdWVpbEV0QWdlbmRhIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwibW90aWZNb2RhbE9wZW5lZCIsInNldE1vdGlmTW9kYWxPcGVuZWQiLCJhZ2VuZGFNb2RhbE9wZW5lZCIsInNldEFnZW5kYU1vZGFsT3BlbmVkIiwic2FsbGVNb2RhbE9wZW5lZCIsInNldFNhbGxlTW9kYWxPcGVuZWQiLCJldmVuZW1lbnRNb2RhbE9wZW5lZCIsInNldEV2ZW5lbWVudE1vZGFsT3BlbmVkIiwiZWRpdGluZ01vdGlmIiwic2V0RWRpdGluZ01vdGlmIiwiZWRpdGluZ0FnZW5kYSIsInNldEVkaXRpbmdBZ2VuZGEiLCJlZGl0aW5nU2FsbGUiLCJzZXRFZGl0aW5nU2FsbGUiLCJlZGl0aW5nRXZlbmVtZW50Iiwic2V0RWRpdGluZ0V2ZW5lbWVudCIsIm1vdGlmRm9ybSIsInNldE1vdGlmRm9ybSIsImNvZGUiLCJkZXNjcmlwdGlvbiIsImR1cmF0aW9uIiwiY29sb3IiLCJjb2xvclJheWVlIiwiYWdlbmRhRGVmYXV0Iiwic2VydmljZXNEZXNpZ25lcyIsImNvdWxldXJTb21icmUiLCJhZ2VuZGFGb3JtIiwic2V0QWdlbmRhRm9ybSIsIm5hbWUiLCJpc1Jlc291cmNlIiwic2FsbGVGb3JtIiwic2V0U2FsbGVGb3JtIiwidHlwZSIsImNhcGFjaXR5IiwiZXZlbmVtZW50Rm9ybSIsInNldEV2ZW5lbWVudEZvcm0iLCJ0aXRsZSIsImRhdGVEZWJ1dCIsImRhdGVGaW4iLCJpbmRpc3BvbmlibGUiLCJwZXJtYW5lbnQiLCJ0b3V0ZUpvdXJuZWUiLCJ0b3VzTGVzSm91cnMiLCJjYWJpbmV0IiwiZ2VuZXJhbFNldHRpbmdzIiwic2V0R2VuZXJhbFNldHRpbmdzIiwiaGV1cmVPdXZlcnR1cmUiLCJoZXVyZUZlcm1ldHVyZSIsInByZW1pZXJKb3VyU2VtYWluZSIsInZ1ZURlZmF1dENhbGVuZHJpZXIiLCJtaW5pbXVtSW50ZXJ2YWxsZSIsImZ1c2VhdUhvcmFpcmUiLCJjYWNoZXJKb3Vyc0Zlcm1ldHVyZSIsImN1cnNldXJBZ2VuZGFIZXVyZUN1cnJlbnQiLCJmaWx0cmUyNGhDYWJpbmV0IiwidXRpbGlzZXJBYmJyZXZpYXRpb24iLCJ1dGlsaXNlckxpc3RlUGF0aWVudHMiLCJhZmZpY2hlclNldWxlbWVudFZpc2l0ZXNUZXJtaW5lZXMiLCJtb3RpZnMiLCJzZXRNb3RpZnMiLCJpZCIsImFnZW5kYXMiLCJzZXRBZ2VuZGFzIiwiZG9jdGV1cnMiLCJzZXREb2N0ZXVycyIsInNhbGxlcyIsInNldFNhbGxlcyIsImV2ZW5lbWVudHMiLCJzZXRFdmVuZW1lbnRzIiwibWVkZWNpbnMiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsImZpbHRlcmVkTW90aWZzIiwiZmlsdGVyIiwibW90aWYiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwiam91cnNTZW1haW5lIiwidnVlQ2FsZW5kcmllck9wdGlvbnMiLCJ2YWx1ZSIsImxhYmVsIiwiaW50ZXJ2YWxsZU9wdGlvbnMiLCJmdXNlYXVIb3JhaXJlT3B0aW9ucyIsImFnZW5kYURlZmF1dE9wdGlvbnMiLCJzZXJ2aWNlc0Rlc2lnbmVzT3B0aW9ucyIsIm9wZW5Nb3RpZk1vZGFsIiwib3BlbkFnZW5kYU1vZGFsIiwiYWdlbmRhIiwib3BlblNhbGxlTW9kYWwiLCJzYWxsZSIsIm9wZW5FdmVuZW1lbnRNb2RhbCIsImV2ZW5lbWVudCIsImhhbmRsZVNhdmVNb3RpZiIsInByZXYiLCJtYXAiLCJtIiwibmV3TW90aWYiLCJEYXRlIiwibm93IiwiaGFuZGxlU2F2ZUFnZW5kYSIsImEiLCJuZXdBZ2VuZGEiLCJoYW5kbGVEZWxldGVNb3RpZiIsImdhcCIsImNsYXNzTmFtZSIsIm9yZGVyIiwic2l6ZSIsIm9uQ2hhbmdlIiwidmFyaWFudCIsIkxpc3QiLCJUYWIiLCJsZWZ0U2VjdGlvbiIsIlBhbmVsIiwicHQiLCJwIiwid2l0aEJvcmRlciIsImRpdiIsIkNvbCIsInNwYW4iLCJmdyIsIm1iIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwiam91ciIsImRhdGEiLCJtdCIsImNoZWNrZWQiLCJjdXJyZW50VGFyZ2V0Iiwib25DbGljayIsInN0cmlwZWQiLCJoaWdobGlnaHRPbkhvdmVyIiwid2l0aFRhYmxlQm9yZGVyIiwiVGhlYWQiLCJUciIsIlRoIiwic3R5bGUiLCJ3aWR0aCIsIlRib2R5IiwiVGQiLCJqdXN0aWZ5IiwiZCIsInRvU3RyaW5nIiwiZG9jdGV1ciIsImxlbmd0aCIsImNvbFNwYW4iLCJvcGVuZWQiLCJvbkNsb3NlIiwid2l0aENsb3NlQnV0dG9uIiwicmVxdWlyZWQiLCJwYXJzZUludCIsInJpZ2h0U2VjdGlvbiIsImxhYmVsUG9zaXRpb24iLCJkaXNhYmxlZCIsImlucHV0IiwiaHRtbEZvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parameters_de_base/AccueilEtAgenda.tsx\n"));

/***/ })

});