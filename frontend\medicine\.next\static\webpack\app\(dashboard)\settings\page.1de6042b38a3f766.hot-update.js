"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/PlanDeSoins.tsx":
/*!********************************************************************************************!*\
  !*** ./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/PlanDeSoins.tsx ***!
  \********************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Tabs/Tabs.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/TextInput/TextInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Modal/Modal.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Group/Group.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Select/Select.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ActionIcon/ActionIcon.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/ColorPicker/ColorPicker.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/NumberInput/NumberInput.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Divider/Divider.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Checkbox/Checkbox.mjs\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Textarea/Textarea.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconPlus.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconHeart.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSearch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconUsers.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconStethoscope.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=IconColorPicker,IconHeart,IconPlus,IconSearch,IconStethoscope,IconUsers!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconColorPicker.mjs\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mantine/hooks */ \"(app-pages-browser)/./node_modules/@mantine/hooks/esm/use-disclosure/use-disclosure.mjs\");\n/* harmony import */ var _mantine_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mantine/form */ \"(app-pages-browser)/./node_modules/@mantine/form/esm/use-form.mjs\");\n/* harmony import */ var mantine_datatable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mantine-datatable */ \"(app-pages-browser)/./node_modules/mantine-datatable/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PlanDeSoins = ()=>{\n    _s();\n    // États pour les onglets et modales\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [protocoleModalOpened, { open: openProtocoleModal, close: closeProtocoleModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [acteSoinModalOpened, { open: openActeSoinModal, close: closeActeSoinModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [groupeActeModalOpened, { open: openGroupeActeModal, close: closeGroupeActeModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [procedureModalOpened, { open: openProcedureModal, close: closeProcedureModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [acteModalOpened, { open: openActeModal, close: closeActeModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    const [modaliteModalOpened, { open: openModaliteModal, close: closeModaliteModal }] = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)(false);\n    // États pour les données\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [protocolesSoin, setProtocolesSoin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [actesSoin, setActesSoin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [groupesActe, setGroupesActe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [actes, setActes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modalites, setModalites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Formulaires\n    const protocoleForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            titre: '',\n            groupes: []\n        },\n        validate: {\n            titre: {\n                \"PlanDeSoins.useForm[protocoleForm]\": (value)=>!value ? 'Le titre est requis' : null\n            }[\"PlanDeSoins.useForm[protocoleForm]\"]\n        }\n    });\n    const acteSoinForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            titre: '',\n            groupeActe: '',\n            acte: '',\n            motif: '',\n            couleur: '#3b82f6'\n        },\n        validate: {\n            titre: {\n                \"PlanDeSoins.useForm[acteSoinForm]\": (value)=>!value ? 'Le titre est requis' : null\n            }[\"PlanDeSoins.useForm[acteSoinForm]\"],\n            groupeActe: {\n                \"PlanDeSoins.useForm[acteSoinForm]\": (value)=>!value ? 'Le groupe d\\'acte est requis' : null\n            }[\"PlanDeSoins.useForm[acteSoinForm]\"],\n            acte: {\n                \"PlanDeSoins.useForm[acteSoinForm]\": (value)=>!value ? 'L\\'acte est requis' : null\n            }[\"PlanDeSoins.useForm[acteSoinForm]\"],\n            motif: {\n                \"PlanDeSoins.useForm[acteSoinForm]\": (value)=>!value ? 'Le motif est requis' : null\n            }[\"PlanDeSoins.useForm[acteSoinForm]\"]\n        }\n    });\n    const groupeActeForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            titre: '',\n            couleur: '#3b82f6'\n        },\n        validate: {\n            titre: {\n                \"PlanDeSoins.useForm[groupeActeForm]\": (value)=>!value ? 'Le titre est requis' : null\n            }[\"PlanDeSoins.useForm[groupeActeForm]\"]\n        }\n    });\n    const procedureForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            code: '',\n            nom: '',\n            honoraire: 0,\n            servicesDesignes: '',\n            codeNGAP: '',\n            codeCCAM: '',\n            tnr: '',\n            modalite: '',\n            remboursable: false\n        },\n        validate: {\n            code: {\n                \"PlanDeSoins.useForm[procedureForm]\": (value)=>!value ? 'Le code est requis' : null\n            }[\"PlanDeSoins.useForm[procedureForm]\"],\n            nom: {\n                \"PlanDeSoins.useForm[procedureForm]\": (value)=>!value ? 'Le nom est requis' : null\n            }[\"PlanDeSoins.useForm[procedureForm]\"]\n        }\n    });\n    const acteForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            code: '',\n            description: '',\n            duree: 15,\n            couleur: '#3b82f6',\n            couleurRayee: '#ef4444',\n            agendaParDefaut: '',\n            servicesDesignes: '',\n            couleurSombre: false\n        },\n        validate: {\n            code: {\n                \"PlanDeSoins.useForm[acteForm]\": (value)=>!value ? 'Le code est requis' : null\n            }[\"PlanDeSoins.useForm[acteForm]\"],\n            description: {\n                \"PlanDeSoins.useForm[acteForm]\": (value)=>!value ? 'La description est requise' : null\n            }[\"PlanDeSoins.useForm[acteForm]\"]\n        }\n    });\n    const modaliteForm = (0,_mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        initialValues: {\n            valeur: '',\n            description: ''\n        },\n        validate: {\n            valeur: {\n                \"PlanDeSoins.useForm[modaliteForm]\": (value)=>!value ? 'La valeur est requise' : null\n            }[\"PlanDeSoins.useForm[modaliteForm]\"]\n        }\n    });\n    // Gestionnaires pour les onglets\n    const handleTabChange = (value)=>{\n        if (value) setActiveTab(value);\n    };\n    // Gestionnaires de soumission\n    const handleProtocoleSubmit = (values)=>{\n        const newProtocole = {\n            id: Date.now().toString(),\n            titre: values.titre,\n            description: ''\n        };\n        setProtocolesSoin((prev)=>[\n                ...prev,\n                newProtocole\n            ]);\n        closeProtocoleModal();\n        protocoleForm.reset();\n    };\n    const handleActeSoinSubmit = (values)=>{\n        const newActeSoin = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setActesSoin((prev)=>[\n                ...prev,\n                newActeSoin\n            ]);\n        closeActeSoinModal();\n        acteSoinForm.reset();\n    };\n    const handleGroupeActeSubmit = (values)=>{\n        const newGroupeActe = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setGroupesActe((prev)=>[\n                ...prev,\n                newGroupeActe\n            ]);\n        closeGroupeActeModal();\n        groupeActeForm.reset();\n    };\n    const handleProcedureSubmit = (values)=>{\n        const newProcedure = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setProcedures((prev)=>[\n                ...prev,\n                newProcedure\n            ]);\n        closeProcedureModal();\n        procedureForm.reset();\n    };\n    const handleActeSubmit = (values)=>{\n        const newActe = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setActes((prev)=>[\n                ...prev,\n                newActe\n            ]);\n        closeActeModal();\n        acteForm.reset();\n    };\n    const handleModaliteSubmit = (values)=>{\n        const newModalite = {\n            id: Date.now().toString(),\n            ...values\n        };\n        setModalites((prev)=>[\n                ...prev,\n                newModalite\n            ]);\n        closeModaliteModal();\n        modaliteForm.reset();\n    };\n    // Fonction pour obtenir le bouton d'action selon l'onglet actif\n    const getActionButton = ()=>{\n        switch(activeTab){\n            case 'protocoles-soin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 26\n                    }, void 0),\n                    onClick: openProtocoleModal,\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    children: \"Protocole de soins\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, undefined);\n            case 'actes-soin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 26\n                    }, void 0),\n                    onClick: openActeSoinModal,\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    children: \"Acte de soins\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 319,\n                    columnNumber: 11\n                }, undefined);\n            case 'groupes-actes':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 26\n                    }, void 0),\n                    onClick: openGroupeActeModal,\n                    className: \"bg-blue-500 hover:bg-blue-600\",\n                    children: \"Groupe d'acte\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    // Colonnes pour les tables\n    const protocoleColumns = [\n        {\n            accessor: 'titre',\n            title: 'Titre'\n        },\n        {\n            accessor: 'description',\n            title: 'Description'\n        }\n    ];\n    const acteSoinColumns = [\n        {\n            accessor: 'titre',\n            title: 'Titre'\n        },\n        {\n            accessor: 'groupeActe',\n            title: 'Groupe d\\'acte'\n        },\n        {\n            accessor: 'couleur',\n            title: 'Couleur',\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 rounded\",\n                        style: {\n                            backgroundColor: record.couleur\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 7\n                }, undefined)\n        },\n        {\n            accessor: 'acte',\n            title: 'Acte'\n        },\n        {\n            accessor: 'motif',\n            title: 'Motif'\n        }\n    ];\n    const groupeActeColumns = [\n        {\n            accessor: 'titre',\n            title: 'Titre'\n        },\n        {\n            accessor: 'couleur',\n            title: 'Couleur',\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 rounded\",\n                        style: {\n                            backgroundColor: record.couleur\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 7\n                }, undefined)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-gray-50 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 32,\n                                className: \"text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-800\",\n                                children: \"Param\\xe9trage des plan de soins\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, undefined),\n                    getActionButton()\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                shadow: \"sm\",\n                padding: \"lg\",\n                radius: \"md\",\n                withBorder: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                    value: activeTab,\n                    onChange: handleTabChange,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.List, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Tab, {\n                                    value: \"general\",\n                                    children: \"G\\xe9n\\xe9ral\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Tab, {\n                                    value: \"protocoles-soin\",\n                                    children: \"Protocoles de soin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Tab, {\n                                    value: \"actes-soin\",\n                                    children: \"Actes de soin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Tab, {\n                                    value: \"groupes-actes\",\n                                    children: \"Groupes d'actes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Panel, {\n                            value: \"general\",\n                            pt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                        placeholder: \"Rechercher\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        value: searchQuery,\n                                        onChange: (event)=>setSearchQuery(event.currentTarget.value),\n                                        className: \"w-96\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                    columns: [\n                                        {\n                                            accessor: 'titre',\n                                            title: 'Titre'\n                                        },\n                                        {\n                                            accessor: 'description',\n                                            title: 'Description'\n                                        }\n                                    ],\n                                    records: [],\n                                    noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\",\n                                    minHeight: 200\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Panel, {\n                            value: \"protocoles-soin\",\n                            pt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                        placeholder: \"Rechercher\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        value: searchQuery,\n                                        onChange: (event)=>setSearchQuery(event.currentTarget.value),\n                                        className: \"w-96\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                    columns: protocoleColumns,\n                                    records: protocolesSoin,\n                                    noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\",\n                                    minHeight: 200\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Panel, {\n                            value: \"actes-soin\",\n                            pt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                        placeholder: \"Rechercher\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        value: searchQuery,\n                                        onChange: (event)=>setSearchQuery(event.currentTarget.value),\n                                        className: \"w-96\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                    columns: acteSoinColumns,\n                                    records: actesSoin,\n                                    noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\",\n                                    minHeight: 200\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Tabs.Panel, {\n                            value: \"groupes-actes\",\n                            pt: \"md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                        placeholder: \"Rechercher\",\n                                        leftSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 30\n                                        }, void 0),\n                                        value: searchQuery,\n                                        onChange: (event)=>setSearchQuery(event.currentTarget.value),\n                                        className: \"w-96\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(mantine_datatable__WEBPACK_IMPORTED_MODULE_2__.DataTable, {\n                                    columns: groupeActeColumns,\n                                    records: groupesActe,\n                                    noRecordsText: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\",\n                                    minHeight: 200\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: protocoleModalOpened,\n                onClose: closeProtocoleModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Nouveau protocole\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: protocoleForm.onSubmit(handleProtocoleSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                label: \"Titre du protocole de soins\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                ...protocoleForm.getInputProps('titre')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-4 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 20,\n                                                className: \"text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                fw: 600,\n                                                className: \"text-blue-700\",\n                                                children: \"Groupes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                        size: \"sm\",\n                                        className: \"text-gray-600 mb-2\",\n                                        children: \"Aucun \\xe9l\\xe9ment trouv\\xe9.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeProtocoleModal,\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Enregistrer et quitter\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"blue\",\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 497,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 482,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: acteSoinModalOpened,\n                onClose: closeActeSoinModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Acte de soins\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 535,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: acteSoinForm.onSubmit(handleActeSoinSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                label: \"Titre\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                ...acteSoinForm.getInputProps('titre')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                label: \"Groupe d'acte\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                data: groupesActe.map((g)=>({\n                                        value: g.id,\n                                        label: g.titre\n                                    })),\n                                ...acteSoinForm.getInputProps('groupeActe')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                        label: \"Acte\",\n                                        placeholder: \"\",\n                                        required: true,\n                                        styles: {\n                                            label: {\n                                                color: 'red'\n                                            }\n                                        },\n                                        data: actes.map((a)=>({\n                                                value: a.id,\n                                                label: a.description\n                                            })),\n                                        className: \"flex-1\",\n                                        ...acteSoinForm.getInputProps('acte')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ActionIcon, {\n                                        size: \"lg\",\n                                        className: \"mt-6 bg-blue-500 hover:bg-blue-600\",\n                                        onClick: openActeModal,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                        label: \"Motif\",\n                                        placeholder: \"\",\n                                        required: true,\n                                        styles: {\n                                            label: {\n                                                color: 'red'\n                                            }\n                                        },\n                                        data: [],\n                                        className: \"flex-1\",\n                                        ...acteSoinForm.getInputProps('motif')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-gray-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeActeSoinModal,\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 547,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: groupeActeModalOpened,\n                onClose: closeGroupeActeModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Groupe d'acte\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 11\n                }, void 0),\n                size: \"md\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: groupeActeForm.onSubmit(handleGroupeActeSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                label: \"Titre\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                ...groupeActeForm.getInputProps('titre')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                        size: \"sm\",\n                                        fw: 500,\n                                        mb: 5,\n                                        children: \"Couleur\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_21__.ColorPicker, {\n                                                format: \"hex\",\n                                                ...groupeActeForm.getInputProps('couleur')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeGroupeActeModal,\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 614,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: procedureModalOpened,\n                onClose: closeProcedureModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Proc\\xe9dure\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 669,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: procedureForm.onSubmit(handleProcedureSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Code\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...procedureForm.getInputProps('code')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Nom\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...procedureForm.getInputProps('nom')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 680,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.NumberInput, {\n                                            label: \"Honoraire\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...procedureForm.getInputProps('honoraire')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                            label: \"Services d\\xe9sign\\xe9s\",\n                                            placeholder: \"\",\n                                            data: [],\n                                            ...procedureForm.getInputProps('servicesDesignes')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Divider, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Code NGAP\",\n                                            placeholder: \"\",\n                                            ...procedureForm.getInputProps('codeNGAP')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Code CCAM\",\n                                            placeholder: \"\",\n                                            ...procedureForm.getInputProps('codeCCAM')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"TNR\",\n                                            placeholder: \"\",\n                                            ...procedureForm.getInputProps('tnr')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                                    label: \"Modalit\\xe9\",\n                                                    placeholder: \"\",\n                                                    data: modalites.map((m)=>({\n                                                            value: m.id,\n                                                            label: m.valeur\n                                                        })),\n                                                    className: \"flex-1\",\n                                                    ...procedureForm.getInputProps('modalite')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ActionIcon, {\n                                                    size: \"lg\",\n                                                    className: \"mt-6 bg-blue-500 hover:bg-blue-600\",\n                                                    onClick: openModaliteModal,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.Checkbox, {\n                                label: \"Remboursable\",\n                                ...procedureForm.getInputProps('remboursable', {\n                                    type: 'checkbox'\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeProcedureModal,\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 678,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 663,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: acteModalOpened,\n                onClose: closeActeModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Actes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 792,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 790,\n                    columnNumber: 11\n                }, void 0),\n                size: \"lg\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: acteForm.onSubmit(handleActeSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                            label: \"Code\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...acteForm.getInputProps('code')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_26__.Textarea, {\n                                            label: \"Description\",\n                                            placeholder: \"\",\n                                            required: true,\n                                            styles: {\n                                                label: {\n                                                    color: 'red'\n                                                }\n                                            },\n                                            ...acteForm.getInputProps('description')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 803,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_23__.NumberInput, {\n                                                    label: \"Dur\\xe9e (min)\",\n                                                    placeholder: \"\",\n                                                    min: 1,\n                                                    defaultValue: 15,\n                                                    className: \"flex-1\",\n                                                    ...acteForm.getInputProps('duree')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                            size: \"sm\",\n                                                            children: \"Couleur\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 835,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 826,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                                                    size: \"sm\",\n                                                    children: \"Couleur ray\\xe9e\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 842,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_24__.Divider, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                                    label: \"Agenda par d\\xe9faut\",\n                                                    placeholder: \"\",\n                                                    data: [],\n                                                    className: \"flex-1\",\n                                                    ...acteForm.getInputProps('agendaParDefaut')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_19__.ActionIcon, {\n                                                    size: \"lg\",\n                                                    className: \"mt-6 bg-blue-500 hover:bg-blue-600\",\n                                                    onClick: openProcedureModal,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 853,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_22__.Grid.Col, {\n                                        span: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_18__.Select, {\n                                            label: \"Services d\\xe9sign\\xe9s\",\n                                            placeholder: \"\",\n                                            data: [],\n                                            ...acteForm.getInputProps('servicesDesignes')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                            lineNumber: 871,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 870,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_25__.Checkbox, {\n                                label: \"Couleur sombre\",\n                                ...acteForm.getInputProps('couleurSombre', {\n                                    type: 'checkbox'\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 880,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeActeModal,\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 885,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 802,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 801,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 786,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Modal, {\n                opened: modaliteModalOpened,\n                onClose: closeModaliteModal,\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconColorPicker_IconHeart_IconPlus_IconSearch_IconStethoscope_IconUsers_tabler_icons_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            className: \"text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 903,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_13__.Text, {\n                            fw: 600,\n                            className: \"text-white\",\n                            children: \"Ajouter Modalit\\xe9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                            lineNumber: 904,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 902,\n                    columnNumber: 11\n                }, void 0),\n                size: \"md\",\n                styles: {\n                    header: {\n                        backgroundColor: '#3b82f6',\n                        color: 'white'\n                    },\n                    title: {\n                        color: 'white'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: modaliteForm.onSubmit(handleModaliteSubmit),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_14__.Stack, {\n                        gap: \"md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\n                                label: \"Valeur\",\n                                placeholder: \"\",\n                                required: true,\n                                styles: {\n                                    label: {\n                                        color: 'red'\n                                    }\n                                },\n                                ...modaliteForm.getInputProps('valeur')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_26__.Textarea, {\n                                label: \"Description\",\n                                placeholder: \"\",\n                                rows: 3,\n                                ...modaliteForm.getInputProps('description')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 923,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_16__.Group, {\n                                justify: \"flex-end\",\n                                mt: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        color: \"gray\",\n                                        onClick: closeModaliteModal,\n                                        children: \"Enregistrer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        color: \"red\",\n                                        children: \"Annuler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                                lineNumber: 930,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                        lineNumber: 914,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                    lineNumber: 913,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n                lineNumber: 898,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\svp\\\\frontend\\\\medicine\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\parametrage_du_module_des_specialites\\\\PlanDeSoins.tsx\",\n        lineNumber: 376,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlanDeSoins, \"R1BZ7p/ynvx2jlsTYZnAlxH1ZPc=\", false, function() {\n    return [\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm,\n        _mantine_form__WEBPACK_IMPORTED_MODULE_4__.useForm\n    ];\n});\n_c = PlanDeSoins;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlanDeSoins);\nvar _c;\n$RefreshReg$(_c, \"PlanDeSoins\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(dashboard)/settings/parametrage_du_module_des_specialites/PlanDeSoins.tsx\n"));

/***/ })

});