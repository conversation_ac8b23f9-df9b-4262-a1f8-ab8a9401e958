"use client";
import React from "react";
import { 
  Modal, 
  Text, 
  Group, 
  Switch,  
  Select, 
  Menu, 
  Avatar, 
  TextInput, 
  ActionIcon, 
  Radio, 
  InputBase, 
  ColorPicker, 
  Button, 
  Textarea,
  NumberInput
} from '@mantine/core';
import SimpleBar from "simplebar-react";
import { IconHexagonPlusFilled, IconCheck, IconColorPicker } from '@tabler/icons-react';
import { FaUserDoctor, FaCalendarPlus, FaMicrophoneLines, FaMicrophone } from "react-icons/fa6";
import { RiUserFollowLine } from "react-icons/ri";
import { LiaBirthdayCakeSolid, LiaAddressCardSolid } from "react-icons/lia";
import { TbNumber } from "react-icons/tb";
import { FiPhone } from "react-icons/fi";
import { CiAt } from "react-icons/ci";
import { MdOutlineSocialDistance, MdOutlineContentPasteSearch, MdOutlineBedroomChild } from "react-icons/md";
import { ListPlus } from 'lucide-react';
import { IMaskInput } from 'react-imask';
import { notifications } from '@mantine/notifications';
import moment from "moment";

interface AjouterUnRendezVousProps {
  opened: boolean;
  onClose: () => void;
  appointmentForm: any;
  handleSubmit: (values: any) => void;
  eventTitle: string;
  setEventTitle: (value: string) => void;
  titleOptions: Array<{value: string, label: string}>;
  setTitleOptions: (options: Array<{value: string, label: string}>) => void;
  newTitle: string;
  setNewTitle: (value: string) => void;
  patientName: string;
  setPatientName: (value: string) => void;
  patientlastName: string;
  setPatientlastName: (value: string) => void;
  openListDesPatient: () => void;
  eventDateDeNaissance: string;
  handleDateChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  eventAge: number | null;
  genderOption: string;
  handleOptionChange: (value: string) => void;
  eventEtatCivil: string;
  setEventEtatCivil: (value: string) => void;
  eventCin: string;
  setEventCin: (value: string) => void;
  address: string;
  setAddress: (value: string) => void;
  eventTelephone: string;
  setEventTelephone: (value: string) => void;
  email: string;
  setEmail: (value: string) => void;
  patientdoctor: string;
  setPatientDocteur: (value: string) => void;
  patientsocialSecurity: string;
  setSocialSecurity: (value: string) => void;
  consultationTypes: Array<{value: string, label: string, duration?: string}>;
  setConsultationTypes: (types: Array<{value: string, label: string, duration?: string}>) => void;
  patienttypeConsultation: string;
  setPatientTypeConsultation: (value: string) => void;
  setEventType: (type: any) => void;
  searchValue: string;
  setSearchValue: (value: string) => void;
  dureeDeLexamen: string;
  getEventTypeColor: (type: any) => string;
  newConsultationType: string;
  setNewConsultationType: (value: string) => void;
  newConsultationColor: string;
  setNewConsultationColor: (value: string) => void;
  ColorPickeropened: boolean;
  openedColorPicker: () => void;
  closeColorPicker: () => void;
  changeEndValue: string;
  setChangeEndValue: (value: string) => void;
  setDureeDeLexamen: (value: string) => void;
  eventAganda: string;
  setEventAganda: (value: string) => void;
  agendaTypes: Array<{value: string, label: string}>;
  setAgendaTypes: (types: Array<{value: string, label: string}>) => void;
  newAgendaType: string;
  setNewAgendaType: (value: string) => void;
  isWaitingList: boolean;
  eventDate: string;
  setEventDate: (value: string) => void;
  eventTime: string;
  setEventTime: (value: string) => void;
  eventConsultation: string;
  openListRendezVous: () => void;
  ListRendezVousOpened: boolean;
  closeListRendezVous: () => void;
  patientcomment: string;
  setPatientcomment: (value: string) => void;
  patientnotes: string;
  setPatientNotes: (value: string) => void;
  patientcommentairelistedattente: string;
  setPatientCommentairelistedattente: (value: string) => void;
  eventResourceId: number;
  setEventResourceId: (value: number) => void;
  eventType: string;
  checkedAppelvideo: boolean;
  handleAppelvideoChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  checkedRappelSms: boolean;
  handleRappelSmsChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  checkedRappelEmail: boolean;
  handleRappelEmailChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  currentPatient: any;
  waitingList: any[];
  setWaitingList: (list: any[]) => void;
  setPatientModalOpen: (open: boolean) => void;
  notifications: any;
}

// Composant RendezVousSelector
interface RendezVousSelectorProps {
  onClose: () => void;
}

const RendezVousSelector: React.FC<RendezVousSelectorProps> = ({ onClose }) => {
  const [selectedPeriod, setSelectedPeriod] = React.useState<'15days' | '1month' | '3months'>('15days');
  const [, setStartDate] = React.useState('12/06/2025');
  const [duration, setDuration] = React.useState(30);
  const [numberOfDays, setNumberOfDays] = React.useState(3);
  const [selectedSlots, setSelectedSlots] = React.useState<Set<string>>(new Set());

  // Générer les créneaux horaires
  const generateTimeSlots = () => {
    const slots = [];
    const startHour = 8;
    const endHour = 14;
    
    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const endMinute = minute + 30;
        const endHour = endMinute >= 60 ? hour + 1 : hour;
        const adjustedEndMinute = endMinute >= 60 ? endMinute - 60 : endMinute;
        const endTime = `${endHour.toString().padStart(2, '0')}:${adjustedEndMinute.toString().padStart(2, '0')}`;
        
        slots.push({
          id: `${hour}-${minute}`,
          startTime,
          endTime,
        });
      }
    }
    return slots;
  };

  // Calculer la date selon la période sélectionnée
  const getDateForPeriod = () => {
    switch (selectedPeriod) {
      case '15days':
        return '12 juin 2025';
      case '1month':
        return '26 juin 2025';
      case '3months':
        return '10 juillet 2025';
      default:
        return '12 juin 2025';
    }
  };

  // Calculer la date de début selon la période
  const getStartDateForPeriod = () => {
    switch (selectedPeriod) {
      case '15days':
        return '12/06/2025';
      case '1month':
        return '25/06/2025';
      case '3months':
        return '10/07/2025';
      default:
        return '12/06/2025';
    }
  };

  // Calculer la date formatée selon la période
  const getFormattedDateForPeriod = () => {
    switch (selectedPeriod) {
      case '15days':
        return '12/06/2025';
      case '1month':
        return '26/06/2025';
      case '3months':
        return '10/07/2025';
      default:
        return '12/06/2025';
    }
  };

  const timeSlots = generateTimeSlots();

  const handleSlotToggle = (slotId: string) => {
    const newSelectedSlots = new Set(selectedSlots);
    if (newSelectedSlots.has(slotId)) {
      newSelectedSlots.delete(slotId);
    } else {
      newSelectedSlots.add(slotId);
    }
    setSelectedSlots(newSelectedSlots);
  };

  const isValidateEnabled = selectedSlots.size > 0;

  return (
    <div className="grid grid-cols-12 gap-4">
      <div className="col-span-4">
        <div className="p-4 bg-gray-50">
          <div className="space-y-4">
            <div>
              <Text size="sm" fw={500} mb="xs">À partir de</Text>
              <Select
                value={getStartDateForPeriod()}
                onChange={(value) => setStartDate(value || '')}
                data={[
                  { value: '12/06/2025', label: '12/06/2025' },
                  { value: '25/06/2025', label: '25/06/2025' },
                  { value: '10/07/2025', label: '10/07/2025' },
                  { value: '10/09/2025', label: '10/09/2025' },
                ]}
              />
            </div>

            <div>
              <Text size="sm" fw={500} mb="xs">Durée (min)</Text>
              <NumberInput
                value={duration}
                onChange={(value) => setDuration(Number(value))}
                min={15}
                max={120}
                step={15}
              />
            </div>

            <div>
              <Text size="sm" fw={500} mb="xs">Nbre des jours</Text>
              <NumberInput
                value={numberOfDays}
                onChange={(value) => setNumberOfDays(Number(value))}
                min={1}
                max={30}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="col-span-8">
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <Text size="lg" fw={600}>{getDateForPeriod()}</Text>
            <Text size="sm" color="dimmed">24</Text>
          </div>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {timeSlots.map((slot) => (
              <div key={slot.id} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                <div className="flex items-center space-x-2">
                  <Text size="sm">le</Text>
                  <Text size="sm" color="blue" fw={500}>
                    {getFormattedDateForPeriod()}
                  </Text>
                  <Text size="sm">de</Text>
                  <Text size="sm" color="red" fw={500}>
                    {slot.startTime}
                  </Text>
                  <Text size="sm">à</Text>
                  <Text size="sm" color="green" fw={500}>
                    {slot.endTime}
                  </Text>
                </div>
                <input
                  type="checkbox"
                  checked={selectedSlots.has(slot.id)}
                  onChange={() => handleSlotToggle(slot.id)}
                  className="form-checkbox h-4 w-4 text-blue-600"
                />
              </div>
            ))}
          </div>

          <div className="flex justify-between items-center mt-6 pt-4 border-t">
            <div className="flex space-x-4">
              <Button
                variant={selectedPeriod === '15days' ? 'filled' : 'outline'}
                onClick={() => setSelectedPeriod('15days')}
                size="sm"
              >
                15 jours
              </Button>
              <Button
                variant={selectedPeriod === '1month' ? 'filled' : 'outline'}
                onClick={() => setSelectedPeriod('1month')}
                size="sm"
              >
                1 Mois
              </Button>
              <Button
                variant={selectedPeriod === '3months' ? 'filled' : 'outline'}
                onClick={() => setSelectedPeriod('3months')}
                size="sm"
              >
                3 Mois
              </Button>
            </div>

            <div className="flex space-x-2">
              <Button
                color={isValidateEnabled ? 'blue' : 'gray'}
                disabled={!isValidateEnabled}
                onClick={() => {
                  // Logique de validation ici
                  onClose();
                }}
              >
                Valider
              </Button>
              <Button
                variant="outline"
                color="red"
                onClick={onClose}
              >
                Annuler
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const AjouterUnRendezVous: React.FC<AjouterUnRendezVousProps> = (props) => {
  const {
    opened,
    onClose,
    appointmentForm,
    handleSubmit,
    eventTitle,
    setEventTitle,
    titleOptions,
    setTitleOptions,
    newTitle,
    setNewTitle,
    patientName,
    setPatientName,
    patientlastName,
    setPatientlastName,
    openListDesPatient,
    eventDateDeNaissance,
    handleDateChange,
    eventAge,
    genderOption,
    handleOptionChange,
    eventEtatCivil,
    setEventEtatCivil,
    eventCin,
    setEventCin,
    address,
    setAddress,
    eventTelephone,
    setEventTelephone,
    email,
    setEmail,
    patientdoctor,
    setPatientDocteur,
    patientsocialSecurity,
    setSocialSecurity,
    consultationTypes,
    setConsultationTypes,
    patienttypeConsultation,
    setPatientTypeConsultation,
    setEventType,
    searchValue,
    setSearchValue,
    dureeDeLexamen,
    getEventTypeColor,
    newConsultationType,
    setNewConsultationType,
    newConsultationColor,
    setNewConsultationColor,
    ColorPickeropened,
    openedColorPicker,
    closeColorPicker,
    changeEndValue,
    setChangeEndValue,
    setDureeDeLexamen,
    eventAganda,
    setEventAganda,
    agendaTypes,
    setAgendaTypes,
    newAgendaType,
    setNewAgendaType,
    isWaitingList,
    eventDate,
    setEventDate,
    eventTime,
    setEventTime,
    eventConsultation,
    openListRendezVous,
    ListRendezVousOpened,
    closeListRendezVous,
    patientcomment,
    setPatientcomment,
    patientnotes,
    setPatientNotes,
    patientcommentairelistedattente,
    setPatientCommentairelistedattente,
    eventResourceId,
    setEventResourceId,
    eventType,
    checkedAppelvideo,
    handleAppelvideoChange,
    checkedRappelSms,
    handleRappelSmsChange,
    checkedRappelEmail,
    handleRappelEmailChange,
    currentPatient,
    waitingList,
    setWaitingList,
    setPatientModalOpen,
  } = props;

  return (
    <Modal.Root
      opened={opened}
      onClose={onClose}
      size="70%"
    >
      <Modal.Overlay />
      <Modal.Content className="overflow-y-hidden">
        <Modal.Header style={{ height: '60px', background: "#3799CE", padding: "11px" }}>
          <Modal.Title>
            <Text fw={600} c="var(--mantine-color-white)" className="mb-2 flex gap-2 text-lg font-semibold leading-none tracking-tight">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24"
              >
                <g
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                >
                  <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5M16 2v4M8 2v4m-5 4h5m9.5 7.5L16 16.3V14"></path>
                  <circle cx={16} cy={16} r={6}></circle>
                </g>
              </svg>
              Ajouter un rendez-vous
            </Text>
            <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
              Remplissez les détails ci-dessous pour ajouter un nouvel événement.
            </p>
          </Modal.Title>
          <Group justify="flex-end">
            <Switch
              defaultChecked
              color="teal"
              size="xs"
            />
            <p className="text-muted-foreground text-sm text-[var(--mantine-color-white)]">
              Pause
            </p>
            <Modal.CloseButton className="mantine-focus-always" style={{ color: "white" }} />
          </Group>
        </Modal.Header>
        
        <Modal.Body style={{ padding: '0px' }}>
          <div className="py-2 pl-4 h-[600px]">
            <SimpleBar className="simplebar-scrollable-y h-[calc(100%)]">
              <div className="pr-4">
                <form onSubmit={(e) => { e.preventDefault(); handleSubmit(appointmentForm.values); }}>
                  <div className="grid gap-3 py-2 pr-4">
                    {/* Titre, Nom, Prénom */}
                    <div className="flex gap-4 mb-2">
                      <Select
                        value={eventTitle}
                        onChange={(value) => setEventTitle(value ?? "")}
                        placeholder="Titre"
                        data={titleOptions}
                        className="w-full"
                        leftSection={<FaUserDoctor size={16} />}
                      />
                      <Menu width={200} shadow="md" closeOnItemClick={false}>
                        <Menu.Target>
                          <Avatar color="#4BA3D3" radius="sm" h={36}>
                            <IconHexagonPlusFilled size={20} />
                          </Avatar>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <TextInput
                            leftSectionPointerEvents="none"
                            leftSection={<FaUserDoctor size={16} />}
                            placeholder="Ajouter des titres"
                            value={newTitle}
                            onChange={(e) => setNewTitle(e.target.value)}
                            rightSection={
                              <ActionIcon
                                size="sm"
                                onClick={() => {
                                  if (newTitle.trim()) {
                                    const newTitleOption = { value: newTitle, label: newTitle };
                                    setTitleOptions([...titleOptions, newTitleOption]);
                                    setEventTitle(newTitle);
                                    setNewTitle("");
                                    notifications.show({
                                      title: 'Titre ajouté',
                                      message: `"${newTitle}" a été ajouté à la liste des titres`,
                                      color: 'green',
                                      autoClose: 2000
                                    });
                                  }
                                }}
                                disabled={!newTitle.trim()}
                              >
                                <FaCalendarPlus size={16} />
                              </ActionIcon>
                            }
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && newTitle.trim()) {
                                const newTitleOption = { value: newTitle, label: newTitle };
                                setTitleOptions([...titleOptions, newTitleOption]);
                                setEventTitle(newTitle);
                                setNewTitle("");
                                notifications.show({
                                  title: 'Titre ajouté',
                                  message: `"${newTitle}" a été ajouté à la liste des titres`,
                                  color: 'green',
                                  autoClose: 2000
                                });
                              }
                            }}
                          />
                        </Menu.Dropdown>
                      </Menu>
                      <TextInput
                        id="event-nom"
                        placeholder="Nom *"
                        type="text"
                        value={patientName}
                        onChange={(e) => setPatientName(e.target.value)}
                        required
                        className="input input-bordered w-full"
                        leftSection={<RiUserFollowLine size={16} />}
                      />
                      <TextInput
                        id="event-prenom"
                        placeholder="Prénom *"
                        type="text"
                        value={patientlastName}
                        onChange={(e) => setPatientlastName(e.target.value)}
                        required
                        className="input input-bordered w-full"
                        leftSection={<RiUserFollowLine size={16} />}
                      />
                      <Avatar color="#4BA3D3" radius="sm" h={36} onClick={openListDesPatient}>
                        <MdOutlineContentPasteSearch size={20} />
                      </Avatar>
                    </div>

                    {/* Date de naissance, âge, sexe */}
                    <div className="flex gap-4 mb-2">
                      <TextInput
                        type="date"
                        placeholder="Date de Naissance..."
                        id="event-dateDeNaissance"
                        value={eventDateDeNaissance}
                        onChange={handleDateChange}
                        required
                        className="input input-bordered max-w-[278px] w-full"
                      />
                      <TextInput
                        type="text"
                        id="event-age"
                        value={eventAge?.toString() ?? ""}
                        placeholder={
                          eventAge !== null
                            ? eventAge.toString()
                            : "Veuillez entrer votre date de naissance"
                        }
                        readOnly
                        className="input input-bordered max-w-[278px] w-full"
                        leftSection={<LiaBirthdayCakeSolid size={16} />}
                      />
                      <div className="flex items-center space-x-2">
                        <Radio.Group
                          value={genderOption}
                          onChange={handleOptionChange}
                        >
                          <div className="flex items-center space-x-4">
                            <Radio key="homme" value="Homme" label="Homme" />
                            <Radio key="femme" value="Femme" label="Femme" />
                            <Radio key="enfant" value="Enfant" label="Enfant" />
                          </div>
                        </Radio.Group>
                      </div>
                    </div>

                    {/* État civil, CIN, Adresse */}
                    <div className="flex gap-4">
                      <Select
                        value={eventEtatCivil}
                        onChange={(value) => setEventEtatCivil(value ?? "")}
                        placeholder="État civil"
                        data={[
                          { value: "Célibataire", label: "Célibataire" },
                          { value: "Marié(e)", label: "Marié(e)" },
                          { value: "Divorcé(e)", label: "Divorcé(e)" },
                          { value: "Veuf(ve)", label: "Veuf(ve)" },
                          { value: "Autre chose", label: "Autre chose" },
                        ]}
                        className="select w-full max-w-xs"
                      />
                      <TextInput
                        placeholder="CIN"
                        disabled={genderOption === 'Enfant'}
                        value={eventCin}
                        onChange={(e) => setEventCin(e.target.value)}
                        styles={{
                          input: {
                            backgroundColor: genderOption === 'Enfant' ? '#f5f5f5' : undefined,
                            color: genderOption === 'Enfant' ? '#999' : undefined
                          }
                        }}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<TbNumber size={16} />}
                      />
                      <TextInput
                        id="Adresse"
                        placeholder="Adressé par"
                        value={address}
                        onChange={(e) => setAddress(e.target.value)}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<LiaAddressCardSolid size={16} />}
                      />
                    </div>

                    {/* Téléphone, Email */}
                    <div className="flex gap-4">
                      <InputBase
                        id="Téléphone"
                        component={IMaskInput}
                        mask="00-00-00-00-00"
                        placeholder="Téléphone"
                        value={eventTelephone}
                        onAccept={(value) => setEventTelephone(value)}
                        unmask={true}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<FiPhone size={16} />}
                      />
                      <TextInput
                        id="Email"
                        placeholder="Email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="input input-bordered mb-2 w-full"
                        leftSection={<CiAt size={16} />}
                      />
                    </div>

                    {/* Docteur, Sécurité sociale */}
                    <div className="flex gap-4">
                      <Select
                        value={patientdoctor}
                        onChange={(value) => setPatientDocteur(value ?? "")}
                        placeholder="Docteur"
                        data={[
                          { value: "Docteur", label: "Docteur" },
                          { value: "dr.Kader", label: "dr.Kader" },
                          { value: "dr.Kaders", label: "dr.Kaders" },
                        ]}
                        className="w-full"
                        leftSection={<FaUserDoctor size={16} />}
                      />
                      <Select
                        value={patientsocialSecurity || 'Aucune'}
                        onChange={(value) => setSocialSecurity(value || 'Aucune')}
                        placeholder="Sécurité sociale"
                        data={[
                          { value: "Aucune", label: "Aucune" },
                          { value: "CNSS", label: "CNSS" },
                          { value: "AMO", label: "AMO" },
                        ]}
                        className="w-full"
                        leftSection={<MdOutlineSocialDistance size={16} />}
                      />
                    </div>

                    {/* Type de consultation */}
                    <div className="flex gap-4 mb-2">
                      <Select
                        label="Type de consultation"
                        placeholder="Rechercher ou saisir..."
                        data={consultationTypes}
                        value={patienttypeConsultation}
                        onChange={(value) => {
                          setPatientTypeConsultation(value ?? "");
                          const selectedLabel = [
                            { value: "Visite de malade", eventType: "visit" },
                            { value: "Visitor Counter", eventType: "visitor-counter" },
                            { value: "Completed", eventType: "completed" },
                          ].find(item => item.value === value);

                          if (selectedLabel) {
                            setEventType(selectedLabel.eventType);
                          }
                        }}
                        searchable
                        searchValue={searchValue}
                        onSearchChange={setSearchValue}
                        clearable
                        maxDropdownHeight={280}
                        rightSectionWidth={70}
                        required
                        rightSection={
                          <span className="bg-[#4CAF50] text-white px-2 py-1 rounded text-xs">{dureeDeLexamen}</span>
                        }
                        allowDeselect
                        className="w-full"
                      />
                      <Menu width={260} shadow="md" closeOnItemClick={false}>
                        <Menu.Target>
                          <Avatar color="#4BA3D3" radius="sm" h={36} mt={"24"}>
                            <IconHexagonPlusFilled size={20} />
                          </Avatar>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <div className="flex">
                            <TextInput
                              leftSectionPointerEvents="none"
                              leftSection={<FaUserDoctor size={16} />}
                              placeholder="Ajouter des Consultation"
                              value={newConsultationType}
                              onChange={(e) => setNewConsultationType(e.target.value)}
                              rightSection={
                                <ActionIcon
                                  size="sm"
                                  onClick={() => {
                                    if (newConsultationType.trim()) {
                                      const newType = {
                                        value: newConsultationType,
                                        label: newConsultationType,
                                        duration: dureeDeLexamen || "15 min"
                                      };
                                      setConsultationTypes([...consultationTypes, newType]);
                                      setPatientTypeConsultation(newConsultationType);
                                      setNewConsultationType("");
                                      notifications.show({
                                        title: 'Type de consultation ajouté',
                                        message: `"${newConsultationType}" a été ajouté`,
                                        color: 'green',
                                        autoClose: 2000
                                      });
                                    }
                                  }}
                                  disabled={!newConsultationType.trim()}
                                >
                                  <FaCalendarPlus size={16} />
                                </ActionIcon>
                              }
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && newConsultationType.trim()) {
                                  const newType = {
                                    value: newConsultationType,
                                    label: newConsultationType,
                                    duration: dureeDeLexamen || "15 min"
                                  };
                                  setConsultationTypes([...consultationTypes, newType]);
                                  setPatientTypeConsultation(newConsultationType);
                                  setNewConsultationType("");
                                  notifications.show({
                                    title: 'Type de consultation ajouté',
                                    message: `"${newConsultationType}" a été ajouté`,
                                    color: 'green',
                                    autoClose: 2000
                                  });
                                }
                              }}
                            />
                            <Avatar
                              color={newConsultationColor}
                              radius="sm"
                              ml={4}
                              h={36}
                              onClick={openedColorPicker}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 200 200"
                                style={{width: "26px", height:"26px"}}
                              >
                                <path fill="#FF5178" d="M100 0a100 100 0 00-50 13.398l30 51.961A40 40 0 01100 60V0z"></path>
                              </svg>
                            </Avatar>
                          </div>
                        </Menu.Dropdown>
                      </Menu>
                      <Modal opened={ColorPickeropened} onClose={closeColorPicker} size="auto" yOffset="18vh" xOffset={30} withCloseButton={false}>
                        <ColorPicker
                          defaultValue={newConsultationColor}
                          value={newConsultationColor}
                          onChange={setNewConsultationColor}
                          onChangeEnd={setChangeEndValue}
                          format="hex"
                          swatches={['#2e2e2e', '#868e96', '#fa5252', '#e64980', '#be4bdb', '#7950f2', '#4c6ef5', '#228be6', '#15aabf', '#12b886', '#40c057', '#82c91e', '#fab005', '#fd7e14']}
                        />
                        <Group justify="center" mt={8}>
                          <Button
                            variant="filled"
                            w={"100%"}
                            color={`${newConsultationColor}`}
                            leftSection={<IconColorPicker stroke={1} size={18} />}
                            onClick={() => {
                              setNewConsultationColor(changeEndValue);
                              closeColorPicker();
                            }}
                          >
                            Sélectionner cette couleur
                          </Button>
                        </Group>
                      </Modal>

                      <Select
                        label="Durée"
                        value={dureeDeLexamen}
                        onChange={(value) => setDureeDeLexamen(value ?? "")}
                        placeholder="15 min"
                        data={["10 min", "15 min", "20 min","25 min","30 min", "35 min" ,"40 min","45 min"]}
                        className="select w-full max-w-xs"
                      />
                      <Select
                        label="Agenda"
                        value={eventAganda}
                        onChange={(value) => setEventAganda(value ?? "")}
                        placeholder="Ajouter des Agenda"
                        data={agendaTypes}
                        className="w-full"
                        leftSection={<FaUserDoctor size={16} />}
                      />
                      <Menu width={200} shadow="md" closeOnItemClick={false}>
                        <Menu.Target>
                          <Avatar color="#4BA3D3" radius="sm" h={36} mt={"24"}>
                            <IconHexagonPlusFilled size={20} />
                          </Avatar>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <TextInput
                            leftSectionPointerEvents="none"
                            leftSection={<FaUserDoctor size={16} />}
                            placeholder="Ajouter des Agenda"
                            value={newAgendaType}
                            onChange={(e) => setNewAgendaType(e.target.value)}
                            rightSection={
                              <ActionIcon
                                size="sm"
                                onClick={() => {
                                  if (newAgendaType.trim()) {
                                    const newAgendaOption = { value: newAgendaType, label: newAgendaType };
                                    setAgendaTypes([...agendaTypes, newAgendaOption]);
                                    setEventAganda(newAgendaType);
                                    setNewAgendaType("");
                                    notifications.show({
                                      title: 'Agenda ajouté',
                                      message: `"${newAgendaType}" a été ajouté à la liste des agendas`,
                                      color: 'green',
                                      autoClose: 2000
                                    });
                                  }
                                }}
                                disabled={!newAgendaType.trim()}
                              >
                                <FaCalendarPlus size={16} />
                              </ActionIcon>
                            }
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && newAgendaType.trim()) {
                                const newAgendaOption = { value: newAgendaType, label: newAgendaType };
                                setAgendaTypes([...agendaTypes, newAgendaOption]);
                                setEventAganda(newAgendaType);
                                setNewAgendaType("");
                                notifications.show({
                                  title: 'Agenda ajouté',
                                  message: `"${newAgendaType}" a été ajouté à la liste des agendas`,
                                  color: 'green',
                                  autoClose: 2000
                                });
                              }
                            }}
                          />
                        </Menu.Dropdown>
                      </Menu>
                    </div>

                    {/* Date et heure du RDV */}
                    {!isWaitingList && !appointmentForm.values.addToWaitingList && (
                      <div className="mx-auto flex gap-4 mb-2">
                        <Text size="12px" className="label" style={{marginTop: "10px"}}>Date du RDV</Text>
                        <TextInput
                          id="event-date"
                          type="date"
                          value={eventDate}
                          onChange={(e) => setEventDate(e.target.value)}
                          className="input input-bordered mb-2 w-64 max-w-64"
                        />
                        <Text size="12px" className="label" style={{marginTop: "10px"}}>De*</Text>
                        <TextInput
                          id="event-time"
                          type="time"
                          value={eventTime}
                          onChange={(e) => setEventTime(e.target.value)}
                          className="input input-bordered mb-2 w-64 max-w-64"
                        />
                        <Text size="12px" className="label" style={{marginTop: "10px"}}>à*</Text>
                        <TextInput
                          id="event-time-end"
                          type="text"
                          placeholder={
                            eventTime !== null
                              ? moment(eventTime, "HH:mm")
                                  .add(parseInt(eventConsultation), "minutes")
                                  .format("HH:mm")
                              : "Please enter your date of birth"
                          }
                          readOnly
                          className="input input-bordered mb-2 w-40"
                        />
                        <Avatar color="#4BA3D3" radius="sm" h={36}>
                          <ListPlus size={30} className="text-[#3799CE] cursor-pointer"
                            onClick={openListRendezVous}
                          />
                        </Avatar>

                        <Modal
                          opened={ListRendezVousOpened}
                          onClose={closeListRendezVous}
                          size="xl"
                          centered
                          withCloseButton={false}
                        >
                          <RendezVousSelector onClose={closeListRendezVous} />
                        </Modal>
                      </div>
                    )}

                    {/* Commentaires */}
                    <div className="flex gap-4 mb-2 -mt-2 pr-4">
                      <Textarea
                        id="event-Commentaire"
                        value={patientcomment}
                        onChange={(event) => setPatientcomment(event.currentTarget.value ?? "")}
                        placeholder="Commentaire ..."
                        className="w-full"
                        rightSection={<FaMicrophoneLines size={18} />}
                      />
                      <Textarea
                        id="event-Notes"
                        value={patientnotes}
                        onChange={(event) => setPatientNotes(event.currentTarget.value ?? "")}
                        placeholder="Notes ..."
                        className="w-full"
                        rightSection={<FaMicrophoneLines size={18} />}
                      />
                      <Textarea
                        id="event-Commentairelistedattente"
                        value={patientcommentairelistedattente}
                        onChange={(event) => setPatientCommentairelistedattente(event.currentTarget.value ?? "")}
                        placeholder="Commentaire (liste d'attente)..."
                        className="w-full"
                        rightSection={<FaMicrophone size={18} />}
                      />
                    </div>

                    {/* Room et types d'événements */}
                    <div className="bg-base-100 px-[4px] pt-[8px]">
                      <ul className="text-daisy flex flex-wrap gap-x-4 gap-y-2">
                        <div className="flex items-center space-x-1">
                          <Select
                            value={eventResourceId ? eventResourceId.toString() : ""}
                            onChange={(value) => {
                              setEventResourceId(Number(value) || 1);
                            }}
                            name="resourceId"
                            placeholder="Room"
                            data={[
                              { value: "1", label: "Room A" },
                              { value: "2", label: "Room B" },
                            ]}
                            required
                            className="select w-full max-w-xs"
                            leftSection={<MdOutlineBedroomChild size={16} />}
                          />
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="visit"
                            type="radio"
                            name="eventType"
                            value="visit"
                            className="peer hidden"
                            checked={eventType === "visit"}
                            onChange={(e) => setEventType(e.target.value)}
                          />
                          <label
                            htmlFor="visit"
                            className={`${
                              eventType === "visit"
                                ? "peer-checked:text-[#34D1BF]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-teal relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Visite de malade
                            </li>
                          </label>
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="visitor-counter"
                            type="radio"
                            name="eventType"
                            value="visitor-counter"
                            className="peer hidden"
                            checked={eventType === "visitor-counter"}
                            onChange={(e) => setEventType(e.target.value)}
                          />
                          <label
                            htmlFor="visitor-counter"
                            className={`${
                              eventType === "visitor-counter"
                                ? "peer-checked:text-[#F17105]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-orange relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Visitor Counter
                            </li>
                          </label>
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="completed"
                            type="radio"
                            name="eventType"
                            value="completed"
                            className="peer hidden"
                            checked={eventType === "completed"}
                            onChange={(e) => setEventType(e.target.value)}
                          />
                          <label
                            htmlFor="completed"
                            className={`${
                              eventType === "completed"
                                ? "peer-checked:text-[#3799CE]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-azure relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Completed
                            </li>
                          </label>
                        </div>

                        <div className="flex items-center space-x-1">
                          <input
                            id="diagnosis"
                            type="radio"
                            name="eventType"
                            value="diagnosis"
                            checked={eventType === "diagnosis"}
                            className="peer hidden"
                            onChange={(e) => setEventType(e.target.value)}
                          />
                          <label
                            htmlFor="diagnosis"
                            className={`${
                              eventType === "diagnosis"
                                ? "peer-checked:text-[#F3124E]"
                                : "text-[var(--mantine-color-dark-0)]"
                            } inline-flex h-10 w-full items-center justify-center whitespace-nowrap px-4 py-2 text-sm font-medium uppercase`}
                          >
                            <li className="flex items-center gap-2 text-xs uppercase">
                              <span className="disk-red relative z-10 block h-[12px] w-[12px] rounded-full border-2 border-white" />
                              Re-diagnose
                            </li>
                          </label>
                        </div>
                      </ul>
                    </div>

                    {/* Switches et boutons */}
                    <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 pr-4">
                      <Group gap="xs">
                        <Switch
                          color="teal"
                          size="xs"
                          label="Add to Waiting List"
                          checked={appointmentForm.values.addToWaitingList}
                          onChange={(event) => {
                            appointmentForm.setFieldValue('addToWaitingList', event.currentTarget.checked);
                            appointmentForm.setFieldValue('removeFromCalendar', event.currentTarget.checked);
                          }}
                          thumbIcon={
                            appointmentForm.values.addToWaitingList ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />

                        <Switch
                          checked={checkedAppelvideo}
                          onChange={handleAppelvideoChange}
                          color="teal"
                          size="xs"
                          label="Appel video"
                          thumbIcon={
                            checkedAppelvideo ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />
                        <Switch
                          checked={checkedRappelSms}
                          onChange={handleRappelSmsChange}
                          color="teal"
                          size="xs"
                          label="Rappel Sms"
                          thumbIcon={
                            checkedRappelSms ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />
                        <Switch
                          checked={checkedRappelEmail}
                          onChange={handleRappelEmailChange}
                          color="teal"
                          size="xs"
                          label="Rappel e-mail"
                          thumbIcon={
                            checkedRappelEmail ? (
                              <IconCheck size={12} color="var(--mantine-color-teal-6)" stroke={3} />
                            ) : (
                              null
                            )
                          }
                        />
                      </Group>

                      <Button
                        type="submit"
                        className="btn mb-2 bg-[#03A684] text-[var(--mantine-Button-label-MB)] hover:bg-[#03A684]/90"
                        onClick={() => {
                          onClose();
                        }}
                      >
                        {currentPatient ? "Enregistrer" : "Ajouter"}
                      </Button>
                      {currentPatient && (
                        <Button
                          color="red"
                          onClick={() => {
                            if (currentPatient) {
                              setWaitingList(waitingList.filter(p => p.id !== currentPatient.id));
                              setPatientModalOpen(false);
                            }
                          }}
                          className="btn mb-2 bg-[#F3124E] text-[var(--mantine-Button-label-MB)] hover:bg-[#F3124E]/90"
                        >
                          Supprimer
                        </Button>
                      )}
                      <Button
                        onClick={() => {
                          onClose();
                        }}
                        className="btn mb-2 bg-[#F5A524] text-[var(--mantine-Button-label-MB)] hover:bg-[#F5A524]/90"
                      >
                        Annuler
                      </Button>
                    </div>
                  </div>
                </form>
              </div>
            </SimpleBar>
          </div>
        </Modal.Body>
      </Modal.Content>
    </Modal.Root>
  );
};

export default AjouterUnRendezVous;
